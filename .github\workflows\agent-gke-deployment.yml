name: Client Build
on:
  push:
    branches:
      - develop
  # pull_request:
#   branches:
#     - develop

permissions:
  contents: write
  id-token: write
jobs:
  build:
    name: Sonarqube test
    runs-on: ubuntu-latest
    steps:
      # Step 1: Checkout the code
      - name: checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          tags: true
      # Step 2: Set Build Number
      - name: Set Build Number
        id: build_number
        run: echo "BUILD_NUMBER=${{ github.run_number }}" >> $GITHUB_ENV
      # # Step 3: Set up the required language runtime
      # - name: Set up Node.js
      #   uses: actions/setup-node@v3
      #   with:
      #     node-version: 18
      # # Step 4: Install dependencies
      # - name: Install Dependencies
      #   run: npm install --legacy-peer-deps
      # # Step 5: Run unit tests and generate coverage report
      # - name: Run Unit Tests
      #   run: npm test -- --coverage
      # # Step 6: Upload coverage report as an artifact
      # - name: Upload Coverage Report
      #   uses: actions/upload-artifact@v4
      #   with:
      #     name: coverage-report
      #     path: coverage/
      # # Step 7: Run SonarQube analysis
      - uses: sonarsource/sonarqube-scan-action@v4
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
          SONAR_SCANNER_OPTS: |
            -Dsonar.projectKey=${{ secrets.SONAR_PROJECT_KEY }}
            -Dsonar.projectVersion=${{ env.BUILD_NUMBER }}
            -Dsonar.login=${{ secrets.SONAR_TOKEN }}
            -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info
      # Step 8: Authenticate to GCP
      - name: Authenticate to GCP
        uses: google-github-actions/auth@v2
        with:
          token_format: 'access_token'
          workload_identity_provider: ${{ secrets.GCP_WORKLOAD_IDENTITY_PROVIDER }}
          service_account: ${{ secrets.GCP_SERVICE_ACCOUNT_EMAIL }}
      - name: Configure Docker for Artifact Registry
        # Step 9: Configure Docker

        run: gcloud auth configure-docker us-central1-docker.pkg.dev
      - name: Determine Version for Develop
        # Step 10: Get Latest Tag and Determine New Version

        id: versioning
        run: |
          BRANCH_NAME="develop"  # Updated to "develop"

          # Get the latest Develop version tag (fallback to v0.0.0)
          latest=$(git tag --list "${BRANCH_NAME}-v*" --sort=-v:refname | head -n 1)
          if [[ -z "$latest" ]]; then
            latest="${BRANCH_NAME}-v0.0.0"
          fi
          latest=${latest#"${BRANCH_NAME}-v"}  # Remove "develop-" prefix

          # Parse major, minor, patch
          IFS='.' read -r major minor patch <<< "$latest"
          major=${major:-0}
          minor=${minor:-0}
          patch=${patch:-0}

          # Get latest tag in repository
          last_tag=$(git describe --tags --abbrev=0 2>/dev/null || echo "")
          echo "Last tag detected: $last_tag"

          # Get commit messages since the last tag
          commits=$(git log --oneline --no-merges "$last_tag"..HEAD --pretty=format:"%s" || echo "")

          echo "Commits since last tag:"
          echo "$commits"

          # Determine version bump
          if echo "$commits" | grep -Eiq "^breaking change"; then
              major=$((major + 1))
              minor=0
              patch=0
          elif echo "$commits" | grep -Eiq "^feat:"; then
              minor=$((minor + 1))
              patch=0
          elif echo "$commits" | grep -Eiq "^fix:"; then
              patch=$((patch + 1))
          else
              patch=$((patch + 1))  # Default to patch increment if no specific commit pattern is found
          fi

          # Construct new version
          version_only="v$major.$minor.$patch"
          full_tag="${BRANCH_NAME}-${version_only}"

          echo "New Version (Docker): $version_only"
          echo "New Tag (Git): $full_tag"

          # Save to GitHub Actions environment
          echo "version_only=$version_only" >> $GITHUB_ENV
          echo "full_tag=$full_tag" >> $GITHUB_ENV
      - name: Create .env.production from secrets
        # Step 11: Create .env.production

        run: |
          echo "NEXT_PUBLIC_ENV=${{ secrets.NEXT_PUBLIC_ENV }}" >> .env.production
          echo "NEXT_PUBLIC_API_URL_GRAPHQL=${{ secrets.NEXT_PUBLIC_API_URL_GRAPHQL }}" >> .env.production
      - name: Build and Push Docker Image
        # Step 12: Build and push Docker image

        run: |
          IMAGE_PATH=us-central1-docker.pkg.dev/${{ secrets.GCP_PROJECT_ID }}/${{ secrets.GCP_ARTIFACT_REGISTRY_DEV }}/agent

          # Build with version tag
          docker build -t $IMAGE_PATH:${{ env.version_only }} .

          # Tag as latest
          docker tag $IMAGE_PATH:${{ env.version_only }} $IMAGE_PATH:latest

          # Push both tags
          docker push $IMAGE_PATH:${{ env.version_only }}
          docker push $IMAGE_PATH:latest
      - name: Create and Push Git Tag
        # Step 13: Create and Push Git Tag

        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          tag_name=${{ env.full_tag }}

          # Check if tag already exists remotely
          if git ls-remote --tags origin | grep -q "refs/tags/$tag_name"; then
              echo "Tag $tag_name already exists. Skipping tag creation."
              exit 0
          fi

          # Create and push the new tag
          git tag "$tag_name"
          git push origin "$tag_name"
      - name: Update image version in Kubemanifest repo
        # Step 14: Update image version in Kubemanifest repo

        run: |
          git config --global user.email "${{ secrets.HUB_USER_EMAIL }}"
          git config --global user.name "${{ secrets.HUB_USERNAME }}"

          git clone --branch develop https://x-access-token:${{ secrets.HUB_TOKEN }}@github.com/ASPRCM-SOLUTIONS-PVT-LTD/Kubemanifest.git

          cd Kubemanifest/overlays/dev/patch

          IMAGE_PATH=us-central1-docker.pkg.dev/asp-rcm/asprcm-dev-frontend-agent-repo/agent

          # More accurate sed for raw patch-agent YAML
          sed -i "s|image: .*agent:.*|image: ${IMAGE_PATH}:${{ env.version_only }}|g" patch-agent.yml

          if ! git diff --quiet patch-agent.yml; then
            git add patch-agent.yml
            git commit -m "ci(agent): bump image version to ${{ env.version_only }}"
            git push origin develop
          else
            echo "Image version is already up to date."
          fi
      - name: Get GKE Credentials
        # Step 15: Authenticate to GKE

        uses: google-github-actions/get-gke-credentials@v2
        with:
          cluster_name: ${{ secrets.GKE_CLUSTER_NAME }}
          location: ${{ secrets.GKE_CLUSTER_REGION }}
          project_id: ${{ secrets.GCP_PROJECT_ID }}
      - name: Apply updated Kubernetes manifests
        # Step 16: Apply updated manifest to GKE

        run: |-
          cd Kubemanifest/overlays/dev
          kubectl apply -k .
