# Stage 1: Build the application
FROM node:18-alpine AS build
 
WORKDIR /app
 
# Copy only package.json and package-lock.json to leverage Docker cache
COPY package*.json ./
 
RUN npm i
 
# Copy the rest of the application source code
COPY . .

COPY .env.production .env
 
# Run the local build step
RUN npm run build
 
# Stage 2: Set up the production environment
FROM node:18-alpine
 
WORKDIR /app
 
# Copy only the necessary files from the build stage
COPY --from=build /app /app
 
# Expose the application port
EXPOSE 3001
 
# Start the application using the correct command
CMD ["npm", "run", "start"]
 