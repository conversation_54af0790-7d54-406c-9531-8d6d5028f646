import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

const protectedRoutes = ["/dashboard"];

export function middleware(request: NextRequest) {
  const token = request.cookies.get("token")?.value || null;
  const isAssigned = request.cookies.get("isAssigned")?.value === 'true';
console.log('isAssigned',isAssigned);

  const { pathname } = request.nextUrl;

  const isProtected = protectedRoutes.some(
    (route) => pathname === route || pathname.startsWith(`${route}/`)
  );

  // Redirect unauthenticated or unassigned users trying to access protected routes
  if (!token && !isAssigned &&isProtected ) {
     return NextResponse.redirect(new URL("/", request.url));
  }

  // Redirect logged-in and assigned users away from login page
  if (token && isAssigned ) {
    return NextResponse.redirect(new URL('/tickets', request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: ["/", "/dashboard",'/tickets'],
};
