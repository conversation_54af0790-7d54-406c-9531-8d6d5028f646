{"name": "rcm-agent", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "dev:prod": "dotenv -e .env.production -- next dev --turbopack -p 3001", "build": "next build", "start": "dotenv -e .env.development -- next start -p 3001", "start:prod": "dotenv -e .env.production -- next start -p 3001", "lint": "next lint", "test": "jest --config"}, "dependencies": {"@apollo/client": "^3.13.8", "@azure/msal-browser": "^4.12.0", "@azure/msal-react": "^3.0.12", "@ckeditor/ckeditor5-build-classic": "^44.3.0", "@ckeditor/ckeditor5-react": "^11.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hello-pangea/dnd": "^18.0.1", "@mui/icons-material": "^7.1.0", "@mui/lab": "^7.0.0-beta.14", "@mui/material": "^7.1.0", "@mui/x-data-grid": "^8.3.1", "@mui/x-date-pickers": "^8.5.2", "@mui/x-date-pickers-pro": "^8.5.2", "@radix-ui/react-dialog": "^1.1.14", "@reduxjs/toolkit": "^2.8.1", "@tinymce/tinymce-react": "^6.2.1", "@tiptap/extension-color": "^2.25.0", "@tiptap/extension-font-family": "^2.25.0", "@tiptap/extension-highlight": "^2.25.0", "@tiptap/extension-image": "^2.25.0", "@tiptap/extension-link": "^2.25.0", "@tiptap/extension-subscript": "^2.25.0", "@tiptap/extension-superscript": "^2.25.0", "@tiptap/extension-table": "^2.25.0", "@tiptap/extension-text-align": "^2.25.0", "@tiptap/extension-text-style": "^2.25.0", "@tiptap/react": "^2.25.0", "@tiptap/starter-kit": "^2.25.0", "@types/js-cookie": "^3.0.6", "axios": "^1.9.0", "cross-fetch": "^4.1.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "dotenv-cli": "^8.0.0", "file-saver": "^2.0.5", "firebase": "^11.10.0", "formik": "^2.4.6", "js-cookie": "^3.0.5", "lucide-react": "^0.510.0", "next": "15.3.2", "qrcode": "^1.5.4", "quill": "^2.0.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-otp-input": "^3.1.1", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "uuid": "^11.1.0", "xlsx": "^0.18.5", "yup": "^1.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/file-saver": "^2.0.7", "@types/jest": "^29.5.14", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.17", "@types/node": "20.17.48", "@types/qrcode": "^1.5.5", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.26.0", "eslint-config-next": "15.3.2", "eslint-config-prettier": "^10.1.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.5.3", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "5.8.3"}}