// public/firebase-messaging-sw.js
importScripts("https://www.gstatic.com/firebasejs/10.3.1/firebase-app-compat.js");
importScripts("https://www.gstatic.com/firebasejs/10.3.1/firebase-messaging-compat.js");

firebase.initializeApp({
  apiKey: "AIzaSyD7cWfyXNEpkxZLJysFqYiQ6NIGlsWjHG0",
  authDomain: "asp-rcm.firebaseapp.com",
  projectId: "asp-rcm",
  messagingSenderId: "775304208308",
  appId: "1:775304208308:web:ead0529e56bac2de04dce1",
});

const messaging = firebase.messaging();

messaging.onBackgroundMessage(function(payload) {
  const { title, body } = payload.notification;
  self.registration.showNotification(title, {
    body,
    icon: "/icon-192x192.png",
  });

  self.clients.matchAll({ includeUncontrolled: true, type: "window" }).then((clients) => {
    clients.forEach((client) => {
      client.postMessage({
        type: "NOTIFICATION_RECEIVED",
        payload: payload,
      });
    });
  });
});

self.addEventListener('push', function (event) {
  event.waitUntil(
    (async () => {
      let data = {};
      try {
        data = event.data ? event.data.json() : {};
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (err) {
        // fallback if not valid JSON
        data = {
          title: 'Test Notification',
          body: event.data?.text() || 'This is a simulated push from DevTools!',
        };
      }

      const title = data.title || 'Test Notification';
      const options = {
        body: data.body || 'This is a simulated push from DevTools!',
        icon: '/logo192.png',
      };

      self.registration.showNotification(title, options);
    })()
  );
});

