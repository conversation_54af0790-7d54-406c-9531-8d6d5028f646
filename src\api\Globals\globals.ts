import client from "@/lib/apollo-client";
import {GET_GLOBAL_OPTIONS, DELETE_TEMPLATE, GET_GLOBALS,CREATE_GLOBALS, GET_TEMPLATE_BY_ID, UPDATE_TEMPLATE} from "./query";

export const getGlobals = async () => {
    try {
      const response = await client.query({
        query: GET_GLOBALS,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };
  export const getGlobalOptions = async (payload:{name:string, stateId?:string}) => {
    try {
      const response = await client.query({
        query: GET_GLOBAL_OPTIONS,
        fetchPolicy: "network-only",
        variables: payload,
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

export const createGlobals = async (payload: {
  input: {
    name: string;
    isTable: boolean;
    options: { id: string; value: string }[];

  };
}) => {
  try {
    const response = await client.mutate({
      mutation: CREATE_GLOBALS,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};

export const updateTemplate = async (payload: {
  input: {
    id: string;
    name?: string;
    status?: boolean;
    type?: string;
    // Remove formType as it's not defined in the schema
    description?: string;
    fields?: string;
      view_summary?: {
    inGrid: string[];
    default: string[];
  };
  };
}) => {
  try {
    const response = await client.mutate({
      mutation: UPDATE_TEMPLATE,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};
export const getByTemplateId = async (id: string) => {
  try {
    const response = await client.query({
      query: GET_TEMPLATE_BY_ID,
      variables: { id },
      fetchPolicy: "network-only",
    });

    return response.data;
  } catch (error) {
    console.error("Error fetching client data:", error);
    throw error;
  }
};
export const deleteTemplate = async (id: string) => {
  try {
    const response = await client.mutate({
      mutation: DELETE_TEMPLATE,
      variables: { id },
    });

    return response.data;
  } catch (error) {
    console.error("Error fetching client data:", error);
    throw error;
  }
};


