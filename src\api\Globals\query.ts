import { gql } from "@apollo/client";

export const GET_GLOBALS = gql`
query FindAllGlobals {
    findAllGlobals {
        globals {
            id
            name
        }
    }
}`;

export const GET_GLOBAL_OPTIONS = gql`
query GetGlobalByName($name: String! $stateId: String) {
    getGlobalByName(name: $name stateId:$stateId) {
        id
        value
    }
}`;

export const CREATE_GLOBALS = gql`
mutation CreateGlobal($input: CreateGlobalInput!) {
    createGlobal(input:$input) {
        name
        isTable
        modalName
        options
        isActive
        createdBy
        updatedBy
        id
        createdAt
        updatedAt
        data
    }
}
`;

export const UPDATE_TEMPLATE = gql`
mutation UpdateTemplate($input: UpdateTemplateInput!) {
    updateTemplate(input: $input) {
        message
        code
        type
        data
    }
}
`;

export const DELETE_TEMPLATE = gql`
mutation DeleteTemplate($id: ID!) {
    deleteTemplate(id: $id) {
        message
        code
        type
        data
    }
}
`;
export const GET_TEMPLATE_BY_ID = gql`
query template($id: ID!) {
    template(id: $id) {
        message
        code
        type
        data
    }
}
`;
// { name: "gender", isTable: false, options: [
//             {
//                 id: "1",
//                 value: "test1"
//             },
//             {
//                 id: "2",
//                 value: "test2"
//             }
//         ] }