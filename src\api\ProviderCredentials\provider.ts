import client from "@/lib/apollo-client";
import {ALLOCATE_TICKET, CREATE_TEMPLATE, DELETE_TEMPLATE, GET_NPI_INFOPRMATION, GET_PROVIDER_CREDENTIALS, GET_PROVIDER_TICKETS, GET_PROVIDER_TICKETS_BY_ID, GET_USER_LIST, UPDATE_PROVIDER_CREDENTIALS, UPDATE_TEMPLATE} from "./query";

export const getProviderTickets = async (payload:{
 filters:object, sortBy: string, sortOrder:string, page: number, limit: number, search: string, selectedFields?: object
 }) => {
    try {
      const response = await client.query({
        query: GET_PROVIDER_TICKETS,
        fetchPolicy: "network-only",
        variables: { input: payload }
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
        const graphQLErrors = (error as { graphQLErrors: import("graphql").GraphQLError[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.extensions?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

export const createTemplate = async (payload: {
  input: {
    name: string;
    status: boolean;
    type: string;
    // formType: string;
    view_summary?: Record<string, unknown>;
    fields?: Record<string, unknown>;
    organisationId?: string;
    subOrganisationId?: string;
  };
}) => {
  try {
    const response = await client.mutate({
      mutation: CREATE_TEMPLATE,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};

export const updateTemplate = async (payload: {
  input: {
    id: string;
    name?: string;
    status?: boolean;
    type?: string;
    description?: string;
    fields?: string;
    view_summary?: {
    inGrid: string[];
    default: string[];
    organisationId?: string;
    subOrganisationId?: string;
  };
  };
}) => {
  try {
    const response = await client.mutate({
      mutation: UPDATE_TEMPLATE,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};
export const getTicketsById = async (id: string) => {
  try {
    const response = await client.query({
      query: GET_PROVIDER_TICKETS_BY_ID,
      variables: { id },
      fetchPolicy: "network-only",
    });

    return response.data;
  } catch (error) {
    console.error("Error fetching client data:", error);
    throw error;
  }
};
export const deleteTemplate = async (id: string) => {
  try {
    const response = await client.mutate({
      mutation: DELETE_TEMPLATE,
      variables: { id },
    });

    return response.data;
  } catch (error) {
    console.error("Error fetching client data:", error);
    throw error;
  }
};

export const getUserList = async () => {
    try {
      const response = await client.query({
        query: GET_USER_LIST,
        fetchPolicy: "network-only"
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
        const graphQLErrors = (error as { graphQLErrors: import("graphql").GraphQLError[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.extensions?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
};
  
export const allocateClient = async (payload: {
  input: {
    id: string;
    assignedTo: string;
  };
}) => {
  try {
    const response = await client.mutate({
      mutation: ALLOCATE_TICKET,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};

export const getNpiInformations = async (payload: { type: string[] }) => {
  try {
    const response = await client.mutate({
      mutation: GET_NPI_INFOPRMATION,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};

export const getProviderNpi = async (payload: { type: string[] }) => {
  try {
    const response = await client.mutate({
      mutation: GET_PROVIDER_CREDENTIALS,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
}

export const updateProviderNpi = async (payload: {
  input: {
    id: string;
    values: string;
  };
}) => {
  try {
    const response = await client.mutate({
      mutation: UPDATE_PROVIDER_CREDENTIALS,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};

