import { gql } from "@apollo/client";

export const GET_PROVIDER_TICKETS = gql`
query ProviderTickets($input:PaginateProviderTicketArgs!){
    providerTickets(input:$input){
        provider
        pagination {
            page
            limit
            total
            totalItems
            totalPages
            hasNext
            hasPrev
        }
    }
}
`;

export const GET_USER_LIST = gql`
query UsersUnderManager {
    usersUnderManager {
        role
        users {
            _id
            name
            email
            employeeId
            roleName
        }
    }
}
`;

export const CREATE_TEMPLATE = gql`
mutation CreateTemplate($input: CreateTemplateInput!) {
    createTemplate(input: $input) {
        message
        code
        type
        data
    }
}
`;

export const UPDATE_TEMPLATE = gql`
mutation UpdateTemplate($input: UpdateTemplateInput!) {
    updateTemplate(input: $input) {
        message
        code
        type
        data
    }
}
`;

export const DELETE_TEMPLATE = gql`
mutation DeleteTemplate($id: ID!) {
    deleteTemplate(id: $id) {
        message
        code
        type
        data
    }
}
`;
export const GET_PROVIDER_TICKETS_BY_ID = gql`
query ProviderTicket($id: ID!) {
    providerTicket(id: $id) {
        _id
        ticketId
        templateId
        values
        status
        type
        allocated_type
        follow_up_date
        received_date
        worked_date
        audit_by
        createdby
        createdAt
        updatedAt
        source_ticket_id
        assigned_to
    }
}
`;

export const ALLOCATE_TICKET = gql`
mutation UpdateProviderTicket($input: UpdateProviderTicketInput!) {
    updateProviderTicket(
        input: $input
    ) {
        message
        code
        type
        data
    }
}`;

export const GET_NPI_INFOPRMATION = gql`
mutation GetNpiInformations($type:[String!]) {
    getNpiInformations(type: $type) {
        message
        code
        type
        data
    }
}`;

export const GET_PROVIDER_CREDENTIALS = gql`
mutation GetProviderInformations($type:[String!]) {
    getProviderInformations(type: $type) {
        message
        code
        type
        data
    }
}
`;

export const UPDATE_PROVIDER_CREDENTIALS = gql`
mutation UpdateProviderTicket($input: UpdateProviderTicketInput!) {
    updateProviderTicket(input: $input) {
        message
        code
        type
        data
    }
}`;