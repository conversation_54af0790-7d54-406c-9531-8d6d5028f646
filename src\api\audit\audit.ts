/* eslint-disable @typescript-eslint/no-explicit-any */
import client from "@/lib/apollo-client";
import { GET_AUDIT } from "./query";

export const getAudit = async (payload: {
  input: {
    filters?: { from: string; to: string, userEmail:string };
    page: number;
    limit: number;
  };
}) => {
  try {
    const sanitizedPayload = { ...payload };

    // Clean up empty filters
    const { filters } = sanitizedPayload.input;
    if (filters?.from === "" && filters?.to === "" && filters?.userEmail === "" ) {
      delete sanitizedPayload.input.filters;
    }

    const response = await client.query({
      query: GET_AUDIT,
      variables: sanitizedPayload,
      fetchPolicy: "network-only",
    });

    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};
