import { gql } from "@apollo/client";

export const GET_AUDIT = gql`
query AuditLogs($input:AuditLogQueryInput) {
    auditLogs(input:$input) {
        total
        page
        limit
        totalPages
        hasNextPage
        hasPreviousPage
        data {
            id
            userEmail
            action
            entityType
            message
            timestamp
           
        }
    }
}`;