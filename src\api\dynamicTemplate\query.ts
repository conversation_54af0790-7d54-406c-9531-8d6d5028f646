import { gql } from "@apollo/client";

export const GET_TEMPLATE = gql`
query Templates($filters: JSON, $sortBy: String, $sortOrder: String, $page: Int, $limit: Int, $search: String) {
    templates(filters: $filters, sortBy: $sortBy, sortOrder:$sortOrder, page: $page, limit: $limit, search: $search) {
        message
        code
        type
        data
    }
}
`;

export const CREATE_TEMPLATE = gql`
mutation CreateTemplate($input: CreateTemplateInput!) {
    createTemplate(input: $input) {
        message
        code
        type
        data
    }
}
`;

export const UPDATE_TEMPLATE = gql`
mutation UpdateTemplate($input: UpdateTemplateInput!) {
    updateTemplate(input: $input) {
        message
        code
        type
        data
    }
}
`;

export const DELETE_TEMPLATE = gql`
mutation DeleteTemplate($id: ID!) {
    deleteTemplate(id: $id) {
        message
        code
        type
        data
    }
}
`;

export const SWITCH_TEMPLATE_VERSION = gql`
mutation SwitchTemplateVersion($input: SwitchTemplateVersionInput!) {
    switchTemplateVersion(input: $input) {
        message
        code
        type
        data
    }
}
`;

export const CREATE_TEMPLATE_VERSION = gql`
mutation CreateTemplateVersion($input: CreateTemplateVersionInput!) {
    createTemplateVersion(input: $input) {
        message
        code
        type
        data
    }
}
`;
export const GET_TEMPLATE_BY_ID = gql`
query template($id: ID!) {
    template(id: $id) {
        message
        code
        type
        data
    }
}
`;

export const CLONE_TEMPLATE = gql`
mutation CloneTemplate($input: CloneTemplateInput!) {
    cloneTemplate(input: $input) {
        message
        code
        type
        data
    }
}
`;
