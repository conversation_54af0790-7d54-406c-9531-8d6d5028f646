import client from "@/lib/apollo-client";
import {CREATE_TEMPLATE, DELETE_TEMPLATE, GET_TEMPLATE, GET_TEMPLATE_BY_ID, UPDATE_TEMPLATE, SWITCH_TEMPLATE_VERSION, CREATE_TEMPLATE_VERSION, CLONE_TEMPLATE} from "./query";

export const getTemplate = async (payload:{
 filters:object, sortBy: string,sortOrder:string, page?: number, limit?: number, search:  string
 }) => {
    try {
      const response = await client.query({
        query: GET_TEMPLATE,
        fetchPolicy: "network-only",
        variables: payload
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

export const createTemplate = async (payload: {
  input: {
    name: string;
    // status: boolean;
    type: string;
    // formType: string;
    view_summary?: Record<string, unknown>;
    fields?: Record<string, unknown>;
    organisationId?: string;
    subOrganisationId?: string;
  };
}) => {
  try {
    const response = await client.mutate({
      mutation: CREATE_TEMPLATE,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};

export const updateTemplate = async (payload: {
  input: {
    id: string;
    name?: string;
    status?: boolean;
    type?: string;
    description?: string;
    fields?: string;
    view_summary?: {
    inGrid: string[];
    default: string[];
    organisationId?: string;
    subOrganisationId?: string;
  };
  };
}) => {
  try {
    const response = await client.mutate({
      mutation: UPDATE_TEMPLATE,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};
export const getByTemplateId = async (id: string) => {
  try {
    const response = await client.query({
      query: GET_TEMPLATE_BY_ID,
      variables: { id },
      fetchPolicy: "network-only",
    });

    return response.data;
  } catch (error) {
    console.error("Error fetching client data:", error);
    throw error;
  }
};
export const deleteTemplate = async (id: string) => {
  try {
    const response = await client.mutate({
      mutation: DELETE_TEMPLATE,
      variables: { id },
    });

    return response.data;
  } catch (error) {
    console.error("Error fetching client data:", error);
    throw error;
  }
};

// Switch to an existing template version
export const switchTemplateVersion = async (payload: {
  input: {
    templateId: string;
    version: string;
  };
}) => {
  try {
    const response = await client.mutate({
      mutation: SWITCH_TEMPLATE_VERSION,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};

// Create a new template version based on an existing version
export const createTemplateVersion = async (payload: {
  input: {
    templateId: string;
    sourceVersion: string;
    newVersion: string;
  };
}) => {
  try {
    const response = await client.mutate({
      mutation: CREATE_TEMPLATE_VERSION,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};

export const cloneTemplate = async (payload: {
  input: {
    name: string;
    id: string;
    organisationId?: string;
    subOrganisationId?: string;
  };
}) => {
  try {
    const response = await client.mutate({
      mutation: CLONE_TEMPLATE,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};


