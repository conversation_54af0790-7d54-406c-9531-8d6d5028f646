import client from "@/lib/apollo-client";
import { EXPORT, GET_ALL_EXPORT, EXPORT_LOGS, DELETE_EXPORT} from "./query";

 export const exportTableData = async (payload:{input:{collection:string,fields:string[],createdBy:string, selectedRow?:string[],fileType:string,filters?:{key:{value:string}}}}) => {
    try {
      const response = await client.mutate({
        mutation: EXPORT,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

   export const getAllExports = async (payload:{
     page: number,
        limit: number,
        search: string,
        filters: string,
        sortBy: string,
        sortOrder: string,
   }) => {
    try {
      const response = await client.query({
        query: GET_ALL_EXPORT,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };


  export const exportLogs = async (payload:{
   taskId:string
   }) => {
    try {
      const response = await client.query({
        query: EXPORT_LOGS,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

 export const deleteExports = async (payload:{
   taskId:string
   }) => {
    try {
      const response = await client.mutate({
        mutation: DELETE_EXPORT,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

  