
import { gql } from "@apollo/client";

export const EXPORT = gql`
mutation StartExport(
        $input:StartExportInput!
    ){
    startExport(
        input:$input
    ) {
        message
        taskId
    }
}`
export const GET_ALL_EXPORT = gql`
query AllExports(
        $page: Int
        $limit: Int
        $search: String
        $filters: JSON
        $sortBy: String
        $sortOrder: String
    ) {
    allExports(
        page: $page
        limit: $limit
        search: $search
        filters: $filters
        sortBy: $sortBy
        sortOrder: $sortOrder
    ) {
        pagination {
            page
            limit
            total
            totalItems
            totalPages
            hasNext
            hasPrev
        }
        items {
            taskId
            createdAt
            createdBy
            name
            status
            fileType
            type
            collectionName
            downloadUrl
        }
    }
}
`
export const EXPORT_LOGS = gql`
query ExportStatus($taskId:String!) {
    exportStatus(taskId: $taskId) {
        taskId
        type
        downloadUrl
        status
        backupUrls
        statusLog {
            status
            message
            timestamp
        }
             failedRows {
                row
                error
                rowNumber
            }
                auditLogUrl
        createdAt
        createdBy
        collectionName
    }
}`

export const DELETE_EXPORT = gql`
mutation DeleteTask9($taskId: String!) {
    deleteTask(taskId: $taskId) {
        success
        message
    }
}`