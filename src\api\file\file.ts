import client from "@/lib/apollo-client";
import { GET_PRE_SIGNED_URL, GET_IMAGE_URL } from "./query";

 export const getPreSignedUrl = async (payload:{input:{filename:string,contentType:string}}) => {
    try {
      const response = await client.mutate({
        mutation: GET_PRE_SIGNED_URL,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

   export const getImagedUrl = async (payload:{filename:string}) => {
    try {
      const response = await client.mutate({
        mutation: GET_IMAGE_URL,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };
  
