
import { gql } from "@apollo/client";

export const GET_PRE_SIGNED_URL = gql`
mutation GenerateUploadUrl($input:GenerateUploadUrlInput! ) {
    generateUploadUrl(input:$input ) {
        message
        code
        type
        data
    }
}`

export const GET_IMAGE_URL = gql`
mutation GenerateViewUrl($filename:String!) {
    generateViewUrl(filename: $filename) {
        message
        code
        type
        data
    }
}`