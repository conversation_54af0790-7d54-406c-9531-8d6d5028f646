

import client from "@/lib/apollo-client";
import { CREATE_VIEW, DELETE_VIEW, GET_ALL_VIEW, GET_VIEW, UPDATE_VIEW, SELECT_HEADER, NOTIFICATION_LIST, NOTIFICATION_COUNT, MARK_AS_READ } from "./query";

export const createView = async (payload: {
    input: {name: string, grid_fields: string[], user_id: string, type?: string };
  }) => {
    try {
      const response = await client.mutate({
        mutation: CREATE_VIEW,
        variables: payload,
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
};
  
export const UpdateView = async (payload: {
  input: {id: string, name: string, grid_fields?: string[], user_id: string, type?: string };
}) => {
  try {
    const response = await client.mutate({
      mutation: UPDATE_VIEW,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};

  export const getAllView = async (payload:{
    type?: string
  }) => {
    try {
      const response = await client.query({
        query: GET_ALL_VIEW,
        fetchPolicy: "network-only",
        variables: payload
      });
      return response?.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

    export const getView = async (payload: { id: string }) => {
    try {
      const response = await client.query({
        query: GET_VIEW,
        variables:payload,
        fetchPolicy: "network-only",
      });
      return response?.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
};
  
export const deleteView = async (payload: { id: string }) => {
  try {
    const response = await client.mutate({
      mutation: DELETE_VIEW,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};

 export const selectHeaders = async (payload: {  name: string,
        userId: string,
        organisationId?: string,
        subOrganisationId?: string}) => {
    try {
      const response = await client.query({
        query: SELECT_HEADER,
        variables:payload,
        fetchPolicy: "network-only",
      });
      return response?.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
};

export const getNotificationList = async (payload:{
  input: {
    userId: string
    page?: number
    limit?: number
    isRead?: boolean
    status?: string
        }
 }) => {
    try {
      const response = await client.query({
        query: NOTIFICATION_LIST,
        fetchPolicy: "network-only",
        variables: payload
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

  export const getNotificationCount = async (payload:{
      userId: string
          
   }) => {
      try {
        const response = await client.query({
          query: NOTIFICATION_COUNT,
          fetchPolicy: "network-only",
          variables: payload
        });
        return response.data;
      } catch (error) {
        if (error instanceof Error && "graphQLErrors" in error) {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
          const errorCode = graphQLErrors[0]?.code;
          throw { code: errorCode, message: error.message };
        } else {
          throw error;
        }
      }
    };

    export const markAsAllRead = async () => {
      try {
        const response = await client.mutate({
          mutation: MARK_AS_READ
        });
        return response.data;
      } catch (error) {
        if (error instanceof Error && "graphQLErrors" in error) {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
          const errorCode = graphQLErrors[0]?.code;
          throw { code: errorCode, message: error.message };
        } else {
          throw error;
        }
      }
  };