
import { gql } from "@apollo/client";

export const CREATE_VIEW = gql`
mutation CreateGridTemplate($input:CreateGridTemplateInput! ) {
    createGridTemplate(input:$input ) {
        message
        code
        type
        data
    }
}`
export const GET_ALL_VIEW = gql`
query GridTemplates($type: String) {
    gridTemplates(type: $type) {
        message
        code
        type
        data
    }
}`

export const GET_VIEW = gql`
query GridTemplate($id:ID!) {
    gridTemplate(id: $id) {
        message
        code
        type
        data
    }
}
`
export const UPDATE_VIEW = gql`
mutation UpdateGridTemplate($input:UpdateGridTemplateInput! ) {
    updateGridTemplate(input:$input ) {
        message
        code
        type
        data
    }
}`

export const DELETE_VIEW = gql`
mutation DeleteGridTemplate($id: ID!) {
    deleteGridTemplate(id: $id) {
        message
        code
        type
        data
    }
}`
export const SELECT_HEADER = gql`
query SelectHeader($name:String! $userId:String! $organisationId:String $subOrganisationId:String) {
    selectHeader(
        name: $name
        userId: $userId
        organisationId: $organisationId
        subOrganisationId: $subOrganisationId
    )
}`

export const NOTIFICATION_LIST = gql`
query NotificationHistory ($input:GetNotificationHistoryInput!){
    notificationHistory(input: $input) {
        total
        page
        limit
        totalPages
        hasNext
        hasPrev
        unreadCount
        notifications {
           _id
            notificationId
            senderId
            userId
            userEmail
            type
            title
            message
            channels
            priority
            status
            data
            metadata
            sentAt
            deliveredAt
            readAt
            orgId
            subOrgId
            processId
            isRead
            createdAt
            updatedAt
            senderName
            senderEmail
        }
    }
}
`

export const NOTIFICATION_COUNT = gql`
query NotificationStats($userId:String) {
    notificationStats(userId:$userId) {
        totalNotifications
        unreadCount
        readCount
    }
}`

export const MARK_AS_READ = gql`
mutation MarkAllNotificationsAsRead {
    markAllNotificationsAsRead {
        message
        affectedCount
        success
    }
}`