/* eslint-disable @typescript-eslint/no-explicit-any */
import client from "@/lib/apollo-client";
import { IMPORT, IMPORT_DATA, START_IMPORT} from "./query";

 export const ImportTableData = async (payload:{input:{uniqueFields:string[],collectionName:string,templateId:string, mappingJson:any,requiredFields:string[]}}) => {
    try {
      const response = await client.mutate({
        mutation: IMPORT,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

   export const startImport = async (payload:
    {input:{
        
            filePath: string,
            collectionName: string,
            createdBy: string,
            type: string,
            templateId: string
            orgId?:string
        }}) => {
    try {
      const response = await client.mutate({
        mutation: START_IMPORT,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };
    export const importData = async (payload:
    {templateId:string}) => {
    try {
      const response = await client.query({
        query: IMPORT_DATA,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };
