import { gql } from "@apollo/client";

export const IMPORT = gql`
mutation CreateImportConfiguration($input:
CreateImportConfigurationInput!) {
    createImportConfiguration(
        input: $input
    ){
        message
        code
        data {
            _id
            userId
            templateId
            collectionName
            mappingJson
            requiredFields
            uniqueFields
            isActive
            orgId
            createdAt
            updatedAt
        }
}
    
}`


export const START_IMPORT = gql`
mutation StartImport($input: StartImportInput!) {
    startImport(
        input: $input
    )
        { message}
}`

export const IMPORT_DATA = gql`
query GetImportConfigurationByTemplateId($templateId: String!) {
    getImportConfigurationByTemplateId(templateId: $templateId) {
        _id
        userId
        templateId
        collectionName
        mappingJson
        requiredFields
        uniqueFields
        isActive
        orgId
        createdAt
        updatedAt
    }
}`