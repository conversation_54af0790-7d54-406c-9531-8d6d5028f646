import client from "@/lib/apollo-client";
import { LOGIN, EMAILED_OTP, RESEND_OTP, T_OTP, SSO_LOGIN } from "./query";

export const login = async (payload: {
    input: {
        email: string
    };
  }) => {
    try {
      const response = await client.mutate({
        mutation: LOGIN,
        variables: payload,
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

  export const emailedOtp = async (payload: {
    input: {
        email: string;
        otp: string
    };
  }) => {
    try {
      const response = await client.mutate({
        mutation: EMAILED_OTP,
        variables: payload,
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

  export const resendOtp = async (payload: {
        email: string
  }) => {
    try {
      const response = await client.mutate({
        mutation: RESEND_OTP,
        variables: payload,
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

    export const tOtp = async (payload: {
    input: {
        email: string;
        otp?: string;
        skip2FACheck:boolean
    };
  }) => {
    try {
      const response = await client.mutate({
        mutation: T_OTP,
        variables: payload,
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

   export const ssoLogin = async (payload: {
    input: {
        token: string;
    };
  }) => {
    try {
      const response = await client.mutate({
        mutation: SSO_LOGIN,
        variables: payload,
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message,error: error.cause};
      } else {
        throw error;
      }
    }
  };