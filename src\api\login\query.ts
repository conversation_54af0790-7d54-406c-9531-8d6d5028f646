import { gql } from "@apollo/client";

export const LOGIN = gql`
mutation LoginRequest($input:OtpRequestInput!) {
    loginRequest(input:$input){
        message
        code
        type
        data
    }
}`;

export const EMAILED_OTP = gql`
mutation OtpVerify($input:OtpVerifyInput!) {
    otpVerify(input:$input){
        message
        code
        type
        data
    }
}`;

export const RESEND_OTP = gql`
mutation ResendOtp($email:String!) {
    resendOtp(email:$email){
        message
        code
        type
        data
    }
}`;

export const T_OTP = gql`
mutation TotpVerify($input:TotpVerifyInput!) {
    totpVerify(input: $input) {
        message
        code
        type
        data
    }
}`;

export const SSO_LOGIN = gql`
mutation AgentSSOLogin($input:AgentSSOLoginInput!) {
    agentSSOLogin(input: $input) {
        message
        code
        type
        data
    }
}`
 