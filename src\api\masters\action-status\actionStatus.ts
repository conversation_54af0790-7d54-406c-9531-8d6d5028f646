/* eslint-disable @typescript-eslint/no-explicit-any */
// services/actionCodes.ts
import client from "@/lib/apollo-client"
import { CREATE_ACTION_CODE, CREATE_ACTION_STATUS, CREATE_STATUS_CODE, DELETE_ACTION_STATUS, GET_ACTION_CODES, GET_ACTION_LIST, GET_BY_ID_ACTION, GET_PROCESS, GET_STATUS_CODE, UPDATE_ACTION_STATUS } from './query';

export const getActionCodes = async (payload: {
        page?: number
        limit?: number
        search?: string
        sortBy?: string
        sortOrder?: string
        filters?:string
        selectedFields?:{ [key: string]: number }
  }) => {
    console.log('variables',payload)
    try {
      const response = await client.query({
        query: GET_ACTION_CODES,
        fetchPolicy: 'network-only',
        variables: payload,
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && 'graphQLErrors' in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

  export const getProcessList = async (payload: {
      search?: string;
      sortBy?: string;
      filters?:string;
      sortOrder?: string;
      page?: number;
      limit?: number;
    }) => {
      try {
        const response = await client.query({
          query: GET_PROCESS,
          fetchPolicy: 'network-only',
          variables: {
            ...payload
          },
        });
        return response.data;
      } catch (error) {
        if (error instanceof Error && 'graphQLErrors' in error) {
          const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
          const errorCode = graphQLErrors[0]?.code;
          throw { code: errorCode, message: error.message };
        } else {
          throw error;
        }
      }
    };

    export const getStatusCodeList = async (payload: {
      search?: string;
      sortBy?: string;
      filters?:string | null;
      sortOrder?: string;
      page?: number;
      limit?: number;
    }) => {
      try {
        const response = await client.query({
          query: GET_STATUS_CODE,
          fetchPolicy: 'network-only',
          variables: {
            ...payload
          },
        });
        return response.data;
      } catch (error) {
        if (error instanceof Error && 'graphQLErrors' in error) {
          const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
          const errorCode = graphQLErrors[0]?.code;
          throw { code: errorCode, message: error.message };
        } else {
          throw error;
        }
      }
    };

    export const getACtionCOdeList = async (payload: {
      search?: string;
      sortBy?: string;
      filters?:string;
      sortOrder?: string;
      page?: number;
      limit?: number;
    }) => {
      try {
        const response = await client.query({
          query: GET_ACTION_LIST,
          fetchPolicy: 'network-only',
          variables: {
            ...payload
          },
        });
        return response.data;
      } catch (error) {
        if (error instanceof Error && 'graphQLErrors' in error) {
          const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
          const errorCode = graphQLErrors[0]?.code;
          throw { code: errorCode, message: error.message };
        } else {
          throw error;
        }
      }
    };

    export const createStatusCode = async (input: {
      input: {
        processId: string,
        code: string
      };
    }) => {
      try {
        const response = await client.mutate({
          mutation: CREATE_STATUS_CODE,
          variables: input,
        });
        return response.data;
      } catch (error) {
        if (error instanceof Error && "graphQLErrors" in error) {
          const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
          const errorCode = graphQLErrors[0]?.code;
          throw { code: errorCode, message: error.message };
        } else {
          throw error;
        }
      }
    };

    export const createActionCode = async (input: {
      input: {
        statusCodeId: string,
        code: string
      };
    }) => {
      try {
        const response = await client.mutate({
          mutation: CREATE_ACTION_CODE,
          variables: input,
        });
        return response.data;
      } catch (error) {
        if (error instanceof Error && "graphQLErrors" in error) {
          const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
          const errorCode = graphQLErrors[0]?.code;
          throw { code: errorCode, message: error.message };
        } else {
          throw error;
        }
      }
    };  

    export const createActionStatus = async (input: {
      input: {
        processId: string | null
            actionCodeId: string | null
            statusCodeId: string | null
            templateId: string | null
            flattenedValues: JSON | null
            values: string | null
      };
    }) => {
      try {
        const response = await client.mutate({
          mutation: CREATE_ACTION_STATUS,
          variables: input,
        });
        return response.data;
      } catch (error) {
        if (error instanceof Error && "graphQLErrors" in error) {
          const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
          const errorCode = graphQLErrors[0]?.code;
          throw { code: errorCode, message: error.message };
        } else {
          throw error;
        }
      }
    }; 

    export const updateActionStatus = async (input: {
      input: {
        id: string | null
        processId: string | null
            actionCodeId: string | null
            statusCodeId: string | null
            templateId: string | null
            values: string | null
            isActive: boolean | null
      };
    }) => {
      try {
        const response = await client.mutate({
          mutation: UPDATE_ACTION_STATUS,
          variables: input,
        });
        return response.data;
      } catch (error) {
        if (error instanceof Error && "graphQLErrors" in error) {
          const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
          const errorCode = graphQLErrors[0]?.code;
          throw { code: errorCode, message: error.message };
        } else {
          throw error;
        }
      }
    }; 

    export const deleteActionStatus = async (payload: { id: string }) => {
      try {
        const response = await client.mutate({
          mutation: DELETE_ACTION_STATUS,
          variables: payload,
        });
        return response.data;
      } catch (error) {
        if (error instanceof Error && "graphQLErrors" in error) {
          const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
          const errorCode = graphQLErrors[0]?.code;
          throw { code: errorCode, message: error.message };
        } else {
          throw error;
        }
      }
    };

    export const actionStatusGetById = async (payload: { id: string | null }) => {
      try {
        const response = await client.query({
          query: GET_BY_ID_ACTION,
          variables: payload,
          fetchPolicy: "network-only",
        });
        return response;
      } catch (error: any) {
        const graphQLErrors = error?.graphQLErrors ?? [];
        const errorCode = graphQLErrors[0]?.code ?? "UNKNOWN";
        throw { code: errorCode, message: error.message };
      }
    };