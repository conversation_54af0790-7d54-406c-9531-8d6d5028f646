import { gql } from "@apollo/client";

export const GET_ACTION_CODES = gql`
 query ActionStatusCodeMaps(
    $filters: JSON
    $page: Int
    $limit: Int
    $search: String
    $sortBy: String
    $sortOrder: String
    $selectedFields: JSON
  ) {
    actionStatusCodeMaps(
      filters: $filters
      page: $page
      limit: $limit
      search: $search
      sortBy: $sortBy
      sortOrder: $sortOrder
      selectedFields:$selectedFields
    ) {
      items
      pagination {
        page
        limit
        total
        totalItems
        totalPages
        hasNext
        hasPrev
      }
    }
  }

`;

export const GET_PROCESS = gql`
query Processes {
    processes(
        page: 1
        limit: 100
        search: null
        filters: null
        sortBy: null
        sortOrder: null
    ) {
        message
        code
        type
        data
    }
}
`;

export const GET_BY_ID_ACTION = gql`
query ActionStatusCodeMap($id: ID!) {
    actionStatusCodeMap(id: $id) {
        message
        code
        type
        data
    }
}
`;

export const GET_STATUS_CODE = gql`
  query StatusCodes(
    $filters: String
  ) {
    statusCodes(
      page: 1
      limit: 100
      search: null
      filters: $filters
      sortBy: null
      sortOrder: null
    ) {
      pagination {
        page
        limit
        total
        totalItems
        totalPages
        hasNext
        hasPrev
      }
      items {
        _id
        processId
        code
        createdBy
        updatedBy
        isActive
      }
    }
  }
`;


export const GET_ACTION_LIST = gql`
query ActionCodes(
    $filters: String
  ){
    actionCodes(
        page: null
        limit: null
        search: null
        filters: $filters
        sortBy: null
        sortOrder: null
    ) {
        items {
            _id
            code
            createdBy
            updatedBy
            isActive
            process
        }
    }
}

`;

export const CREATE_STATUS_CODE = gql`
  mutation CreateStatusCode($input: CreateStatusCodeInput!) {
    createStatusCode(input: $input) {
      message
      code
      type
      data
    }
  }
`;

export const CREATE_ACTION_CODE = gql`
  mutation CreateActionCode($input: CreateActionCodeInput!) {
    createActionCode(input: $input) {
      message
      code
      type
      data
    }
  }
`;

export const CREATE_ACTION_STATUS = gql`
  mutation CreateActionStatusCodeMap($input: CreateActionStatusCodeMapInput!) {
    createActionStatusCodeMap(input: $input) {
      message
      code
      type
      data
    }
  }
`;

export const UPDATE_ACTION_STATUS = gql`
  mutation UpdateActionStatusCodeMap($input: UpdateActionStatusCodeMapInput!) {
    updateActionStatusCodeMap(input: $input) {
      message
      code
      type
      data
    }
  }
`;

export const DELETE_ACTION_STATUS = gql`
  mutation DeleteActionStatusCodeMap($id:ID!) {
    deleteActionStatusCodeMap(id: $id) {
        message
        code
        type
        data
    }
}

`;