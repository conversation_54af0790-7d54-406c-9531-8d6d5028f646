/* eslint-disable @typescript-eslint/no-explicit-any */
import client from "@/lib/apollo-client";
import { CREATE_CPT_CODE, CREATE_DIAGNOSIS, CREATE_ICD, GET_ALL_CPT, DELETE_CPT, CREATE_SPECIALITY, GET_ICD, GET_DIAGNOSIS, GET_SPECIALITY, GET_CPTCODES, CREATE_MAIN_CPT, GET_CPT_BY_ID, UPDATE_MAIN_CPT } from "./query";

// Create ICD
export const createIcd = async (input: {
  input: {
    type: string;
    code: string | null;
    values: string | null;
    templateId: string | null;
  };
}) => {
  try {
    const response = await client.mutate({
      mutation: CREATE_ICD,
      variables: input,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};

// Create Diagnosis
export const createDiagnosis = async (input: {
  input: {
    type: string | null;
    name: string | null;
    icd: string | null;
    templateId: string | null;
    values: string | null;
  };
}) => {
  try {
    const response = await client.mutate({
      mutation: CREATE_DIAGNOSIS,
      variables: input,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};

// Create CPT Code
export const createCPTCode = async (input: {
    input: {
         code: string | null,
         values: string | null,
         templateId: string | null,
         specialtyId: string |  null };
}) => {
  try {
    const response = await client.mutate({
      mutation: CREATE_CPT_CODE,
      variables: input,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};

export const createSpeciality = async (input: {
    input: {
        type: string | null,
        name: string | null,
        values: string | null,
        templateId: string |  null 
};
}) => {
  try {
    const response = await client.mutate({
      mutation: CREATE_SPECIALITY,
      variables: input,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};

export const createMainCPT = async (input: {
    input: {
        type: string | null
        values: string | null
        specialtyId: string | null
        diagnosisId: string | null
        templateId: string | null
        cptCodeId: string | null
        icdId: string | null
        flattenedValues:{ [key: string]: any }
    }
}) => {
  try {
    const response = await client.mutate({
      mutation: CREATE_MAIN_CPT,
      variables: input,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};

export const updateMainCPT = async (input: {
  input: {
    id: string | null
    type: string | null
    values: string | null
    specialtyId: string | null
    diagnosisId: string | null
    cptCodeId: string | null
    icdId: string | null
    flattenedValues:{ [key: string]: any }
  }
}) => {
try {
  const response = await client.mutate({
    mutation: UPDATE_MAIN_CPT,
    variables: input,
  });
  return response.data;
} catch (error) {
  if (error instanceof Error && "graphQLErrors" in error) {
    const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
    const errorCode = graphQLErrors[0]?.code;
    throw { code: errorCode, message: error.message };
  } else {
    throw error;
  }
}
};

// Get All CPT
export const getAllCpt = async (payload: {
  page: number;
  limit: number;
  search: string;
  sortBy: string;
  sortOrder: string;
  filters: string;
  selectedFields:{ [key: string]: number }
}) => {

  try {
    const response = await client.query({
      query: GET_ALL_CPT,
      variables: payload,
      fetchPolicy: "network-only",
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};



// Delete CPT
export const deleteCpt = async (payload: { id: string }) => {
  try {
    const response = await client.mutate({
      mutation: DELETE_CPT,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};

export const getICDCodes = async (payload: {
    search?: string;
    filters?: any;
    sortBy?: string;
    sortOrder?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      const response = await client.query({
        query: GET_ICD,
        fetchPolicy: 'network-only',
        variables: {
          ...payload
        },
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && 'graphQLErrors' in error) {
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

  export const getDiagnosis = async (payload: {
    search?: string;
    filters?: any;
    sortBy?: string;
    sortOrder?: string;
    page?: number;
    limit?: number;
  }) => {
    console.log('diagnosis',payload)
    try {
      const response = await client.query({
        query: GET_DIAGNOSIS,
        fetchPolicy: 'network-only',
        variables: payload,
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && 'graphQLErrors' in error) {
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

  export const getSpeciality = async (payload: {
    search?: string;
    filters?: any;
    sortBy?: string;
    sortOrder?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      const response = await client.query({
        query: GET_SPECIALITY,
        fetchPolicy: 'network-only',
        variables: payload,
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && 'graphQLErrors' in error) {
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

  export const getCPTCodes = async (payload: {
    search?: string;
    filters?: any;
    sortBy?: string;
    sortOrder?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      const response = await client.query({
        query: GET_CPTCODES,
        fetchPolicy: 'network-only',
        variables: payload,
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && 'graphQLErrors' in error) {
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

  export const getByIdCPT = async (payload: { id: string  }) => {
    try {
      const response = await client.query({
        query: GET_CPT_BY_ID,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data.cpt;
    } catch (error: any) {
      const graphQLErrors = error?.graphQLErrors ?? [];
      const errorCode = graphQLErrors[0]?.code ?? "UNKNOWN";
      throw { code: errorCode, message: error.message };
    }
  };
  
