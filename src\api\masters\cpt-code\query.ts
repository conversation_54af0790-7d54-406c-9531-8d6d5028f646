import { gql } from "@apollo/client";

export const CREATE_ICD = gql`
  mutation CreateIcd($input: CreateIcdInput!) {
    createIcd(input: $input) {
      message
      code
      type
      data
    }
  }
`;

export const CREATE_DIAGNOSIS = gql`
  mutation CreateDiagnosis($input: CreateDiagnosisInput!) {
    createDiagnosis(input: $input) {
      message
      code
      type
      data
    }
  }
`;

export const CREATE_CPT_CODE = gql`
  mutation CreateCptCode($input: CreateCptCodeInput!) {
    createCptCode(input: $input) {
      message
      code
      type
      data
    }
  }
`;

export const CREATE_SPECIALITY = gql`
  mutation CreateSpecialty($input: CreateSpecialtyInput!) {
    createSpecialty(input: $input) {
      message
      code
      type
      data
    }
  }
`;

export const CREATE_MAIN_CPT = gql`
  mutation CreateCpt($input: CreateCptInput!) {
    createCpt(input: $input) {
      message
      code
      type
      data
    }
  }
`;

export const UPDATE_MAIN_CPT = gql`
  mutation UpdateCpt($input: UpdateCptInput!) {
    updateCpt(input: $input) {
      message
      code
      type
      data
    }
  }
`;




export const GET_ALL_CPT = gql`
query Cpts(
  $page: Int
        $limit: Int
        $search: String
        $filters: JSON
        $sortBy: String
        $sortOrder: String
        $selectedFields: JSON) {
    cpts(
        page: $page
        limit: $limit
        search: $search
        sortBy: $sortBy
        sortOrder: $sortOrder
        filters: $filters
        selectedFields: $selectedFields
  ) {
    items
    pagination {
      page
      limit
      total
      totalItems
      totalPages
      hasNext
      hasPrev
    }
  }
}`





export const GET_CPT_BY_ID = gql`
  query Cpt($id: String!) {
    cpt(id: $id) {
      message
      code
      type
      data
    }
  }
`;


export const DELETE_CPT = gql`
mutation DeleteCpt($id:String!) {
    deleteCpt(id: $id) {
        code
        message
        type
        data
    }
}
`

export const GET_ICD = gql`
  query Icds {
    icds {
        items
    }
}

`;

export const GET_DIAGNOSIS = gql`
 query Diagnosis($filters:JSON) {
    diagnoses(input: { filters: $filters }) {
        items
    }
}

`;

export const GET_SPECIALITY = gql`
 query Specialties($filters:JSON) {
    specialties(input: { filters: $filters }) {
        items
    }
}

`;

export const GET_CPTCODES = gql`
 query CptCodes($filters:JSON) {
    cptCodes(input: { filters: $filters }) {
        items
    }
}

`;