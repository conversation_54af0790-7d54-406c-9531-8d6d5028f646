/* eslint-disable @typescript-eslint/no-explicit-any */
import client from "@/lib/apollo-client";
import { CREATE_EXCEPTION, DELETE_EXCEPTION, GET_ALL_EXCEPTIONS, GET_BY_ID_EXCEPTION, UPDATE_EXCEPTION } from "./query";

export const getAllExceptions = async (payload: {
    page: number;
    limit: number;
    search: string;
    sortBy: string;
    sortOrder: string;
    filters: string;
    selectedFields:{ [key: string]: number }
  }) => {
  
    try {
      const response = await client.query({
        query: GET_ALL_EXCEPTIONS,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

  export const createExecption = async (payload: {
    input: {
            templateId: string | null
            values: string | null
            flattenedValues: { [key: string]: any }
            processId: string | null
            exceptions: string | null
    };
  }) => {
    try {
      const response = await client.mutate({
        mutation: CREATE_EXCEPTION,
        variables: payload,
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

  export const updateException = async (input: {
    input: {
      id: string | null
      templateId: string | null
      values: string | null
      flattenedValues: { [key: string]: any }
      processId: string | null
      exceptions: string | null
    }
  }) => {
  try {
    const response = await client.mutate({
      mutation: UPDATE_EXCEPTION,
      variables: input,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
  };

  export const getByIdException = async (payload: { id: string | null }) => {
    try {
      const response = await client.query({
        query: GET_BY_ID_EXCEPTION,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response;
    } catch (error: any) {
      const graphQLErrors = error?.graphQLErrors ?? [];
      const errorCode = graphQLErrors[0]?.code ?? "UNKNOWN";
      throw { code: errorCode, message: error.message };
    }
  };

  export const deleteException = async (payload: { id: string }) => {
    try {
      const response = await client.mutate({
        mutation: DELETE_EXCEPTION,
        variables: payload,
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };