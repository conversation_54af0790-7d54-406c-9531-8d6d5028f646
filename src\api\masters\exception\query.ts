import { gql } from "@apollo/client";

export const GET_ALL_EXCEPTIONS = gql`
query FindAllExceptions(
 $input: FindAllExceptionsInput!) {
    findAllExceptions(
        input: $input
  ) {
    items
    pagination {
      page
      limit
      total
      totalItems
      totalPages
      hasNext
      hasPrev
    }
  }
}`

export const CREATE_EXCEPTION = gql`
mutation CreateException($input: CreateExceptionInput!) {
    createException(input: $input) {
        message
        code
        type
        data
    }
}
`

export const GET_BY_ID_EXCEPTION = gql`
query FindExceptionById($id: ID!) {
    findExceptionById(id: $id) {
        message
        code
        type
        data
    }
}
`;

export const UPDATE_EXCEPTION = gql`
mutation UpdateException($input: UpdateExceptionInput!) {
    updateException(input: $input) {
        message
        code
        type
        data
    }
}
`
export const DELETE_EXCEPTION = gql`
mutation DeleteException($id:ID!) {
    deleteException(id: $id) {
        message
        code
        type
        data
    }
}
`