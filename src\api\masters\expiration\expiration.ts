/* eslint-disable @typescript-eslint/no-explicit-any */
import client from "@/lib/apollo-client";
import { GET_ALL_EXPIRATION, UPDATE_EXPIRATION } from "./query";

export const getAllExpiration = async (payload: {
    page: number;
    limit: number;
    search?: string;
    sortBy: string;
    sortOrder: string;
    filters: any;
    
  }) => {
  
    try {
      const response = await client.query({
        query: GET_ALL_EXPIRATION,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

  export const updateExpiration = async (payload: {
    id: string;
    input:any;
    
  }) => {
  
    try {
      const response = await client.mutate({
        mutation: UPDATE_EXPIRATION,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };
