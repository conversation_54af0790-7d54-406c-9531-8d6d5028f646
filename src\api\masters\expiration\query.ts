import { gql } from "@apollo/client";

export const GET_ALL_EXPIRATION = gql`
query GetModuleUrls(
        $filters: JSON
        $page: Int
        $limit: Int
        $sortBy: String
       $sortOrder: String
    ) {
    getModuleUrls(
        filters: $filters
        page: $page
        limit: $limit
        sortBy: $sortBy
        sortOrder: $sortOrder
    ) {
        data {
            _id
            moduleName
            importUrlTime
            backupUrlTime
            exportUrlTime
            auditUrlTime
            isImport
        }
        pagination {
            page
            limit
            total
            totalItems
            totalPages
            hasNext
            hasPrev
        }
    }
}`

export const UPDATE_EXPIRATION = gql`
mutation UpdateModuleUrl($id: String!, $input: UpdateModuleUrlInput!) {
    updateModuleUrl(id: $id, input: $input) {
        _id
        moduleName
        importUrlTime
        backupUrlTime
        exportUrlTime
        auditUrlTime
        updatedBy
        isActive
        collectionName
        createdAt
        updatedAt
    }
}`

