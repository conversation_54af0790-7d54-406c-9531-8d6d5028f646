import client from "@/lib/apollo-client";
import { GET_ALL_PAYER, CREATE_PAYER, DELETE_PAYER, GET_PAYER, UPDATE_PAYER } from "./query";

export const getAllPayers = async (payload: {
   page: number,
        limit: number,
        search?: string,
        sortBy?:string,
        sortOrder?:string,
        filters: string,
        selectedFields: { [key: string]: number }
  }) => {
    console.log('variables',payload)
    try {
      const response = await client.query({
        query: GET_ALL_PAYER,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

  export const createPayers = async (payload: {
   input: { templateId: string, values: string,flattenedValues:{ [key: string]: number } }
  }) => {
    try {
      const response = await client.mutate({
        mutation: CREATE_PAYER,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

export const deletePayer = async (payload: {
    id: string;
}) => {
  try {
    const response = await client.mutate({
      mutation:DELETE_PAYER,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};
  

export const getPayer = async (payload: {
   id: string
  }) => {
    try {
      const response = await client.query({
        query: GET_PAYER,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

  export const updatePayer = async (payload: {
    input:{
       id: string, templateId: string, values: string,flattenedValues:{ [key: string]: number }
    }
  }) => {
  try {
    const response = await client.mutate({
      mutation:UPDATE_PAYER,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};