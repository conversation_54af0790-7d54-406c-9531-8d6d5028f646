import { gql } from "@apollo/client";

export const GET_ALL_PAYER = gql`
query Payers( $page: Int
        $limit: Int
        $search: String
        $filters: String
        $sortBy: String
        $sortOrder: String
        $selectedFields: JSON) {
    payers(
        page: $page
        limit: $limit
        search: $search
        sortBy: $sortBy
        sortOrder: $sortOrder
        filters: $filters
        selectedFields: $selectedFields
    ) {
        payers 
        pagination {
            page
            limit
            total
            totalItems
            totalPages
            hasNext
            hasPrev
        }
    }
}`

export const CREATE_PAYER = gql`
mutation CreatePayer($input: CreatePayerInput!) {
    createPayer(input:$input) {
        message
        code
        type
        data
    }
}`

export const DELETE_PAYER = gql`
mutation DeletePayer($id:ID!) {
    deletePayer(id: $id) {
        message
        code
        type
        data
    }
}`

export const GET_PAYER = gql`
query Payer($id:ID!) {
    payer(id: $id) {
        message
        code
        type
        data
    }
}`

export const UPDATE_PAYER = gql`
mutation UpdatePayer($input: UpdatePayerInput!) {
    updatePayer(input:$input) {
        message
        code
        type
        data
    }
}`
