/* eslint-disable @typescript-eslint/no-explicit-any */
import client from "@/lib/apollo-client";
import { GET_ALL_PROVIDER, CREATE_PROVIDER, DELETE_PROVIDER, GET_PROVIDER, UPDATE_PROVIDER, PROVIDER_EDIT } from "./query";

export const getAllProvider = async (payload: {
             input: {
            search?: string,
            filters?: any
            sortBy?: string
            sortOrder?: string
            page: number
            limit: number
        }
  }) => {
    try {
      const response = await client.query({
        query: GET_ALL_PROVIDER,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

  export const createProviders = async (payload: {
   input: { templateId: string, values: string, isActive?: boolean,subOrganisationId: string,email?:any }
      }) => {
    try {
      const response = await client.mutate({
        mutation: CREATE_PROVIDER,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

export const deleteProvider = async (payload: {
    id: string;
}) => {
  try {
    const response = await client.mutate({
      mutation:DELETE_PROVIDER,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};
  
export const getProvider = async (payload: {
   id: string
  }) => {
    try {
      const response = await client.query({
        query: GET_PROVIDER,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

  export const updateProvider = async (payload: {input:{
    values: string,
    id: string, subOrganisationId: string,token?:string}
    }) => {
  try {
    const response = await client.mutate({
      mutation:UPDATE_PROVIDER,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};

  export const checkVaildToken = async (payload:{token:string,
    id: string}
    ) => {
  try {
    const response = await client.mutate({
      mutation:PROVIDER_EDIT,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};




