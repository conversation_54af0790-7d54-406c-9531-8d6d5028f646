import { gql } from "@apollo/client";

export const GET_ALL_PROVIDER = gql`
query Providers($input:GetProviderInput!){
 providers(input:$input) {
        providers 
        pagination {
            page
            limit
            total
            totalItems
            totalPages
            hasNext
            hasPrev
        }
    }
}`

export const CREATE_PROVIDER = gql`
mutation CreateProvider($input:CreateProviderInput!) {
    createProvider(input:$input) {
        message
        code
        type
        data
    }
}`

export const DELETE_PROVIDER = gql`
mutation DeleteProvider($id:String!) {
    deleteProvider(id: $id) {
        message
        code
        type
        data
    }
}`

export const GET_PROVIDER = gql`
query Provider($id: String!) {
    provider(id: $id) {
        message
        code
        type
        data
    }
}`

export const UPDATE_PROVIDER = gql`
mutation UpdateProvider($input:UpdateProviderInput!) {
    updateProvider(input: $input) {
        message
        code
        type
        data
    }
}`

export const PROVIDER_EDIT = gql`
mutation ValidateProviderToken($token: String! $id: String!) {
    validateProviderToken(
        token: $token
        id: $id
    ) {
        _id
        templateId
        subOrganisationId
        values
        createdBy
        updatedBy
        isActive
    }
}`