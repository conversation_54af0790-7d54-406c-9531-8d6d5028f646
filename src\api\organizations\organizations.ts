/* eslint-disable @typescript-eslint/no-explicit-any */
 import client from "@/lib/apollo-client";
import { CREATE_USER, GET_ORGANIZATION_USER, DELETE_ORGANIZATION_USER, GET_BY_ORGANIZATION, UPDATE_ORGANIZATION, GET_ORG_USERS, CREATE_ORG_USERS, GET_BY_ID_ORGUSERS, UPDATE_ORG_USERS, DELETE_ORG_USER, UPDATE_ORG_SETTINGS, GET_BY_ID_ORGSETTINGS } from "./query";
// import dayjs from "dayjs";
 
 export const getOrganizationUser = async (payload:{
  input: {
            search?: string,
            filters?: any
            sortBy?: string
            sortOrder?: string
            page?: number
     limit?: number
            type?: string;
        }
 }) => {
    try {
      const response = await client.query({
        query: GET_ORGANIZATION_USER,
        fetchPolicy: "network-only",
        variables: payload
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

  export const getByOrganization = async (payload:{clientId:any}) => {
    try {
      const response = await client.mutate({
        mutation: GET_BY_ORGANIZATION,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };


  export const createUser = async (payload: {
  input: {
    name: string ;
    email: string ;
    templateId: string ;
    values?: string ;
    type?: string;
    flattenedValues:any;
    created_by?: string;
    is2FAenabled?: boolean;
    bypass_2fa?: boolean;
    main_client?:string
  };
}) => {
  try {
    const response = await client.mutate({
      mutation: CREATE_USER,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};

export const deleteUser = async (payload: {
  input: {
    id: string;
  };
}) => {
  try {
    const response = await client.mutate({
      mutation:DELETE_ORGANIZATION_USER,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};
export const updateUser = async (payload: {
  input: {
    id: string;
    values?: string;
    flattenedValues?: any,
  };
}) => {
  try {
    const response = await client.mutate({
      mutation: UPDATE_ORGANIZATION,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};

export const getOrgUsers = async (payload: {
  page?: number ;
  limit?: number;
  search: string ;
  sortBy?: string;
  sortOrder?: string ;
  filters?: string;
  organisationId?:string 
  // selectedFields?: { [key: string]: number };
}) => {
  try {
    const response = await client.query({
      query: GET_ORG_USERS,
      variables: payload,
      fetchPolicy: "network-only",
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
};

export const createOrgUsers = async (payload: {
    input: {
            name: string
            email: string
            employeeId: string
            organisationId: string
            roleId: string
            type: string
    }
  }) => {
  try {
    const response = await client.mutate({
      mutation: CREATE_ORG_USERS,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
  };

  export const getOrgUserById = async (payload: { id: string  }) => {
    try {
      const response = await client.query({
        query: GET_BY_ID_ORGUSERS,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error: any) {
      const graphQLErrors = error?.graphQLErrors ?? [];
      const errorCode = graphQLErrors[0]?.code ?? "UNKNOWN";
      throw { code: errorCode, message: error.message };
    }
  };

  export const updateOrgUsers = async (payload: {
    input: {
            id:string
            name: string
            email: string
            employeeId: string
            organisationId: string
            roleId: string
            type: string
    }
  }) => {
  try {
    const response = await client.mutate({
      mutation: UPDATE_ORG_USERS,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
  };

  export const deleteOrgUsers = async (payload: { id: string }) => {
    try {
      const response = await client.mutate({
        mutation: DELETE_ORG_USER,
        variables: payload,
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;        
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

  export const updateOrgSettings = async (payload: {
    input: {
      id: string
      preferredCommunication: string
      accessPortal: boolean
      accessOrganisation: boolean
      expiryTime: Date
    }
  }) => {
  try {
    const response = await client.mutate({
      mutation: UPDATE_ORG_SETTINGS,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
  };

  export const getOrgSettings = async (payload: { id: string  }) => {
    try {
      const response = await client.query({
        query: GET_BY_ID_ORGSETTINGS,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error: any) {
      const graphQLErrors = error?.graphQLErrors ?? [];
      const errorCode = graphQLErrors[0]?.code ?? "UNKNOWN";
      throw { code: errorCode, message: error.message };
    }
  };
