import { gql } from "@apollo/client";

export const GET_ORGANIZATION_USER = gql`
query GetUsersWithPagination($input:GetUsersInput!) {
    getUsersWithPagination(input:$input) {
        users 
        pagination {
            page
            limit
            total
            totalItems
            totalPages
            hasNext
            hasPrev
        }
    }
}`

export const GET_ORGANIZATION_PAGINATION = gql`
query GetUsersWithPagination($input:GetUsersInput!) {
    getOrganisationsWithPagination(input:$input) {
        users 
        pagination {
            page
            limit
            total
            totalItems
            totalPages
            hasNext
            hasPrev
        }
    }
}`

export const CREATE_USER = gql`
  mutation CreateUser($input: CreateUserInput!) {
    createUser(input: $input) {
      message
      code
      type
      data
    }
  }
`;

export const DELETE_ORGANIZATION_USER = gql`
mutation DeleteClient($input:DeleteClientInput!) {
    deleteClient(input: $input) {
        message
        code
        type
        data
    }
}
`
export const GET_BY_ORGANIZATION = gql`
mutation GetByClient($clientId: String!) {
    getByClient(clientId: $clientId) {
        message
        code
        type
        data
    }
}`
export const UPDATE_ORGANIZATION = gql`
mutation UpdateClient($input: UpdateClientInput!) {
    updateClient(input: $input) {
        message
        code
        type
        data
    }
}`
export const GET_SUB_ORGANIZATION = gql`
mutation GetByClients($input:GetClientsByMainClientInput!) {
    getByClients(input:$input ) {
        message
        code
        type
        data
    }
}`

export const GET_ORG_USERS = gql`
  query OrganisationUsers(
    $page: Int
    $limit: Int
    $search: String
    $filters: JSONObject
    $sortBy: String
    $sortOrder: String
    $organisationId:String
    $type:OrganisationType
  ) {
    organisationUsers(
      page: $page
      limit: $limit
      search: $search
      filters: $filters
      sortBy: $sortBy
      sortOrder: $sortOrder
      organisationId:$organisationId
      type:$type
    ) {
      pagination
        items {
            _id
            name
            email
            employeeId
            organisationId
            roleId
            type
            isActive
            createdAt
            updatedAt
        }
            }
      
    
  }
`;

export const CREATE_ORG_USERS = gql`
mutation CreateOrganisationUser($input:CreateOrganisationUserInput!){
    createOrganisationUser(input:$input) {
        _id
        name
        email
        employeeId
        organisationId
        roleId
        type
        isActive
        createdAt
        updatedAt
    }
}
`
export const GET_BY_ID_ORGUSERS = gql`
query OrganisationUser($id:String!){
    organisationUser(id:$id) {
        _id
        name
        email
        employeeId
        organisationId
        roleId
        roleName
        type
        isActive
        createdAt
        updatedAt
    }
}
`

export const UPDATE_ORG_USERS = gql`
mutation UpdateOrganisationUser($input:UpdateOrganisationUserInput!){
    updateOrganisationUser(input:$input) {
        _id
        name
        email
        employeeId
        organisationId
        roleId
        type
        isActive
        createdAt
        updatedAt
    }
}
`
export const DELETE_ORG_USER = gql`
mutation RemoveOrganisationUser($id:String! $type:String!) {
    removeOrganisationUser(id: $id type:$type) {
        _id
        name
        email
        employeeId
        organisationId
        roleId
        roleName
        type
        isActive
        createdAt
        updatedAt
    }
}
`
export const UPDATE_ORG_SETTINGS = gql`
mutation UpdateOrganisationSettings($input:UpdateOrganisationSettingsInput!) {
    updateOrganisationSettings(input:$input) {
        _id
        organisationId
        preferredCommunication
        accessPortal
        accessOrganisation
        expiryTime
        createdAt
        updatedAt
    }
}
`
export const GET_BY_ID_ORGSETTINGS = gql`
query OrganisationSettings($id:String!){
    organisationSettingsByOrganisationId(organisationId:$id) {
        _id
        organisationId
        preferredCommunication
        accessPortal
        accessOrganisation
        expiryTime
        createdAt
        updatedAt
    }
}
`