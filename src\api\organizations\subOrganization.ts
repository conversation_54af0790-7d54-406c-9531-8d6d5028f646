 import client from "@/lib/apollo-client";
import { GET_SUB_ORGANIZATION} from "./query";

 
 export const getSubOrganizationUser = async (payload:{input:{mainClientId:string,search?: string,
            filters?: string
            sortBy?: string
            sortOrder?: string
            page: number
            limit: number}}) => {
    try {
      const response = await client.mutate({
        mutation: GET_SUB_ORGANIZATION,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };
