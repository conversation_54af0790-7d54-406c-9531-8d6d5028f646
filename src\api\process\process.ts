import client from "@/lib/apollo-client";
import { GET_PROCESS, GET_ALL_PROCESS_SETTINGS, GET_ORG_PROCESS, UPDATE_ORG_PROCESS,OPEARTION_CREATE, DELETE_OPERATION, DELETE_AUDIT, DELETE_MANAGEMENT, MANAGEMENT_CREATE, AUDIT_CREATE } from "./query";

export const getAllProcess = async () => {
    try {
      const response = await client.query({
        query: GET_PROCESS,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

  export const getOrgProcess = async (payload:{organisationId:string, subOrganisationId:string}) => {
    try {
      const response = await client.query({
        query: GET_ORG_PROCESS,
        fetchPolicy: "network-only",
        variables:payload
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };



  export const getAllProcessSettings = async (payload:{
    processId: string,
        organisationId: string,
        subOrganisationId: string
  }) => {
    try {
      const response = await client.query({
        query: GET_ALL_PROCESS_SETTINGS,
        variables:payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

    export const updateProcessSettings = async (payload:{
        id: string,
        isActive: boolean,
  }) => {
    try {
      const response = await client.mutate({
        mutation: UPDATE_ORG_PROCESS,
        variables:payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };


      export const operationCreate = async (payload:{
       input:{
        orgId:string
            organisationId: string,
            subOrganisationId: string,
            processId: string,
            managerId?: string,
            supervisorId?: string,
            agentId?: string,
            qcManagerId?: string, 
            qcSupervisorId?: string, 
            qcAgentId?: string,
            adminId?: string, 
            subAdminId?: string
        }
  },title:string) => {
    try {
      const response = await client.mutate({
         mutation: title.includes('Management')? MANAGEMENT_CREATE: title.includes("Audit") ?AUDIT_CREATE :OPEARTION_CREATE,
        variables:payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };
      export const deleteCreate = async (payload:{
       input:{
            organisationId: string,
            subOrganisationId: string
            processId: string,
            managerId?: string,
            supervisorId?: string,
            agentId?: string,
            qcManagerId?: string, 
            qcSupervisorId?: string, 
            qcAgentId?: string,
            adminId?: string, 
            subAdminId?: string
        }
  },title:string) => {
    try {
      const response = await client.mutate({
        mutation: title.includes('Management')? DELETE_MANAGEMENT: title.includes("Audit") ?DELETE_AUDIT :DELETE_OPERATION,
        variables:payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };


  