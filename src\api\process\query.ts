
import { gql } from "@apollo/client";

export const GET_PROCESS = gql`
query Processes {
    processes(sortBy: "_id" sortOrder: "asc") {
        message
        code
        type
        data
    }
}
`
export const GET_ALL_PROCESS_SETTINGS = gql`
query ProcessAssignmentsByCategory( $processId: String!
        $organisationId: String
        $subOrganisationId: String) {
    processAssignmentsByCategory(
        processId: $processId
        organisationId: $organisationId
        subOrganisationId: $subOrganisationId
    ) {
        processId
        isActive
        users {
            category
            hierarchies
        }
    }
}
`
export const GET_ORG_PROCESS = gql`
query OrganisationProcessMappings($organisationId:String  $subOrganisationId:String) {
    organisationProcessMappings(organisationId: $organisationId, subOrganisationId: $subOrganisationId) {
        _id
        organisationId
        subOrganisationId
        processId
        processName
        isActive
    }
}`

export const UPDATE_ORG_PROCESS = gql`
mutation UpdateOrganisationProcessMappingIsActive($id: String!, $isActive: Boolean!) {
    updateOrganisationProcessMappingIsActive(id: $id, isActive: $isActive) {
        _id
        organisationId
        subOrganisationId
        processId
        processName
        isActive
    }
}`
export const OPEARTION_CREATE = gql`
mutation AssignOperationsRole($input:AssignOperationsRoleInput!) {
    assignOperationsRole(
        input: $input
    )
}`

export const AUDIT_CREATE = gql`
mutation AssignAuditRole($input:AssignAuditRoleInput!) {
    assignAuditRole(
        input: $input
    )
}`

export const MANAGEMENT_CREATE = gql`
mutation AssignManagementsRole($input:AssignManagementsRoleInput!) {
    assignManagementsRole(
        input: $input
    )
}`


export const DELETE_OPERATION = gql`
mutation RemoveOperationsRole($input:RemoveOperationsRoleInput!) {
    removeOperationsRole(
        input: $input
    )
}`
export const DELETE_AUDIT = gql`
mutation RemoveAuditRole($input:RemoveAuditRoleInput!) {
    removeAuditRole(
        input: $input
    )
}`
export const DELETE_MANAGEMENT = gql`
mutation RemoveManagementsRole($input:RemoveManagementsRoleInput!) {
    removeManagementsRole(
        input: $input
    )
}`