 import client from "@/lib/apollo-client";
import { GET_TEMPLATE} from "./query";

 
 export const getTemplates = async (payload:{search:string,filters?:object}) => {
    try {
      const response = await client.query({
        query: GET_TEMPLATE,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };
