import { gql } from "@apollo/client";

export const GET_SOURCE_LIST = gql`
query ProviderEmailTickets($input:PaginateProviderEmailTicketArgs!) {
    providerEmailTickets(input:$input) {
        providerEmailTickets 
        pagination {
            page
            limit
            total
            totalItems
            totalPages
            hasNext
            hasPrev
        }
    }
}`

export const GET_BY_SOURCE = gql`
query ProviderEmailTicket($id:ID!) {
    providerEmailTicket(id:$id) {
       _id
        subject
        type
        ticket_type
        from
        to
        priority
        description
        createdAt
        updatedAt
        created_by
        values
        conversationId
        status
        messages {
            from
            to
            body
            date
            message
            messageId
            reason
            attachments {
                fileName
                url
                uploadedAt
                type
            }
        }
    }
}
`

export const UPDATE_PROVIDEREMAILTICKET = gql`
mutation UpdateProviderEmailTicket($input:UpdateProviderEmailTicketInput!) {
    updateProviderEmailTicket(input:$input) {
        message
        code
        type
        data
    }
}
`

export const CREATE_PROVIDERMAILTICKET = gql`
mutation CreateProviderTicket($input:CreateProviderTicketInput!) {
    createProviderTicket(input: $input) {
        message
        code
        type
        data
    }
}
`

export const REPLY_EMAIL_THREAD = gql`
mutation ReplyToEmailThread($input:ReplyToEmailTicketInput!) {
    replyToEmailThread(input:$input) {
        message
        code
        type
        data
    }
}
`

export const PUSHNOTIFICATION_ADD = gql`
mutation AddNotificationToken($token:[String!]!) {
    addNotificationToken(token: $token)
}`