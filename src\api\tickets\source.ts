/* eslint-disable @typescript-eslint/no-explicit-any */
import client from "@/lib/apollo-client";
import { CREATE_PROVIDERMAILTICKET, GET_BY_SOURCE, GET_SOURCE_LIST, PUSHNOTIFICATION_ADD, REPLY_EMAIL_THREAD, UPDATE_PROVIDEREMAILTICKET } from "./query";
// import { FieldValue } from "@/app/(protected)/tickets/Source/ticketView";

export const getSourceList = async (payload:{input:{
    search?: string,
    filters?: string
    sortBy?: string
    sortOrder?: string
    page: number
    limit: number}}) => {
try {
const response = await client.mutate({
mutation: GET_SOURCE_LIST,
variables: payload,
fetchPolicy: "network-only",
});
return response.data;
} catch (error) {
if (error instanceof Error && "graphQLErrors" in error) {
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
const errorCode = graphQLErrors[0]?.code;
throw { code: errorCode, message: error.message };
} else {
throw error;
}
}
};

export const getSourceById = async (payload: { id: string  }) => {
    try {
      const response = await client.query({
        query: GET_BY_SOURCE,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error: any) {
      const graphQLErrors = error?.graphQLErrors ?? [];
      const errorCode = graphQLErrors[0]?.code ?? "UNKNOWN";
      throw { code: errorCode, message: error.message };
    }
  };

  export const UpdateProviderTicket = async (input: {
    input: {
        id:string 
        status?: string
        reason?:string
        priority?:string
    };
  }) => {
    try {
      const response = await client.mutate({
        mutation: UPDATE_PROVIDEREMAILTICKET,
        variables: input,
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

  export const createProviderTicket = async (input: {
    input: {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      source_ticket_id:string
      flattenedValues: any
      type: string
      templateId: string
      status: string
      // ticketId: string
    };
  }) => {
    try {
      const response = await client.mutate({
        mutation: CREATE_PROVIDERMAILTICKET,
        variables: input,
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

  export const addPushNotificationToken = async (input: { token: string[] }) => {
    try {
      const response = await client.mutate({
        mutation: PUSHNOTIFICATION_ADD,
        variables: input,
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

  export const replyEmailThread = async (input: {
    input: {
      conversationId: string
      from: string
      to: string
      body: string 
      attachments?:any[]
      messageId:string

    };
  }) => {
    try {
      const response = await client.mutate({
        mutation: REPLY_EMAIL_THREAD,
        variables: input,
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };