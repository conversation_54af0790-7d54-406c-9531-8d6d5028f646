/* eslint-disable @typescript-eslint/no-explicit-any */
import client from "@/lib/apollo-client";
import { CREATE_ORGROLES, DELETE_ORGROLES, GET_ORG_LIST, ORGROLES_GET_ID, UPDATE_ORG_ROLES } from "./query";
import { Module } from "@/app/(protected)/user-management/organization-roles/orgRolePermissionForm";

export const getOrgList = async (payload?: {
    page: number | null;
    limit: number | null;
    search: string | null;
    sortBy: string | null;
    sortOrder: string | null;
    filters?: string | null;
    selectedFields?: { [key: string]: number };
  }) => {
    try {
      const response = await client.query({
        query: GET_ORG_LIST,
        variables:{ input: payload },
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      interface GraphQLError {
        message: string;
        code?: string;
        [key: string]: unknown;
      }
      if (error instanceof Error && "graphQLErrors" in error) {
        const graphQLErrors = (error as { graphQLErrors: GraphQLError[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }


    
  };

  export const createOrgRoles = async (input: {
    input: {
        name: string | null
        type: string,
        isActive: boolean
    };
  }) => {
    try {
      const response = await client.mutate({
        mutation: CREATE_ORGROLES,
        variables: input,
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

  export const deleteOrgRoles = async (payload: { id: string }) => {
      try {
        const response = await client.mutate({
          mutation: DELETE_ORGROLES,
          variables: payload,
        });
        return response.data;
      } catch (error) {
        if (error instanceof Error && "graphQLErrors" in error) {
          const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
          const errorCode = graphQLErrors[0]?.code;
          throw { code: errorCode, message: error.message };
        } else {
          throw error;
        }
      }
    };

    export const getByIdOrgRoles = async (payload: { id: string  }) => {
      try {
        const response = await client.query({
          query: ORGROLES_GET_ID,
          variables: payload,
          fetchPolicy: "network-only",
        });
        return response.data;
      } catch (error: any) {
        const graphQLErrors = error?.graphQLErrors ?? [];
        const errorCode = graphQLErrors[0]?.code ?? "UNKNOWN";
        throw { code: errorCode, message: error.message };
      }
    };

    export const updateOrgRoles = async (payload: {
        input: {
          id:string | null,
          permissions:Module[],
          type:string | null,
          name:string|null
        }
      }) => {
      try {
        const response = await client.mutate({
          mutation: UPDATE_ORG_ROLES,
          variables: payload,
        });
        return response.data;
      } catch (error) {
        if (error instanceof Error && "graphQLErrors" in error) {
          const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
          const errorCode = graphQLErrors[0]?.code;
          throw { code: errorCode, message: error.message };
        } else {
          throw error;
        }
      }
      };