import { gql } from "@apollo/client";

export const GET_ORG_LIST = gql`
  query OrganisationRoles($input: OrganisationRoleListArgs) {
    organisationRoles(input: $input) {
      pagination
      items {
        _id
        name
        type
        permissions
        isActive
        createdAt
            updatedAt
      }
    }
  }
`;

export const CREATE_ORGROLES = gql`
mutation CreateOrganisationRole($input:CreateOrganisationRoleInput!) {
    createOrganisationRole(input:$input) {
        _id
        name
        permissions
        isActive
        type
        updatedAt
        createdAt
    }
}
`

export const DELETE_ORGROLES = gql`
mutation RemoveOrganisationRole($id:ID!) {
    removeOrganisationRole(id: $id) {
        _id
        name
        permissions
        isActive
        type
        updatedAt
        createdAt
    }
}
`
export const ORGROLES_GET_ID = gql`
query OrganisationRole($id:ID!) {
    organisationRole(id: $id) {
        _id
        name
        permissions
        isActive
        type
        updatedAt
        createdAt
    }
}
`

export const UPDATE_ORG_ROLES = gql`
mutation UpdateOrganisationRole(
$input:UpdateOrganisationRoleInput!
) {
    updateOrganisationRole(input: $input){
      _id
        name
    }
}`