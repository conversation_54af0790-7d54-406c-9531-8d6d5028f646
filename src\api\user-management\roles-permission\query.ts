import { gql } from "@apollo/client";

export const GET_ROLESLIST = gql`
  query Roles(
   $page: Int
    $limit: Int
    $search: String
    $filters: JSON
    $sortBy: String
    $sortOrder: String
  ) {
    roles(
    page: $page
      limit: $limit
      search: $search
      filters: $filters
      sortBy: $sortBy
      sortOrder: $sortOrder
      ) {
        pagination
        items {
            _id
            name
            category
            createdAt
            updatedAt
            isActive
        }
    }
}

`;

export const ROLES_GETBY_ID = gql`
query Role($id:String!) {
    role(id: $id) {
        _id
        name
        category
        createdAt
        updatedAt
        permissions
    }
}
`

export const UPDATE_ROLES_PERMISSION = gql`
mutation UpdateRolePermissions($input:UpdateRolePermissionsInput!) {
    updateRolePermissions(input: $input)
}
`