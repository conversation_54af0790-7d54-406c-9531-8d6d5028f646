/* eslint-disable @typescript-eslint/no-explicit-any */
import client from "@/lib/apollo-client";
import { GET_ROLESLIST, ROLES_GETBY_ID, UPDATE_ROLES_PERMISSION } from "./query";
import { Module } from "@/app/(protected)/user-management/roles-managements/rolePermissionForm";

export const getRoles = async (payload: {
    page: number | null;
    limit: number | null;
    search: string | null;
    sortBy: string | null;
    sortOrder: string | null;
    filters?: string | null;
    selectedFields?: { [key: string]: number };
  }) => {
    try {
      const response = await client.query({
        query: GET_ROLESLIST,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      interface GraphQLError {
        message: string;
        code?: string;
        [key: string]: unknown;
      }
      if (error instanceof Error && "graphQLErrors" in error) {
        const graphQLErrors = (error as { graphQLErrors: GraphQLError[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

  export const getByIdRoles = async (payload: { id: string  }) => {
    try {
      const response = await client.query({
        query: ROLES_GETBY_ID,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error: any) {
      const graphQLErrors = error?.graphQLErrors ?? [];
      const errorCode = graphQLErrors[0]?.code ?? "UNKNOWN";
      throw { code: errorCode, message: error.message };
    }
  };

  export const updateRolesPermissions = async (payload: {
    input: {
      roleId:string | null,
      permissions:Module[]
    }
  }) => {
  try {
    const response = await client.mutate({
      mutation: UPDATE_ROLES_PERMISSION,
      variables: payload,
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && "graphQLErrors" in error) {
      const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
      const errorCode = graphQLErrors[0]?.code;
      throw { code: errorCode, message: error.message };
    } else {
      throw error;
    }
  }
  };