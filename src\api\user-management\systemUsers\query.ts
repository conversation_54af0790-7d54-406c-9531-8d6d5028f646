import { gql } from "@apollo/client";

export const GET_SYSTEMUSERS = gql`
  query SystemUsers(
    $page: Int
    $limit: Int
    $search: String
    $filters: JSON
    $sortBy: String
    $sortOrder: String
  ) {
    systemUsers(
      page: $page
      limit: $limit
      search: $search
      filters: $filters
      sortBy: $sortBy
      sortOrder: $sortOrder
    ) 
      
    
  }
`;


export const CREATE_SYSTEMUSERS = gql`
mutation CreateSystemUser($input:CreateSystemUserInput!) {
    createSystemUser(input:$input) {
        email
        employeeId
        roleName
        profileImage
        isActive
        createdAt
        updatedAt
        createdBy
        updatedBy
        id
        name
    }
}
`

export const UPDATE_SYSTEMUSERS = gql`
mutation UpdateSystemUser($input:UpdateSystemUserInput!) {
    updateSystemUser(input:$input) {
        email
        employeeId
        roleName
        profileImage
        isActive
        id
        name
    }
}
`

export const DELETE_SYSTEMUSERS = gql`
mutation RemoveSystemUser($id:String!) {
    removeSystemUser(id: $id) {
        id
        name
        email
        employeeId
        roleName
        profileImage
        isActive
        createdAt
        updatedAt
        createdBy
        updatedBy
    }
}
`

export const GET_SYSTEMUSERS_BY_ID = gql`
  query SystemUser($id:String!) {
    systemUser(id: $id) {
        id
        name
        email
        employeeId
        roleName
    }
}`;