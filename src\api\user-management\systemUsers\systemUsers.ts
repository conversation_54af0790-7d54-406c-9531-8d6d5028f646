/* eslint-disable @typescript-eslint/no-explicit-any */
import client from "@/lib/apollo-client";
import { CREATE_SYSTEMUSERS, DELETE_SYSTEMUSERS, GET_SYSTEMUSERS, GET_SYSTEMUSERS_BY_ID, UPDATE_SYSTEMUSERS } from "./query";

export const getSystemUsers = async (payload: {
    page?: number | null;
    limit?: number | null;
    search: string | null;
    sortBy?: string | null;
    sortOrder?: string | null;
    filters?: string | null;
    // selectedFields?: { [key: string]: number };
  }) => {
    try {
      const response = await client.query({
        query: GET_SYSTEMUSERS,
        variables: payload,
        fetchPolicy: "network-only",
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };
  
  export const createSystemUser = async (input: {
    input: {
        name: string | null
        email: string | null
        employeeId: string | null
        roleName: string | null
    };
  }) => {
    try {
      const response = await client.mutate({
        mutation: CREATE_SYSTEMUSERS,
        variables: input,
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

  export const updateSystemUser = async (input: {
    input: {
        id:string | null
        name: string | null
        email: string | null
        employeeId: string | null
        roleName: string | null
    };
  }) => {
    try {
      const response = await client.mutate({
        mutation: UPDATE_SYSTEMUSERS,
        variables: input,
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

  export const deleteSystemUsers = async (payload: { id: string }) => {
    try {
      const response = await client.mutate({
        mutation: DELETE_SYSTEMUSERS,
        variables: payload,
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && "graphQLErrors" in error) {
        const graphQLErrors = (error as { graphQLErrors: any[] }).graphQLErrors;
        const errorCode = graphQLErrors[0]?.code;
        throw { code: errorCode, message: error.message };
      } else {
        throw error;
      }
    }
  };

  export const getSystemUserById = async (payload: { id: string  }) => {
      try {
        const response = await client.query({
          query: GET_SYSTEMUSERS_BY_ID,
          variables: payload,
          fetchPolicy: "network-only",
        });
        return response.data;
      } catch (error: any) {
        const graphQLErrors = error?.graphQLErrors ?? [];
        const errorCode = graphQLErrors[0]?.code ?? "UNKNOWN";
        throw { code: errorCode, message: error.message };
      }
    };