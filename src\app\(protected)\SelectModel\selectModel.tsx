/* eslint-disable @typescript-eslint/no-explicit-any */
// MySelectModal.tsx
import React, { useEffect, useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
} from "@mui/material";
import { selectHeaders } from "@/api/header/header";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { setProcess, setRole } from "@/features/headers/headersSlice";
import Image from "next/image";
import logo from "../../../assests/rcm-logo.png";
import { useRouter } from "next/navigation";
import Cookies from "js-cookie";
type Props = {
  open: boolean;
  onClose: () => void;
  onSave: (values: {
    select1: string;
    select2: string;
    select3: string;
  }) => void;
};

const MySelectModal: React.FC<Props> = ({ open, onClose }) => {
  const user = useSelector((state: RootState) => state.user.id);
  const router = useRouter();
  const [select1, setSelect1] = useState([]);
  const [select2, setSelect2] = useState([]);
  const [select3, setSelect3] = useState([]);
  const [loader, setLoader] = useState(true);
  const [query, setQuery] = useState({
    name: "organisation",
    userId: user,
    organisationId: "",
    subOrganisationId: "",
  });
  const dispatch = useDispatch();

  useEffect(() => {
    setLoader(true);
    selectHeaders(query)
      .then((res) => {
        console.log(res.selectHeader);
        if (query.name == "organisation") {
          setSelect1(res.selectHeader);
        } else if (query.name == "suborganisation") {
          setSelect2(res.selectHeader);
        } else {
          setSelect3(res.selectHeader);
        }
        setLoader(false);
      })
      .catch(() => {
        setLoader(false);
        console.log("error");
      });
  }, [query]);

  useEffect(() => {
    if (!loader && select1.length === 0) {
      router.push("/access-denied");
      onClose();
    }
  }, [select1, loader, router, onClose]);

  return (
    <Dialog open={open} fullWidth>
      <DialogTitle>
        <div className="flex justify-center">
          {" "}
          <Image src={logo} alt="asp-rcm" width={100} height={100} />
        </div>
        <h6>Select the Process</h6>
      </DialogTitle>
      <DialogContent className="mb-5">
        <Box display="flex" flexDirection="column" gap={2} mt={1}>
          <FormControl fullWidth>
            <InputLabel id="select1-label">Organization</InputLabel>
            <Select
              labelId="select1-label"
              value={query.organisationId}
              label="Organization"
              onChange={(e) => {
                setQuery({
                  ...query,
                  ["organisationId"]: e.target.value,
                  ["name"]: "suborganisation",
                });
                dispatch(setProcess({ organizationID: e.target.value }));
                
              }}
            >
              <MenuItem value="">
                <em>None</em>
              </MenuItem>
              {select1.map((item: { id: string; value: string ,organisationId:string}, i) => (
                <MenuItem onClick={()=>{
                  Cookies.set("orgId", item.organisationId, { expires: 1 });
                }} value={item.id} key={i}>
                  {item.value}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl fullWidth>
            <InputLabel id="select2-label">Sub-Organization</InputLabel>
            <Select
              labelId="select2-label"
              value={query.subOrganisationId}
              label="Sub-Organization"
              disabled={query.organisationId == ""}
              onChange={(e) => {
                setQuery({
                  ...query,
                  ["subOrganisationId"]: e.target.value,
                  ["name"]: "process",
                });
                dispatch(setProcess({ subOrganizationId: e.target.value }));
                Cookies.set("subOrgId", e.target.value, { expires: 1 });
              }}
            >
              <MenuItem value="">
                <em>None</em>
              </MenuItem>
              {select2.map((item: { id: string; value: string }, i) => (
                <MenuItem value={item.id} key={i}>
                  {item.value}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl fullWidth>
            <InputLabel id="select3-label">Process</InputLabel>
            <Select
              labelId="select3-label"
              disabled={query.subOrganisationId == ""}
              label="Process"
              onChange={(e) => {
                const selected: any = e.target.value;
                console.log("Selected Process:", selected);

                dispatch(setRole(selected));
                dispatch(setProcess({ processId: selected.id }));
                Cookies.set("processId", selected.id, { expires: 1 });
                onClose();
              }}
            >
              <MenuItem value="">
                <em>None</em>
              </MenuItem>
              {select3.map((item: any, i) => (
                <MenuItem value={item} key={i}>
                  {item.value}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default MySelectModal;
