'use client'
import { Accordion, AccordionDetails, AccordionSummary, Box, FormControlLabel, Radio, RadioGroup, Typography } from '@mui/material'
import React from 'react'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { useRouter } from 'next/navigation';


const Page = () => {
  const router = useRouter();
  return (
    //  <div className="py-4 space-y-8 bg-[white] !rounded-[16px] !h-full">
      <Box sx={{ width: '100%'}}>
    <Accordion defaultExpanded  className='!mt-3'>
             <AccordionSummary expandIcon={<ExpandMoreIcon />} className='!mb-0 !bg-[#7592F90D] !min-h-[40px]' sx={{'& .MuiAccordionSummary-content':{
                marginY:"0px !important"
              }}}>
                 <Typography variant="h6" sx={{ color: "#2B3674", fontWeight: 600, fontSize: '16px' }} className='!my-3'>
            {"Notification"}
          </Typography>
              </AccordionSummary>
                            <AccordionDetails>
 <Typography variant="h6" sx={{ color: "#2B3674", fontWeight: 600, fontSize: '16px' }} className='!my-3'>
            {"Select Type"}
          </Typography>
          <RadioGroup
            row
            value={'Email Notification'}
            // onChange={(e) => setOrgAccess(e.target.value)}
          >
            <FormControlLabel value="Email Notification" control={<Radio />} label="Email Notification" />
            <FormControlLabel value="In App Notification" control={<Radio />} label="In App Notification" />
          </RadioGroup>

            <div className="flex justify-end gap-2">
                 <button
                   className="px-4 py-2 rounded bg-gray-400 text-white w-[85px]"
                 onClick={() => { router.push('/exports') }}
                 >
                   Cancel
                 </button>
                 <button
                   className={`px-4 py-2 rounded bg-teal-500 text-white w-[85px]`}
                  //  onClick={() => handleSubmit()}
                  //  disabled={visibleCount==0}
                 >
                   Save
                 </button>
               </div>
                            </AccordionDetails>
               </Accordion>
      </Box>
    //  </div>
  )
}

export default Page
