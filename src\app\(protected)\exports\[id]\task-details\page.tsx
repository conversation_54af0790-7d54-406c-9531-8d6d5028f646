/* eslint-disable @typescript-eslint/no-explicit-any */
'use client'
import React, { useEffect, useState } from 'react'
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
} from '@mui/lab';
import { Paper, Typography, Chip, Box, Accordion, AccordionSummary, AccordionDetails, Table, TableBody, TableCell, TableContainer, TableRow, Button } from '@mui/material';
import { Stack } from "@mui/material";
import { capitalizeFirstLetter, fetchImageUrl, formatDate, toNormalCase } from '@/utils/generic';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { exportLogs } from '@/api/exportAction/export';
import { useParams, useRouter } from 'next/navigation';

// const backupUrls = {
//   specialty: {
//     url: "https://storage.googleapis.com/asp-rcm-dev-documents-bucket/backup/Imp-875727/complete_specialty_backup.json",
//     format: "json",
//     recordCount: 5,
//     timestamp: "2025-07-28T09:16:17.357Z",
//     status: "success",
//   },
//   icd: {
//     url: "https://storage.googleapis.com/asp-rcm-dev-documents-bucket/backup/Imp-875727/complete_icd_backup.json",
//     format: "json",
//     recordCount: 5,
//     timestamp: "2025-07-28T09:16:17.664Z",
//     status: "success",
//   },
//   diagnosis: {
//     url: "https://storage.googleapis.com/asp-rcm-dev-documents-bucket/backup/Imp-875727/complete_diagnosis_backup.json",
//     format: "json",
//     recordCount: 5,
//     timestamp: "2025-07-28T09:16:17.966Z",
//     status: "success",
//   },
//   cpt_code: {
//     url: "https://storage.googleapis.com/asp-rcm-dev-documents-bucket/backup/Imp-875727/complete_cpt_code_backup.json",
//     format: "json",
//     recordCount: 5,
//     timestamp: "2025-07-28T09:16:18.271Z",
//     status: "success",
//   },
//   cpts: {
//     url: "https://storage.googleapis.com/asp-rcm-dev-documents-bucket/backup/Imp-875727/complete_cpts_backup.json",
//     format: "json",
//     recordCount: 5,
//     timestamp: "2025-07-28T09:16:18.571Z",
//     status: "success",
//   },
// };

const Page = () => {
  const {id} = useParams();
  const router = useRouter();
  const [exportData, setExportData]=useState<any>({})
  const[open,setOpen]=useState(false)
  const [errorData, setErrorData]=useState([])

  useEffect(()=>{
    const payload:any={
      taskId:id??""
    }
  exportLogs(payload).then((res)=>{
  setExportData(res.exportStatus)
  setErrorData(res.exportStatus.failedRows)
  console.log(res.exportStatus.statusLog)
})
  },[id])
console.log('exportData',exportData);

type BackupValue = {
  url: string;
  format: string;
  recordCount: number;
  timestamp: string;
  status: string;
};

  return (
     <Box sx={{ width: '100%'}}>
     
      <div
                    className="text-sm font-200 text-gray-700  pl-4 m-0 rounded flex items-end !justify-end w-full"
                  >
                   <Button className="font-normal !h-[40px] !mt-0 text-[12px] !w-[75px] !shadow-none" onClick={()=>{
       if(typeof window !== 'undefined')
         {window.history.back();}
     }}>Back</Button>
                  </div>
    
      <Accordion defaultExpanded  className='!mt-3' elevation={0}>
             <AccordionSummary expandIcon={<ExpandMoreIcon />} className='!mb-0 !bg-[#7592F90D] !min-h-[40px]' sx={{'& .MuiAccordionSummary-content':{
                marginY:"0px !important"
              }}}>
                 <Typography variant="h6" sx={{ color: "#2B3674", fontWeight: 600, fontSize: '16px' }} className='!my-3'>
            {"Task Information"}
          </Typography>
              </AccordionSummary>
                            <AccordionDetails>
                              <Paper
      elevation={0}
      sx={{
        borderRadius: 1,
        p: 2,
        width: '100%',
        overflowX: 'auto',
      }}
    >
                             <Stack
                             display={'flex'}
                             justifyContent={'space-between'}
        direction={'row'}
        // spacing={2}
        flexWrap="wrap"
      >
        <Stack>
          <Typography >
            Task ID
          </Typography>
          <Typography variant="subtitle2" color="textSecondary">{exportData.taskId}</Typography>
        </Stack>
 {exportData?.type=='import' &&
 <><Stack>
          <Typography>
            Stop on Error
          </Typography>
          <Typography variant="subtitle2" color="textSecondary">{(exportData.isStopOnError?'Yes':'No')}</Typography>
        </Stack>
         </>}
        <Stack>
          <Typography>
            Submission Time
          </Typography>
          <Typography variant="subtitle2" color="textSecondary">{formatDate(exportData.createdAt)}</Typography>
        </Stack>
      { exportData?.downloadUrl && (
        <Stack>
        <Typography>
          Uploaded File
        </Typography>
        <Typography onClick={()=>fetchImageUrl(exportData.downloadUrl)} className='cursor-pointer' variant="subtitle2" color="blue">{exportData.downloadUrl}</Typography>
      </Stack>
      )}
        

        <Stack>
          <Typography>
            Initiated By
          </Typography>
          <Typography variant="subtitle2" color="textSecondary">{exportData.createdBy}</Typography>
        </Stack>

        <Stack>
          <Typography >
            Source
          </Typography>
          <Typography variant="subtitle2" color="textSecondary">{capitalizeFirstLetter(exportData.collectionName)}</Typography>
        </Stack>
     </Stack>
    </Paper>
                            </AccordionDetails>
                             </Accordion>

{exportData?.backupUrls && (
   <Accordion defaultExpanded  className='!mt-3' elevation={0}>
   <AccordionSummary expandIcon={<ExpandMoreIcon />} className='!mb-0 !bg-[#7592F90D] !min-h-[40px]' sx={{'& .MuiAccordionSummary-content':{
           marginY:"0px !important"
         }}}>
            <Typography variant="h6" sx={{ color: "#2B3674", fontWeight: 600, fontSize: '16px' }} className='!my-3'>
       {"Backup URLs"}
     </Typography>
 </AccordionSummary>

 <AccordionDetails>
   <TableContainer sx={{ maxHeight: 300 }}  className='w-[50%]'>
     <Table stickyHeader size="small"  className='!w-[50%] border border-gray-200' >
       {/* <TableHead>
         <TableRow>
          
         </TableRow>
       </TableHead> */}
       <TableBody>
        <TableRow className='w-[50%]' >
         <TableCell><strong>Key</strong></TableCell>
           <TableCell><strong>Download URL</strong></TableCell></TableRow>
       {Object.entries(exportData?.backupUrls || {}).map(([key, value]) => {
  const backup = value as BackupValue;
  return (
    <TableRow key={key}>
      <TableCell>{key}</TableCell>
      <TableCell>
        <Typography
          onClick={() => fetchImageUrl(backup.url)}
          className="cursor-pointer"
          variant="subtitle2"
          color="primary"
          sx={{ textDecoration: "underline" }}
        >
          {'Click to download'}
        </Typography>
      </TableCell>
    </TableRow>
  );
})}
       </TableBody>
     </Table>
   </TableContainer>
 </AccordionDetails>
</Accordion>
)}
                            
    
    <Accordion defaultExpanded  className='!mt-3' elevation={0}>
             <AccordionSummary expandIcon={<ExpandMoreIcon />} className='!mb-0 !bg-[#7592F90D] !min-h-[40px] flex justify-between' sx={{'& .MuiAccordionSummary-content':{
                marginY:"0px !important", display:'flex', justifyContent:'space-between'
              }}}>
                 <Typography variant="h6" sx={{ color: "#2B3674", fontWeight: 600, fontSize: '16px' }} className='!my-3'>
            {"Status Log"}
          </Typography>
          {(exportData.type=='import') &&
           <Typography variant="h6" sx={{ color: "#0c8af1ff", fontWeight: 600, fontSize: '16px' , paddingRight:'20px'}} className='!my-3' onClick={(e)=>{e.stopPropagation();fetchImageUrl(exportData.auditLogUrl)}} >
            Download Audit Logs
          </Typography>}
              </AccordionSummary>
                            <AccordionDetails>
<Timeline position="left"  sx={{
    [`& .MuiTimelineItem-root`]: {
      flexDirection: 'row-reverse',
    },
    [`& .MuiTimelineContent-root`]: {
      textAlign: 'left',
    },
  }}>
  {exportData?.statusLog?.map((step:{message:string,timestamp:string,status:string}, index:number) => (
    <TimelineItem key={index}>
      
      <TimelineContent>
        <Paper elevation={3} sx={{ padding: 2 }}>
          <Box className='flex justify-between'>
         
         <div> <Typography variant="subtitle1" fontWeight="bold" className='text-blue-900'>
            {step.message} &nbsp;{(step.status=='FAILED' || step.status=="PARTIAL_COMPLETED" )&&
            <span onClick={()=>setOpen(!open)} className='underline text-[12px] text-normal text-blue-500 cursor-pointer'>{open?'Hide':'View'} Details</span>}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {formatDate(step.timestamp)}
          </Typography>
        {(step.status=='FAILED'|| step.status=="PARTIAL_COMPLETED") && open && errorData.map((item:{rowNumber:number,error:string},i)=> {
        return(
        <Typography variant="body2" key={i} className='!mt-2'>
            <p className='text-[red]'>Error in Row number: {item.rowNumber}</p>
            <p className='text-[red] font-bold'>{item.error}</p>
            
          </Typography>)}
        )}
          </div>
          <Chip
            label={toNormalCase(step.status)}
            color={
              step.status === "COMPLETED"
                ? "success"
                : step.status === "IN_PROGRESS"
                ? "primary"
                : "default"
            }
            size="small"
            sx={{ mt: 1 }}
          />
          </Box>
        </Paper>
      </TimelineContent>
      <TimelineSeparator>
        <TimelineDot
          color={
            step.status === "COMPLETED"
              ? "success"
              : step.status === "IN_PROGRESS"
              ? "primary"
              : "grey"
          }
          variant={ "filled" }
        />
        {index < exportData?.statusLog?.length - 1 && <TimelineConnector />}
      </TimelineSeparator>
    </TimelineItem>
  ))}
</Timeline>
 <div className="flex justify-end gap-2">
                { exportData?.type=='import' && <button
                   className={`px-4 py-2 rounded bg-teal-500 text-white w-[115px]`}
                   onClick={() =>router.push('/tickets/import') }
                 >
                   Retry Task
                 </button>}
               </div>
</AccordionDetails>
  </Accordion>
      </Box>
  )
}

export default Page
