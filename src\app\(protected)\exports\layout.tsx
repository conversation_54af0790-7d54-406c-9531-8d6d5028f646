 "use client";
import { useEffect, useMemo, useState } from "react";
import Link from "next/link";
import { useParams, usePathname } from "next/navigation";
import { ChevronsLeft, ChevronsRight } from "lucide-react";
import { setSideNav } from "@/features/client/clientSlice";
import { useDispatch } from "react-redux";
import Image from "next/image";
import desform from "@/assests/desform.svg.svg";

 export default function ClientsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
   const [collapsed, setCollapsed] = useState(false);
  const pathname = usePathname();
  const dispatch = useDispatch();
 const params = useParams();
    const exportId = params.id;


  useEffect(() => {
    dispatch(setSideNav(collapsed));
  }, [collapsed]);

   const links = useMemo(() => {
    if(pathname.includes(`/exports/${exportId}`))
         return [

        { href: `/exports/${exportId}/task-details`,
          text: "Export Task Details",
          icon: <Image src={desform} alt="desform" width={20} height={20} />,},
        { href: `/exports/${exportId}/settings`,
          text: "Settings",
          icon: <Image src={desform} alt="desform" width={20} height={20} />,}
      ]
      }, [exportId]);
      console.log('links',links);
      
 return (
      <div className="flex" style={{ height: "calc(100vh - 191px)" }}>

      {links &&  <aside
        className={`bg-[#F8F8F9] transition-all duration-300 ${
          collapsed ? "w-20" : "w-64"
        }`}
      >
        {/* Toggle Collapse Button */}
        <button
          onClick={() => setCollapsed(!collapsed)}
          className="flex items-center justify-end w-full py-4 pr-3"
        >
          {collapsed ? <ChevronsRight size={20} /> : <ChevronsLeft size={20} />}
        </button>

        {/* Navigation Links */}
        <nav className="flex flex-col">
          {
          links?.map(({ href, text, icon }) => {
            const isActive = pathname.startsWith(href);
            return (
              <Link
                key={href}
                href={href}
                className={`${collapsed && "justify-center"} flex items-center  px-4 py-3 text-sm font-medium ${
                  isActive
                    ? "text-gray-900 bg-blue-50 border-l-4 active:border-[#1465ab] bg-white"
                    : "text-gray-700 hover:bg-white hover:border-l-4 hover:border-[#1465ab] active:bg-white active:border-l-4 active:border-[#1465ab]"
                }`}
              >
                <span className={`${collapsed ? "mr-0" : "mr-3"}`}>{icon}</span>
                {!collapsed && <span>{text}</span>}
              </Link>
            );
          })
          }
        </nav>
      </aside>}

      {/* Main Content */}
      <main className="flex-1 overflow-auto p-4">{children}</main>
    </div>
  );
}