/* eslint-disable @typescript-eslint/no-explicit-any */
'use client'
import { getAllExports } from '@/api/exportAction/export';
import { getImagedUrl } from '@/api/file/file';
import ClientDataTable from '@/app/(protected)/user-management/ClientDataTable';
import MUIFormRenderer from '@/app/[components]/print';
import Loader from '@/components/loader/loader';
import { setClientTable } from '@/features/client/clientSlice';
import { setPrintData } from '@/features/export/exportSlice';
import { setHeaders, setHeadersDefault } from '@/features/headers/headersSlice';
import { TableData } from '@/types/user';
import { convertFormJsonFromClients, transformedClients } from '@/utils/generic';
import React, { useEffect, useState } from 'react'
import { useDispatch } from 'react-redux';

const Page = () => {
  // getAllExports
   const dispatch = useDispatch();
  const [pagination, setPagination] = React.useState(1);
  const [loader, setLoader] = React.useState(false);
  const [data, setData]=useState([])
  const [query, setQuery] = React.useState({
    search: "",
    filters: '',
    sortBy: "",
    sortOrder: "asc",
    page: 1,
    limit: 10,
  });

  React.useEffect(() => {
    handleGetApi();
    console.log("each");
  }, [query]);
const printRef = React.useRef<HTMLDivElement>(null);

  useEffect(() => {
  if (data && data.length!==0) {
  const printContents = printRef.current?.innerHTML;

  if (printContents) {
    const printWindow = window.open('', '_blank');

    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>Print Preview</title>
            <style>
              body { font-family: sans-serif; padding: 20px; }
              h6 { text-transform: uppercase; border-bottom: 2px solid #ddd; padding-bottom: 4px; }
              .MuiGrid-root { display: flex; flex-wrap: wrap; }
              .MuiTypography-root { margin: 4px 0; }
            </style>
          </head>
          <body onload="window.print(); window.close();">
            ${printContents}
          </body>
        </html>
      `);
      printWindow.document.close();
    }
  }
  }
}, [data]);

 

   const handlePrint = async(filename:string) => {
    try {
      const res = await getImagedUrl({ filename });
      console.log(res.generateViewUrl.data.viewUrl);

      // Optional fetch check if you want:
      const responses = await fetch(res.generateViewUrl.data.viewUrl, {
        method: "GET",
        // headers: {
        //   "Content-Type": "image/png",
        // },
      });
      console.log(responses,'response');
     
       try {
      // Your signed URL
      const signedUrl =responses.url

      const response = await fetch(signedUrl);
      console.log('response',response.url);

      const datas = await response.json();
      setData(datas)
      dispatch(setPrintData(datas))


      } catch (error) {
      console.error("Error downloading file:", error);
      // alert("Failed to download the file.");
    }
    }
  catch (error) {
      console.error("Error downloading file:", error);
      // alert("Failed to download the file.");
    }

   
    // 
  };

  const handleGetApi = () => {
    setLoader(true);
  const titles = [ "taskId",
            "type",
            "createdAt",
            "createdBy",
            "downloadUrl",
            "status",
            "name",
            ];
          dispatch(setHeaders(titles))
          dispatch(setHeadersDefault(titles as any))
      
      getAllExports(query)
        .then((res) => {
          console.log(
            res.allExports.items
,
            "res.allExports.users"
          );
          const data = transformedClients(res.allExports.items);
          const tableData = convertFormJsonFromClients(data);
          console.log('tableData',tableData);
          
          dispatch(setClientTable(tableData as TableData));
          setPagination(res.allExports.pagination);
          setLoader(false);
        })

        .catch((err) => {
          setLoader(false);
          console.error(err);
        });
  };
  return (
    <>
      {loader && <Loader />}
      <ClientDataTable
        title={"File Transfer"}
        handleGetApi={handleGetApi}
        setQuery={setQuery}
        query={query}
        pagination={pagination}
        role={[]}
        handlePrint={handlePrint}
      />
         {/* <div> */}

      {/* Hidden print component */}
      <div style={{ display: "none" }}>
        <MUIFormRenderer ref={printRef} data={data}/>
      </div>
    {/* </div> */}
    </>
  );
}

export default Page
