"use client";

import { useEffect } from "react";
import Layout from "../Layout/index";
import { useIsAuthenticated, useMsal } from "@azure/msal-react";
import { useRouter, usePathname } from "next/navigation";
import Loader from "@/components/loader/loader";
import Cookies from "js-cookie";

export default function ProtectedLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const isAuthenticated = useIsAuthenticated();
  const { inProgress } = useMsal();
  const router = useRouter();
  const pathname = usePathname();

  // const isAssigned = sessionStorage.getItem("isAssigned") === "true";
  const isAssigned =Cookies.get("isAssigned")
console.log('isAssigned',isAssigned);

  useEffect(() => {
    // if (!isAssigned) {
    //   // Optional: redirect if user is not assigned
    //   router.replace("/");
    //   // return;
    // }

    if (inProgress === "none" && !isAuthenticated) {
      router.replace(`/?redirect=${encodeURIComponent(pathname)}`);
    }
  }, [inProgress, isAuthenticated, router, pathname]);

  if (inProgress !== "none") {
    return <Loader />;
  }

  return <Layout>{children}</Layout>;
}
