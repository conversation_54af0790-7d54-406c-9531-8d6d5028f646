/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { Card } from "@/app/[components]/card";
import { RootState } from "@/store";
import { Icon, Typography } from "@mui/material";
import Image from "next/image";
import addIcon from '../../../assests/AddIcon.png'
import React, { useEffect, useState, useRef } from "react";
import { useSelector } from "react-redux";
import { updateUser } from "@/api/organizations/organizations";
import { showToast } from "@/components/toaster/ToastProvider";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import { extractMatchingKeys, toCamelCase } from "@/utils/generic";
import CreateDrawer from "@/app/[components]/createDrawer";
import { createPayers, updatePayer } from "@/api/masters/payer/payer";
import { usePathname } from 'next/navigation';
import CreateCPTDrawer from "./createDrawer";
import { createMainCPT, getByIdCPT, getCPTCodes, getDiagnosis, getICDCodes, getSpeciality, updateMainCPT } from "@/api/masters/cpt-code/cptCode";
import { editIcon } from "@/app/[components]/editIcon";
import { deleteIcon } from "@/app/[components]/deleteIcon";
import { createProviders, updateProvider } from "@/api/masters/provider/provider";
import { actionStatusGetById, createActionStatus, getACtionCOdeList, getProcessList, getStatusCodeList, updateActionStatus } from "@/api/masters/action-status/actionStatus";
import { createExecption, getByIdException, updateException } from "@/api/masters/exception/exception";
import SuccessPage from "./successPage";
import { RenderForm } from "./renderForm";
import { getPreSignedUrl } from "@/api/file/file";
// import Loader from "@/components/loader/loader";

// Type definitions
interface SelectOption {
  id?: string;
  code?: string;
  name?: string;
  value?: string;
  [key: string]: any; // For additional properties
}

interface FormData {
  [section: string]: {
    [field: string]: string | SelectOption | SelectOption[];
  };
}

interface IdsState {
  speciality?: string;
  diagnosisCode?: string;
  cPTCode?: string;
  iCD?: string;
  process?: string;
  statusCode?: string;
  actionCode?: string;
}

const ClientOnboardingForm = ({ type, formTemplate, templateId, flattedValues,access }: { type: string, formTemplate: any, templateId: string, clientTyoe: string, flattedValues?: any,access:boolean }) => {
  const pathname = usePathname();
  const selectRef = useRef<HTMLInputElement>(null);
  const collapsed = useSelector((state: RootState) => state.sideNav.sideNav);
  const formDataById: any = useSelector((state: RootState) => state.clientById);
  const router = useRouter();
  const params = useParams();
  console.log(formDataById, "params");

  const id = params?.id as string;
  const idss = params?.ids as string;
  const searchParams = useSearchParams();
  const providerId = searchParams.get('id') || "";
  const token = sessionStorage.getItem('token');
  

  console.log('token', token);

  // State with proper typing
  const [formData, setFormData] = useState<FormData>({});
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [fieldName, setFieldName] = useState('');
  const [cptDrawer, setCptDrawer] = useState(false);
  const [success, setSuccess] = useState(false);
  const [ids, setIds] = useState<IdsState>({
    speciality: "",
    diagnosisCode: "",
    cPTCode: "",
    iCD: "",
    process: '',
    statusCode: '',
    actionCode: ''
  });
  console.log(formData, 'formData');

  const [editIndex, setEditIndex] = useState<number | null>(null);
  const [editRowData, setEditRowData] = useState<any | null>(null);
  const [query, setQuery] = useState({ filters: '' });
  const [statusQuery, setStatusQuery] = useState({ filters: '' });
  const [actionQuery, setActionQuery] = useState({ filters: '' })
  const [openSelects, setOpenSelects] = useState<Record<string, boolean>>({});
  const [exception, setException] = useState('')
  const hasPatchedData = useRef(false);

  const BillingList: SelectOption[] = [
    { "id": "per_day", "value": "Per Day" },
    { "id": "per_week", "value": "Per Week" },
    { "id": "per_month", "value": "Per Month" },
    { "id": "per_year", "value": "Per Year" }
  ];

  const unitType: SelectOption[] = [
    { "id": "15_mins", "value": "15 Minutes" },
    { "id": "30_mins", "value": "30 Minutes" },
    { "id": "45_mins", "value": "45 Minutes" },
    { "id": "60_mins", "value": "60 Minutes" }
  ];

  const unitValue: SelectOption[] = [
    { "id": "1", "value": "1 Units" },
    { "id": "2", "value": "2 Units" },
    { "id": "3", "value": "3 Units" },
    { "id": "4", "value": "4 Units" }
  ];

  const statusList: SelectOption[] = [
    { id: 'active', value: 'Active' },
    { id: 'inactive', value: 'Inactive' }
  ];

  const [gridFieldMeta, setGridFieldMeta] = useState<{
    sectionName: string;
    fieldId: string;
    columns: any[];
  } | null>(null);
  const [fetchedGlobals, setFetchedGlobals] = useState<Record<string, boolean>>({});
  const [dynamicSelectOptions, setDynamicSelectOptions] = useState<Record<string, SelectOption[]>>({});
  const isEditMode = !collapsed;
  const columnClasses = isEditMode ? "grid-cols-1 md:grid-cols-3" : "grid-cols-1 md:grid-cols-4";
  const [loader,setLoader] = useState(false)
  useEffect(() => {
    console.log('formTemplateeee', formDataById)
    if (type === 'edit' && formDataById) {
      const patchedData = JSON.parse(formDataById?.values);
      setFormData(patchedData);
      if (pathname.includes("/cpt-code")) {
        getIcdList();
      }
    }
  }, [type, formDataById]);

  useEffect(() => {
    const fetchAndPatchForm = async () => {
      if (
        !hasPatchedData.current &&
        formTemplate &&
        pathname.includes(`masters/cpt-code/${id}/edit/`) &&
        Object.keys(dynamicSelectOptions).length > 0
      ) {
        const cptResponse = await getByIdCPTData(); // Ensure it returns CPT object
        // const cptValues = JSON.parse(cptResponse?.data?.values || '{}')?.addCPTDictionary || {};



        console.log('cptREssss', cptResponse?.data?.values)

        setIds(prev => ({
          ...prev,
          cPTCode: cptResponse?.data?.cptCodeId,
          iCD: cptResponse?.data?.icdId,
        }));

        setQuery(prev => ({
          ...prev,
          filters: cptResponse?.data?.icdId ? JSON.stringify({ icd: cptResponse?.data?.icdId }) : ''
        }));


        setTimeout(() => {
          setIds(prev => ({
            ...prev,
            speciality: cptResponse?.data?.specialtyId,
            diagnosisCode: cptResponse?.data?.diagnosisId,

          }));

          setQuery(prev => ({
            ...prev,
            filters: cptResponse?.data?.specialtyId ? JSON.stringify({ specialtyId: cptResponse?.data?.specialtyId }) : ''
          }));
        }, 100)




        // setQuery(prev => ({
        //   ...prev,
        //   filters: JSON.stringify({
        //     ...(cptResponse?.data?.specialtyId && { specialtyId: cptResponse?.data?.specialtyId }),
        //     ...(cptResponse?.data?.icdId && { icd: cptResponse?.data?.icdId })
        //   })
        // }));
        const parsedValues = JSON.parse(cptResponse?.data?.values)

        console.log(`cptResponse`, parsedValues)

        setFormData((prevData: FormData) => ({
          ...prevData,
          ...parsedValues,
        }));

        hasPatchedData.current = true;
      } else if (!hasPatchedData.current && formTemplate && pathname.includes(`masters/action-status/${id}/edit/`) &&
        Object.keys(dynamicSelectOptions).length > 0) {

        const cptResponse = await getByIdActionStatusData();




        setIds(prev => ({
          ...prev,
          process: cptResponse?.processId,
          actionCode: cptResponse?.actionCodeId,
        }));



        setStatusQuery(prev => ({
          ...prev,
          filters: cptResponse?.processId ? JSON.stringify({ processId: cptResponse?.processId }) : ''
        }));

        setTimeout(() => {
          setIds(prev => ({
            ...prev,
            statusCode: cptResponse?.statusCodeId
          }));

          setActionQuery(prev => ({
            ...prev,
            filters: cptResponse?.statusCodeId ? JSON.stringify({ statusCodeId: cptResponse?.statusCodeId }) : ''
          }));
        }, 100)


        const parsedValues = JSON.parse(cptResponse?.values)

        console.log(`cptResponse`, parsedValues)

        setFormData((prevData: FormData) => ({
          ...prevData,
          ...parsedValues,
        }));

        hasPatchedData.current = true;
      } else if (!hasPatchedData.current && formTemplate && pathname.includes(`masters/exception/${id}/edit/`) &&
        Object.keys(dynamicSelectOptions).length > 0) {

        const cptResponse = await getByIdExceptionData();




        setIds(prev => ({
          ...prev,
          process: cptResponse?.processId?._id
        }));

        setException(cptResponse?.exceptions)

        const parsedValues = JSON.parse(cptResponse?.values)
        setFormData((prevData: FormData) => ({
          ...prevData,
          ...parsedValues,
        }));

        hasPatchedData.current = true;
      }
    };

    fetchAndPatchForm();
  }, [type, formTemplate, dynamicSelectOptions]);


  const getByIdCPTData = async () => {
    setLoader(true)
    if (id !== null) {
      try {
        const data = await getByIdCPT({ id });
        console.log("CPT data:", data);
        return data; // ✅ Return the actual data
      } catch (err) {
        console.error("Error:", err);
        return null; // Optional: return null on error
      }finally{
        setLoader(false)
      }
    }
    return null; // Optional: handle no-id case
  };

  const getByIdActionStatusData = async () => {
    setLoader(true)
    if (id !== null) {
      try {
        const response = await actionStatusGetById({ id });
        return response?.data?.actionStatusCodeMap?.data;
      } catch (err) {
        console.error("Error:", err);
        return null;
      } finally{
        setLoader(false)
      }
    }
    return null;
  };

  const getByIdExceptionData = async () => {
    setLoader(true)
    if (id !== null) {
      try {
        const response = await getByIdException({ id });
        return response?.data?.findExceptionById?.data?.exception;
      } catch (err) {
        console.error("Error:", err);
        return null;
      }finally{
        setLoader(false)
      }
    }
    return null;
  };

  useEffect(() => {
    if (pathname.includes(`masters/action-status`)) {
      getProcessData()
      getStatusCodeData()
      getActionList()
    } else if (pathname.includes(`masters/cpt-code/`)) {
      setDynamicSelectOptions(prev => ({
        ...prev,
        'Billing Frequency': BillingList,
        'Unit Type': unitType,
        'Status': statusList,
        'Unit Value': unitValue
      }));
      getIcdList();
      getDiagnosisList();
      getSpecialityList();
      getCPTCodeList();
    } else if (pathname.includes(`masters/exception/`)) {
      getProcessData()
    }


  }, [cptDrawer, statusQuery, actionQuery]);

  useEffect(() => {
    if (pathname.includes(`masters/cpt-code/`)) {
      getDiagnosisList();
    }
  }, [ids?.iCD])


  useEffect(() => {
    if (pathname.includes(`masters/cpt-code/`)) {
      getCPTCodeList();
    }
  }, [ids?.speciality]);

  const getIcdList = () => {
    setLoader(true)
    getICDCodes({})
      .then((res) => {
        setDynamicSelectOptions(prev => ({
          ...prev,
          ICD: res?.icds?.items ?? []
        }));
      })
      .catch((err) => {
        showToast.error(err.message);
        console.error(err);
      }).finally(()=>{
        setLoader(false)
      });
  };

  const getProcessData = () => {
    setLoader(true)
    getProcessList({})
      .then((res) => {
        console.log('ProcessList', res)
        setDynamicSelectOptions(prev => ({
          ...prev,
          Process: res?.processes?.data?.items ?? []
        }));
      })
      .catch((err) => {
        showToast.error(err.code);
        console.error(err);
      }).finally(()=>{
        setLoader(false)
      });
  };


  const getStatusCodeData = () => {
    setLoader(true)
    getStatusCodeList(statusQuery)
      .then((res) => {
        console.log('StatusCode', res)
        setDynamicSelectOptions(prev => ({
          ...prev,
          'Status Code': res?.statusCodes?.items ?? []
        }));
      })
      .catch((err) => {
        showToast.error(err.code);
        console.error(err);
      }).finally(()=>{
        setLoader(false)
      });
  };

  const getActionList = () => {
    setLoader(true)
    getACtionCOdeList(actionQuery)
      .then((res) => {
        console.log('actionStatus', res)
        setDynamicSelectOptions(prev => ({
          ...prev,
          'Action Code': res?.actionCodes?.items ?? []
        }));
      })
      .catch((err) => {
        showToast.error(err.code);
        console.error(err);
      }).finally(()=>{
        setLoader(false)
      });
  };



  const getDiagnosisList = () => {
    setLoader(true)
    getDiagnosis(query)
      .then((res) => {
        setDynamicSelectOptions(prev => ({
          ...prev,
          "Diagnosis Code": res?.diagnoses?.items ?? []
        }));
      })
      .catch((err) => {
        showToast.error(err.message);
        console.error(err);
      }).finally(()=>{
        setLoader(false)
      });
  };

  const getSpecialityList = () => {
    setLoader(true)
    getSpeciality({})
      .then((res) => {
        setDynamicSelectOptions(prev => ({
          ...prev,
          Speciality: res?.specialties?.items ?? []
        }));
      })
      .catch((err) => {
        showToast.error(err.message);
        console.error(err);
      }).finally(()=>{
        setLoader(false)
      });
  };

  const getCPTCodeList = () => {
    setLoader(true)
    getCPTCodes(query)
      .then((res) => {
        setDynamicSelectOptions(prev => ({
          ...prev,
          "CPT Code": res?.cptCodes?.items ?? []
        }));
      })
      .catch((err) => {
        showToast.error(err.message);
        console.error(err);
      }).finally(()=>{
        setLoader(false)
      });
  };

  function evaluateLogicConditions(
    conditions: any[],
    formData: any,
    logicJoinType: 'AND' | 'OR' = 'AND'
  ): boolean {

    if (!Array.isArray(conditions) || conditions.length === 0) {
      return true;
    }

    const results = conditions.map((condition) => {
      const { fieldId, operator, value: expectedValue } = condition;

      let actualValue: any = undefined;

      // Step 1: Find the field label from formTemplate using fieldId
      let fieldLabel = '';
      for (const section of formTemplate.sections) {
        const found = section.fields.find((field: any) => field.id === fieldId);
        if (found) {
          fieldLabel = found.label;
          break;
        }
      }

      if (!fieldLabel) return false;

      const fieldKey = toCamelCase(fieldLabel);

      // Step 2: Find the value from formData
      for (const sectionKey in formData) {
        const section = formData[sectionKey];
        if (section && fieldKey in section) {
          const val = section[fieldKey];
          actualValue = typeof val === 'object'
            ? val?.value ?? val?.name ?? val?.code ?? val?.id
            : val;
          break;
        }
      }

      // Step 3: Apply logic
      switch (operator) {
        case '===':
          return actualValue === expectedValue;
        case '!==':
          return actualValue !== expectedValue;
        default:
          return false;
      }
    });

    return logicJoinType === 'AND' ? results.every(Boolean) : results.some(Boolean);
  }


  const handleFieldChange = async (
    sectionName: string,
    fieldLabel: string,
    value: any
  ) => {
    const sectionKey = toCamelCase(sectionName);
    const fieldKey = toCamelCase(fieldLabel);

    if (fieldKey === 'iCD') {
      console.log(value, 'value11111111');

      const icdId = typeof value === 'string' ? value : value.id || value?._id;
      setIds(prev => ({
        ...prev,
        [fieldKey]: icdId,
        diagnosisCode: ''
      }));
      setFormData((prevData: FormData) => ({
        ...prevData,
        [sectionKey]: {
          ...(prevData[sectionKey] || {}),
          [fieldKey]: value.code,
          diagnosisCode: ''
        },
      }));
      // Update the query with ICD filter for Diagnosis API
      setQuery(prev => ({
        ...prev,
        filters: icdId ? JSON.stringify({ icd: icdId }) : ''
      }));
    }
    else if (fieldKey === 'speciality') {
      console.log(value, 'value');
      const specialityId = typeof value === 'string' ? value : value.id || value?._id;
      setIds(prev => ({
        ...prev,
        [fieldKey]: specialityId
      }));
      setFormData((prevData: FormData) => ({
        ...prevData,
        [sectionKey]: {
          ...(prevData[sectionKey] || {}),
          [fieldKey]: value.name,
          cPTCode: ''
        },
      }));
      // Update the query with ICD filter for Diagnosis API
      setQuery(prev => ({
        ...prev,
        filters: specialityId ? JSON.stringify({ specialtyId: specialityId }) : ''
      }));
    }
    else if (fieldKey === 'diagnosisCode') {
      console.log(value, 'diagnosisCode');
      const diagnosisCode = typeof value === 'string' ? value : value.id || value?._id;
      const icdLabel = dynamicSelectOptions[`ICD`]?.find(opt => opt._id === value.icd)?.code || '';
      setIds(prev => ({
        ...prev,
        [fieldKey]: diagnosisCode,
        iCD: value.icd
      }));
      setFormData((prevData: FormData) => ({
        ...prevData,
        [sectionKey]: {
          ...(prevData[sectionKey] || {}),
          [fieldKey]: value.name,
          ...(icdLabel ? { iCD: icdLabel } : {}),
        },
      }));


      setQuery(prev => ({
        ...prev,
        filters: value?.icd ? JSON.stringify({ icd: value?.icd }) : ''
      }));
    }
    else if (fieldKey === 'cPTCode') {
      console.log(value, 'value');
      const cPTCode = typeof value === 'string' ? value : value.id || value?._id;
      const specialityLabel = dynamicSelectOptions[`Speciality`]?.find(opt => opt._id === value.specialtyId)?.name || '';

      setIds(prev => ({
        ...prev,
        [fieldKey]: cPTCode,
        speciality: value.specialtyId,
      }));
      setFormData((prevData: FormData) => ({
        ...prevData,
        [sectionKey]: {
          ...(prevData[sectionKey] || {}),
          [fieldKey]: value.code,
          ...(specialityLabel ? { speciality: specialityLabel } : {}),
        },
      }));
      
      setQuery(prev => ({
        ...prev,
        filters: value.specialtyId ? JSON.stringify({ specialtyId: value.specialtyId }) : ''
      }));
    } else if (fieldKey === 'process') {
      console.log(value, 'process');
      const cPTCode = typeof value === 'string' ? value : value.id || value._id;

      setStatusQuery(prev => ({
        ...prev,
        filters: cPTCode ? JSON.stringify({ processId: cPTCode }) : ''
      }));

      setActionQuery(prev => ({
        ...prev,
        filters: cPTCode ? JSON.stringify({ statusCodeId: '' }) : ''
      }));

      setIds(prev => ({
        ...prev,
        [fieldKey]: cPTCode,
        actionCode: '',
        statusCode: ''
      }));
      setFormData((prevData: FormData) => ({
        ...prevData,
        [sectionKey]: {
          ...(prevData[sectionKey] || {}),
          [fieldKey]: value.name,
          actionCode: '',
          statusCode: ''
        },
      }));
    }
    else if (fieldKey === 'statusCode') {
      console.log(value, 'statusCode');
      const cPTCode = typeof value === 'string' ? value : value.id || value._id;

      setActionQuery(prev => ({
        ...prev,
        filters: cPTCode ? JSON.stringify({ statusCodeId: cPTCode }) : ''
      }));

      setIds(prev => ({
        ...prev,
        [fieldKey]: cPTCode
      }));
      setFormData((prevData: FormData) => ({
        ...prevData,
        [sectionKey]: {
          ...(prevData[sectionKey] || {}),
          [fieldKey]: value.code,
        },
      }));
    }
    else if (fieldKey === 'actionCode') {
      console.log(value, 'Action Code');
      const cPTCode = typeof value === 'string' ? value : value.id || value._id;

      setIds(prev => ({
        ...prev,
        [fieldKey]: cPTCode
      }));
      setFormData((prevData: FormData) => ({
        ...prevData,
        [sectionKey]: {
          ...(prevData[sectionKey] || {}),
          [fieldKey]: value.code,
        },
      }));
    }

    else {

      console.log('typeof value', typeof value);

      // const valueObj = typeof value === 'object' ? value?.name || value?.code || value?.value : value;
      const valueObj = typeof value === 'object' && value !== null
        ? value?.name ?? value?.code ?? value?.value ?? value
        : value;
      if (fieldKey === 'exception') {
        setException(valueObj)
      }
      setFormData((prevData: FormData) => ({
        ...prevData,
        [sectionKey]: {
          ...(prevData[sectionKey] || {}),
          [fieldKey]: valueObj,
        },
      }));
    }
    if (fieldLabel.toLowerCase().includes("image")) {
      if (value) {
        const path = `organization/provider/${value.name}`; // <-- desired path in storage
        const payload = {
          input: { filename: path as string, contentType: "application/pdf" },
        };

        try {
          const uploadPath = await getPreSignedUrl(payload);

          console.log(
            "uploadPath",
            uploadPath.generateUploadUrl.data.uploadUrl
          );
          // const path = `organization/profile/${value}`; // <-- desired path in storage
          const uploadResponse = await fetch(
            uploadPath.generateUploadUrl.data.uploadUrl,
            {
              method: "PUT",
              body: value,
              headers: {
                "Content-Type": "*", // fallback
              },
            }
          );

          if (!uploadResponse.ok) {
            throw new Error("Something went wrong. Please try again.");
          }

          // Save the file path or accessible URL to formData
          setFormData((prevData: any) => ({
            ...prevData,
            [sectionKey]: {
              ...(prevData[sectionKey] || {}),
              [fieldKey]: path, // Assuming this is returned by getPreSignedUrl
            },
          }));
          console.log(uploadPath, "uploadPath");
        } catch (e) {
          console.error("Image upload failed:", e);
        }
      }
    }
  };

  console.log('formData', formData);

  function cleanCPTValues(input: any) {
    const rawDict = input.addCPTDictionary;
    const cleaned: Record<string, string> = {};

    for (const key in rawDict) {
      const val = rawDict[key];

      if (typeof val === 'object' && val !== null) {
        cleaned[key] = val.code || val.name || val.value || '';
      } else {
        cleaned[key] = val ?? '';
      }
    }

    return { addCPTDictionary: cleaned };
  }

  function extractIds(input: any): Record<string, string> {
    const raw = input.addCPTDictionary;
    const ids: Record<string, string> = {};

    ["iCD", "diagnosisCode", "cPTCode", "speciality"].forEach((key) => {
      const val = raw?.[key];
      if (typeof val === "object" && val?.id) {
        ids[key] = val.id;
      }
    });

    return ids;
  }


  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoader(true)
    console.log('formTemp', formData)
    const result = extractMatchingKeys(formData, flattedValues);

    if (pathname.includes("/cpt-code/create/")) {
      console.log('flattenValues', result, formData, flattedValues)
      const payload = {
        input: {
          type: 'SUB_CLIENT',
          values: JSON.stringify(formData) ?? '',
          specialtyId: ids.speciality ?? null,
          diagnosisId: ids.diagnosisCode ?? null,
          templateId: templateId ?? null,
          cptCodeId: ids.cPTCode ?? null,
          icdId: ids.iCD ?? null,
          flattenedValues: result
        },



      };

      await createMainCPT(payload)
        .then(() => {
          setLoader(false)
          showToast.success('CREATED');
          router.replace('/masters/cpt-code')
        })
        .catch((err) => {
          setLoader(false)
          showToast.error(err.message);
          console.error(err);
        });
    }
    else if (pathname.includes(`masters/cpt-code/${id}/edit/`)) {
      console.log(formData)
      const transformed = cleanCPTValues(formData);
      const idData = extractIds(formData)

      console.log('idData', idData)
      // const values = formData?.addCPTDictionary || {};

      const payload = {
        input: {
          id: id ?? null,
          type: 'SUB_CLIENT',
          values: JSON.stringify(transformed) ?? null,
          specialtyId: ids?.speciality ?? null,
          diagnosisId: ids?.diagnosisCode ?? null,
          cptCodeId: ids?.cPTCode ?? null,
          icdId: ids?.iCD ?? null,
          flattenedValues: result ?? {}, // assuming this is always an object
        }
      };


      await updateMainCPT(payload)
        .then((res) => {
          console.log(res)
          setLoader(false)
          showToast.success(`CPT Updated Successfully`);
          router.replace('/masters/cpt-code')
        })
        .catch((err) => {
          setLoader(false)
          showToast.error(err.message);
          console.error(err);
        });
    }
    else if (pathname.includes("/payer/create/")) {

      const payload = {
        input: {
          templateId: templateId,
          values: JSON.stringify(formData) ?? '',
          flattenedValues: result
        },
      };
      await createPayers(payload)
        .then(() => {
          setLoader(false)
          showToast.success('CREATED');
          router.push('/masters/payer');
        })
        .catch((err) => {
          setLoader(false)
          showToast.error(err.message);
          console.error(err);
        });
    }
    else if (pathname.includes(`masters/payer/${id}`)) {

      const payload = {
        input: {
          id: id,
          templateId: templateId,
          values: JSON.stringify(formData) ?? '',
          flattenedValues: result
        },
      };
      await updatePayer(payload)
        .then((res) => {
          setLoader(false)
          showToast.success(res?.code);
          router.push('/masters/payer');
        })
        .catch((err) => {
          setLoader(false)
          showToast.error(err.message);
          console.error(err);
        });
    }
    else if (pathname.includes("/provider/create/")) {
      const payload = {
        input: {
          values: JSON.stringify(formData) ?? '',
          templateId: templateId,
          flattenedValues: result,
          subOrganisationId: id,
          email: formData?.basicInformation?.email ?? ""
        }
      };

      await createProviders(payload)
        .then(() => {
          setLoader(false)
          showToast.success('CREATED');
          if (typeof window !== 'undefined') { window.history.back(); }
        })
        .catch((err) => {
          setLoader(false)
          showToast.error(err.message);
          console.error(err);
        });
    }

    else if (pathname.includes(`provider/${idss}`) || pathname.includes("provider-edit")) {
      // eslint-disable-next-line prefer-const
      let payload: any = {
        input: {
          values: JSON.stringify(formData) ?? '',
          id: pathname.includes("provider-edit") ? providerId : idss,
          flattenedValues: result,
          subOrganisationId: formDataById.subOrganisationId
        }
      };
      if (pathname.includes("provider-edit")) {
        payload.input.token = token;
      }

      await updateProvider(payload)
        .then(() => {
          setLoader(false)
          showToast.success('UPDATED');
          if (typeof window !== 'undefined' && !pathname.includes("provider-edit")) {
            window.history.back();
            setSuccess(false)
          }
          if (pathname.includes("provider-edit")) {
            setSuccess(true)
          }
        })
        .catch((err) => {
          setLoader(false)
          showToast.error(err.message);
          console.error(err);
        });
    } else if (pathname.includes("/masters/action-status/create/")) {

      console.log('flattenValue', flattedValues)
      const payload = {
        input: {
          processId: ids?.process ?? null,
          actionCodeId: ids?.actionCode ?? null,
          statusCodeId: ids?.statusCode ?? null,
          templateId: templateId ?? null,
          flattenedValues: result as unknown as JSON,
          values: JSON.stringify(formData) ?? null,
        },
      };


      await createActionStatus(payload)
        .then(() => {
          setLoader(false)
          showToast.success('CREATED');
          if (typeof window !== 'undefined') { window.history.back(); }
        })
        .catch((err) => {
          setLoader(false)
          showToast.error(err.message);
          console.error(err);
        });
    }
    else if (pathname.includes(`/masters/action-status/${id}/edit/`)) {

      console.log('flattenValue', formData)
      const payload = {
        input: {
          id: id ?? null,
          processId: ids?.process ?? null,
          actionCodeId: ids?.actionCode ?? null,
          statusCodeId: ids?.statusCode ?? null,
          templateId: templateId ?? null,
          values: JSON.stringify(formData) ?? null,       // 👈 do NOT JSON.stringify
          isActive: formData?.createActionStatusCode?.active === 'Active' ? true : false,                  // or `true ?? null` if nullable
        }
      };


      await updateActionStatus(payload)
        .then(() => {
          setLoader(false)
          showToast.success('UPDATED');
          if (typeof window !== 'undefined') { window.history.back(); }
        })
        .catch((err) => {
          setLoader(false)
          showToast.error(err.message);
          console.error(err);
        });
    }
    else if (pathname.includes(`masters/exception/create`)) {

      const payloads = {
        input: {
          templateId: templateId ?? null,
          values: JSON.stringify(formData) ?? null,
          flattenedValues: result,
          processId: ids?.process ?? null,
          exceptions: exception,
        },
      };
      createExecption(payloads)
        .then((res) => {
          setLoader(false)
          showToast.success(res?.code);
          router.push('/masters/exception');
        })
        .catch((err) => {
          setLoader(false)
          showToast.error(err.message);
          console.error(err);
        });

    }
    else if (pathname.includes(`masters/exception/${id}/edit/`)) {

      const payloads = {
        input: {
          id: id ?? null,
          templateId: templateId ?? null,
          values: JSON.stringify(formData) ?? null,
          flattenedValues: result,
          processId: ids?.process ?? null,
          exceptions: exception,
        },
      };
      updateException(payloads)
        .then((res) => {
          setLoader(false)
          showToast.success(res?.code);
          router.push('/masters/exception');
        })
        .catch((err) => {
          setLoader(false)
          showToast.error(err.message);
          console.error(err);
        });

    }
    else {
      if (type === 'edit') {
        const payloads = {
          input: {
            id: formDataById.id,
            values: JSON.stringify(formData) ?? '',
          },
        };
        updateUser(payloads)
          .then((res) => {
            setLoader(false)
            showToast.success(res?.code);
            router.push('/masters/cpt-codes');
          })
          .catch((err) => {
            setLoader(false)
            showToast.error(err.message);
            console.error(err);
          });
      }
    }
  };

  const handleGridDelete = (
    sectionName: string,
    index: number
  ) => {
    const sectionKey = toCamelCase(sectionName);

    setFormData((prev: any) => {
      const dataArray = prev[sectionKey];
      if (!Array.isArray(dataArray) || index < 0 || index >= dataArray.length) {
        return prev;
      }
      const updatedArray = dataArray.filter((_, i) => i !== index); // Remove by index
      return {
        ...prev,
        [sectionKey]: updatedArray,
      };
    });
  };


  const handleGridEdit = (
    sectionName: string,
    fieldLabel: string,
    index: number,
    columns: any[],
    existingRow: any
  ) => {
    setGridFieldMeta({
      sectionName,
      fieldId: fieldLabel,
      columns,
    });
    setEditIndex(index);
    setEditRowData(existingRow);
    setDrawerOpen(true);
  };

  const addGridRow = ({
    sectionName,
    newRow,
  }: {
    sectionName: string;
    newRow: Record<string, string>;
  }) => {
    const sectionKey = toCamelCase(sectionName);
    setFormData((prevData: any) => ({
      ...prevData,
      [sectionKey]: [
        ...(prevData[sectionKey] || []),
        newRow,
      ],
    }));
  };




  useEffect(() => {
    console.log('fieldssssss', formTemplate)
    if (!formTemplate?.sections) return;

    formTemplate.sections.forEach((section: any) => {
      section.fields.forEach((field: any) => {
        if (
          field.field_type === 'global_select' &&
          !fetchedGlobals[field.label]
        ) {
          setFetchedGlobals(prev => ({ ...prev, [field.label]: true }));
        }
      });
    });
  }, [formTemplate]);

  const handleAddICD = (fieldName: string) => {
    setFieldName(fieldName);
    setCptDrawer(true);
  };

  return (
    <>
    
      {!success ? <div className="mt-4">
        <div className="space-y-6">
          <Card className="border border-gray-300 !p-0 !rounded-lg">
            <form onSubmit={handleSubmit} className="space-y-6">
              {formTemplate?.sections?.map((section: { id: string, name: string, fields: any[] }) => (
                <div key={section.id}>
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2 bg-blue-50 px-2 py-1 pt-[10px] ">
                    <Typography variant="h6">{section.name}</Typography>
                    {section.fields.some((f) => f.field_type === "grid") && (
                      <button
                        onClick={() => {
                          setGridFieldMeta({
                            sectionName: section.name,
                            fieldId: section.id,
                            columns: section.fields[0].columns,
                          });
                          setDrawerOpen(true);
                        }}
                        type="button"
                        className="text-sm font-200 text-gray-700 bg-none px-4 py-2 rounded flex"
                      >
                        <Icon><Image src={addIcon} alt="add" width={20} height={20} /></Icon>
                        <span>Add Row</span>
                      </button>
                    )}
                  </div>
                  <div className={`grid ${columnClasses} gap-4 p-4`}>
                    {section.fields
                      .filter((field) => field.field_type !== "grid" &&
                        field.field_type !== "checkboxes" &&
                        !field.only_in_grid &&
                        !field.only_in_custom_form)
                      .map((field) => {
                        const isVisible = !field.logicConditions || evaluateLogicConditions(
                          field.logicConditions,
                          formData,
                          field.logicJoinType === 'AND' ? 'AND' : 'OR'
                        );

                        console.log('fieldssssss', evaluateLogicConditions(
                          field.logicConditions,
                          formData,
                          field.logicJoinType === 'AND' ? 'AND' : 'OR'
                        ))
                        if (!isVisible) return null;

                        return (
                          <div key={field.id}>
                            <RenderForm
                              loader={loader}
                              field={field}
                              sectionName={section.name}
                              formData={formData}
                              handleFieldChange={handleFieldChange}
                              type={type}
                              handleGridEdit={handleGridEdit}
                              handleGridDelete={handleGridDelete}
                              editIcon={editIcon}
                              deleteIcon={deleteIcon}
                              dynamicSelectOptions={dynamicSelectOptions}
                              evaluateLogicConditions={evaluateLogicConditions}
                              selectRef={selectRef}
                              openSelects={openSelects}
                              setOpenSelects={setOpenSelects}
                              handleAddICD={handleAddICD}
                              pathname={pathname}
                              access={access}
                            />
                          </div>
                        );
                      })}

                  </div>
                  {section.fields
                    .filter((field) => field.field_type === "checkboxes")
                    .map((field) => (
                      <div key={field.id} className="px-4">
                        <RenderForm
                          field={field}
                          sectionName={section.name}
                          formData={formData}
                          handleFieldChange={handleFieldChange}
                          type={type}
                          handleGridEdit={handleGridEdit}
                          handleGridDelete={handleGridDelete}
                          editIcon={editIcon}
                          deleteIcon={deleteIcon}
                          dynamicSelectOptions={dynamicSelectOptions}
                          evaluateLogicConditions={evaluateLogicConditions}
                          selectRef={selectRef}
                          openSelects={openSelects}
                          setOpenSelects={setOpenSelects}
                          handleAddICD={handleAddICD}
                          pathname={pathname}
                          access={access}
                        />
                      </div>
                    ))}
                  {section.fields
                    .filter((field) => field.field_type === "grid")
                    .map((field) => (
                      <div key={field.id}>
                        <RenderForm
                          field={field}
                          sectionName={section.name}
                          formData={formData}
                          handleFieldChange={handleFieldChange}
                          type={type}
                          handleGridEdit={handleGridEdit}
                          handleGridDelete={handleGridDelete}
                          editIcon={editIcon}
                          deleteIcon={deleteIcon}
                          dynamicSelectOptions={dynamicSelectOptions}
                          evaluateLogicConditions={evaluateLogicConditions}
                          selectRef={selectRef}
                          openSelects={openSelects}
                          setOpenSelects={setOpenSelects}
                          handleAddICD={handleAddICD}
                          pathname={pathname}
                          access={access}
                        />
                      </div>
                    ))}
                </div>
              ))}
              <div className="w-full flex items-end !justify-end mb-6 !pr-4">
               {access && <button
                  type="submit"
                  className="bg-[#1969AE] hover:bg-[#155b96] text-white font-bold py-2 px-6 rounded"
                >
                  Save &amp; Close
                </button>}
              </div>
            </form>
          </Card>
        </div>
        {gridFieldMeta && (
          <CreateDrawer
            title={gridFieldMeta.sectionName}
            open={drawerOpen}
            onClose={() => setDrawerOpen(false)}
            editRow={editRowData}
            columns={gridFieldMeta?.columns}
            onSave={(rowData) => {
              if (editIndex !== null && editRowData) {
                // Update existing row
                const sectionKey = toCamelCase(gridFieldMeta.sectionName);
                setFormData((prevData: any) => {
                  const currentRows = [...(prevData[sectionKey] || [])];
                  currentRows[editIndex] = rowData;
                  return {
                    ...prevData,
                    [sectionKey]: currentRows,
                  };
                });
                setEditIndex(null);
                setEditRowData(null);
              } else {
                // Add new row
                addGridRow({
                  sectionName: gridFieldMeta.sectionName,
                  newRow: rowData,
                });
              }
              setDrawerOpen(false);
            }}

        />
      )}
     {cptDrawer && <CreateCPTDrawer
        open={cptDrawer}
        onClose={(createdData) => {
          console.log(createdData,formData,'parentData')
          if (createdData && createdData._id ) {
            const sectionName = 'Create Action & Status Code'; // Update this if your section name differs
            const sectionName1 = 'Add CPT Dictionary'
            const fieldKey = toCamelCase(fieldName);
            if (fieldKey === 'iCD') {
              getIcdList()
              setIds((prev) => ({
                ...prev,
                iCD: createdData._id,
              }));
              
              setFormData((prev) => ({
                ...prev,
                [toCamelCase(sectionName1)]: {
                  ...(prev[toCamelCase(sectionName1)] || {}),
                  [fieldKey]: createdData.code
                },
              }));
              
              setQuery(prev => ({
                ...prev,
                filters: createdData._id ? JSON.stringify({ icd: createdData._id }) : ''
              }));
              
            }else if (fieldKey === 'diagnosisCode') {
              getDiagnosisList()
              // const key = process as unknown as keyof typeof dynamicSelectOptions;
              const icdLabel = dynamicSelectOptions[`ICD`]?.find(opt => opt._id === createdData.icd)?.code || '';
              

              setIds((prev) => ({
                ...prev,
                diagnosisCode: createdData._id,
                iCD: createdData.icd,
              }));
              
              setFormData((prev) => ({
                ...prev,
                [toCamelCase(sectionName1)]: {
                  ...(prev[toCamelCase(sectionName1)] || {}),
                  [fieldKey]: createdData.name,
                  ...(icdLabel ? { iCD: icdLabel } : {}),
                },
              }));
              
              setQuery(prev => ({
                ...prev,
                filters: createdData.icd ? JSON.stringify({ icd: createdData.icd }) : ''
              }));
            }
            else if (fieldKey === 'speciality') {
              getSpecialityList()
              setIds((prev) => ({
                ...prev,
                speciality: createdData._id,
              }));
              
              setFormData((prev) => ({
                ...prev,
                [toCamelCase(sectionName1)]: {
                  ...(prev[toCamelCase(sectionName1)] || {}),
                  [fieldKey]: createdData.name
                },
              }));
              
              setQuery(prev => ({
                ...prev,
                filters: createdData._id ? JSON.stringify({ specialtyId: createdData._id }) : ''
              }));
              
            }else if (fieldKey === 'cPTCode') {
              getCPTCodeList()
              // const key = process as unknown as keyof typeof dynamicSelectOptions;
              const specialityLabel = dynamicSelectOptions[`Speciality`]?.find(opt => opt._id === createdData.specialtyId)?.name || '';
              

              setIds((prev) => ({
                ...prev,
                cPTCode: createdData._id,
                speciality: createdData.specialtyId,
              }));
              
              setFormData((prev) => ({
                ...prev,
                [toCamelCase(sectionName1)]: {
                  ...(prev[toCamelCase(sectionName1)] || {}),
                  [fieldKey]: createdData.code,
                  ...(specialityLabel ? { speciality: specialityLabel } : {}),
                },
              }));
              
              setQuery(prev => ({
                ...prev,
                filters: createdData.specialtyId ? JSON.stringify({ specialtyId: createdData.specialtyId }) : ''
              }));
            }
            else if (fieldKey === 'statusCode') {

                // const key = process as unknown as keyof typeof dynamicSelectOptions;
                const processLabel = dynamicSelectOptions[`Process`]?.find(opt => opt._id === createdData.processId)?.name || '';


                setIds((prev) => ({
                  ...prev,
                  statusCode: createdData._id,
                  process: createdData.processId,
                }));

                setFormData((prev) => ({
                  ...prev,
                  [toCamelCase(sectionName)]: {
                    ...(prev[toCamelCase(sectionName)] || {}),
                    [fieldKey]: createdData.code,
                    ...(processLabel ? { process: processLabel } : {}),
                  },
                }));

                setStatusQuery((prev) => ({
                  ...prev,
                  filters: createdData.processId ? JSON.stringify({ processId: createdData.processId }) : '',
                }));

                setActionQuery((prev) => ({
                  ...prev,
                  filters: createdData._id ? JSON.stringify({ statusCodeId: createdData._id }) : '',
                }));

              } else if (fieldKey === 'actionCode') {
                // Patch actionCode
                const processLabel = dynamicSelectOptions[`Process`]?.find(opt => opt._id === createdData?.statusCodeId?.processId)?.name || '';
                console.log('processLabel', processLabel)

                setIds((prev) => ({
                  ...prev,
                  actionCode: createdData._id,
                  statusCode: createdData?.statusCodeId?._id,
                  process: createdData?.statusCodeId?.processId,
                }));
                setFormData((prev) => ({
                  ...prev,
                  [toCamelCase(sectionName)]: {
                    ...(prev[toCamelCase(sectionName)] || {}),
                    [fieldKey]: createdData.code,
                    ...(createdData?.statusCodeId?.code ? { statusCode: createdData?.statusCodeId?.code } : {}),
                    ...(processLabel ? { process: processLabel } : {}),

                  },
                }));

                setStatusQuery((prev) => ({
                  ...prev,
                  filters: createdData?.statusCodeId?.processId ? JSON.stringify({ processId: createdData?.statusCodeId?.processId }) : '',
                }));

                setActionQuery((prev) => ({
                  ...prev,
                  filters: createdData?.statusCodeId?._id ? JSON.stringify({ statusCodeId: createdData?.statusCodeId?._id }) : '',
                }));
              }
            }

            setCptDrawer(false);
          }}
          fieldName={fieldName}
          onSave={(rowData) => {
            console.log(rowData);
          }}
        />}
      </div> :
        <SuccessPage />}
    </>
  );
};

export default ClientOnboardingForm;