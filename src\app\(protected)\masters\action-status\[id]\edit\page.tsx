"use client";
import {
  Typography,
  Icon,
} from "@mui/material";
import backIcon from '../../../../../../assests/backIcon.png'
import { useState } from "react";
import CreateClientPage from '../../../Form'
import { getTemplates } from "@/api/templates/templates";
import React from "react";
import Image from 'next/image'
import { getSubModulePermissionCommon } from "@/utils/generic";
import Loader from "@/components/loader/loader";

export default function EditClientPage() {
  const [form, setForm] = useState({});
 const [templateId, setTemplateId] = useState('')
 const [flattedValues, setFlattedValues] = useState([]);
  const [loader,setLoader] = useState(false)
    React.useEffect(() => {
      setLoader(true);
      getTemplates({ search: "", filters:{key:"action-status-code", type:'Master',isActive:true}}).then((res)=>{
 const template = res.templates.data.templates[0];
 setFlattedValues(res.templates.data.templates[0]?.view_summary?.inGrid);
      console.log('template',template);
      if (template && template.fields) {
        const fieldsData = template.fields;
        if (typeof fieldsData === 'string') {
          try {
            const unescaped = fieldsData.replace(/^"|"$/g, '').replace(/\\"/g, '"');

// Step 2: Parse it
const parsedJSON = JSON.parse(unescaped)[0]

console.log('parsedJSON',parsedJSON);
            // console.log('fieldsData',JSON.parse(JSON.parse(fieldsData)));
            // const parsedFields = JSON.parse(fieldsData);
            setTemplateId(template._id);
            setForm(parsedJSON);
             }catch(error) {
            console.error("Error parsing JSON:", error);
          }
          } else {
          setTemplateId(template._id);
          setForm(fieldsData);
        }
      } else {
        console.warn("Template or fields property is missing.");
      }
      }).catch((err)=>{
        console.error(err);
      }).finally(() => {
        setLoader(false);
      });
    },[])

   const getPermission=()=>{
            const permission =getSubModulePermissionCommon('Masters','Action and Status', 'Edit')?.isEnabled??false
            return permission
          }
  return (
    <>
    {loader && <Loader/>}
    <div className='px-6 py-4'>    
    <button
type="button"
className="text-sm font-200 text-gray-700 bg-none pl-4 py-2 m-0 rounded flex items-end !justify-end w-full"

>
<Icon onClick={()=>{
  if(typeof window !== 'undefined')
    {window.history.back();}
}}><Image src={backIcon} alt="add" width={20} height={20} /></Icon>  <Typography onClick={()=>{
  if(typeof window !== 'undefined')
    {window.history.back();}
}} className='!text-[18px]'>Back</Typography>
</button>
  {/* <hr className="mx-0 my-2 border-slate-100" /> */}
<CreateClientPage formTemplate={form} type="create" clientTyoe={'MAIN_CLIENT'} templateId={templateId} flattedValues={flattedValues} access={getPermission()}/>
</div>
</>
  );
}
