/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { Card } from "@/app/[components]/card";
import { RootState } from "@/store";
import { Checkbox, FormControlLabel, FormGroup, Icon, InputLabel, MenuItem, Table, TableBody, TableCell, TableHead, TableRow, TextField, Typography } from "@mui/material";
import Image from "next/image";
// import addIcon from '../../../assests/AddIcon.png'
import React, { useEffect, useRef, useState } from "react";
import {  useSelector } from "react-redux";
import { showToast } from "@/components/toaster/ToastProvider";
import {  toCamelCase } from "@/utils/generic";
import CreateDrawer from "@/app/[components]/createDrawer";
import addIcon from "../../../../assests/AddIcon.png";
import { createCPTCode, createDiagnosis, createIcd, createSpeciality, getCPTCodes, getICDCodes, getSpeciality } from "@/api/masters/cpt-code/cptCode";
import { createActionCode, createStatusCode, getProcessList, getStatusCodeList } from "@/api/masters/action-status/actionStatus";
import { usePathname } from "next/navigation";
import { getGlobalOptions } from "@/api/Globals/globals";
import { editIcon } from "@/app/[components]/editIcon";
import { deleteIcon } from "@/app/[components]/deleteIcon";

interface SelectOption {
  id?: string;
  code?: string;
  name?: string;
  value?: string;
  [key: string]: any; // For additional properties
}



const ClientOnboardingForm = ({type,formTemplate, templateId,fieldLabel,onClose }:{type:string,formTemplate:any,clientTyoe?:string,templateId:string,fieldLabel:string,onClose: (data?:any) => void;}) => {
  console.log('formTemplate',type);
  const collapsed = useSelector((state: RootState) => state.sideNav.sideNav);
  const formDataById:any = useSelector((state: RootState) => state.clientById);
  const [formData, setFormData] = useState<any>({});
  const [drawerOpen, setDrawerOpen] = useState(false);
 const pathname = usePathname()
 const [openSelects, setOpenSelects] = useState<Record<string, boolean>>({});
 const [globalOptions, setGlobalOptions] = useState<
     { id: string; value: string }[]
   >([]);
   const [isGlobalLoading, setIsGlobalLoading] = useState(false);
   const [fetchedGlobals, setFetchedGlobals] = useState<Record<string, boolean>>({});
  const [editIndex, setEditIndex] = useState<number | null>(null);
    const [editRowData, setEditRowData] = useState<any | null>(null);
    const selectRef = useRef<HTMLInputElement>(null);

const [gridFieldMeta, setGridFieldMeta] = useState<{
  sectionName: string;
  fieldId: string;
  columns: any[];
} | null>(null);
  // const [drawerOpen, setDrawerOpen] = React.useState(false);
  const [dynamicSelectOptions, setDynamicSelectOptions] = useState<Record<string, any[]>>({});
  const isEditMode = !collapsed;
  const columnClasses = isEditMode
    ? "grid-cols-1 md:grid-cols-3"
    : "grid-cols-1 md:grid-cols-4";
console.log('formData',formData);


 const fetchGlobalOptions = async (field: { label: string; }) => {
    if (!field.label) return;
    setIsGlobalLoading(true);
    console.log("Fetching global options for field:", field);

    try {
      const cleanedLabel = field.label.replace(/ field$/i, "").trim();
      const res = await getGlobalOptions({ name: cleanedLabel });
      console.log("Fetched global options:", res?.getGlobalByName);

      // const data = await res.json();
      const data = res?.getGlobalByName;
      if (Array.isArray(data)) {
        setGlobalOptions(data);
      } else {
        console.error("Unexpected global field data", data);
        setGlobalOptions([]);
      }
    } catch (err) {
      console.error("Error fetching global options:", err);
      setGlobalOptions([]);
    } finally {
      setIsGlobalLoading(false);
    }
  };

  useEffect(() => {
    if (!formTemplate?.sections) return;

    formTemplate.sections.forEach((section: any) => {
      section.fields.forEach((field: any) => {
        if (
          field.field_type === 'global_select' &&
          !fetchedGlobals[field.label]
        ) {
          fetchGlobalOptions(field);
          setFetchedGlobals(prev => ({ ...prev, [field.label]: true }));
        }
      });
    });
  }, [formTemplate]);

useEffect(() => {
  console.log('formTemplateeee',formTemplate)
    if (type === 'edit' && formDataById) {
            if(typeof formDataById?.values =='string')
     { const patchedData = JSON.parse(formDataById?.values);
      console.log('patchedData',patchedData);
      
      setFormData(patchedData);}
    }
  }, [type, formDataById]);

  useEffect(()=>{
    if (pathname.includes("/cpt-code")){
      getIcdList()
      getSpecialityList()
      getCPTCodeList()
       }
      
      getProcessData()
      getStatusCodeData()
    },[])

  const getIcdList = () =>{
    getICDCodes({})
    .then((res) => {
      // showToast.success(res?.code);
      setDynamicSelectOptions(prev => ({
        ...prev,
        ICD: res?.icds?.items ?? []
      }));
      console.log('res?.icds?.data?.items',res?.icds?.items)
    })
    .catch((err) => {
      showToast.error(err.message);
      console.error(err);
    });
   }

   const getProcessData = () => {
       getProcessList({})
         .then((res) => {
           setDynamicSelectOptions(prev => ({
             ...prev,
             Process: res?.processes?.data?.items ?? []
           }));
         })
         .catch((err) => {
           showToast.error(err.code);
           console.error(err);
         });
     };

     const getStatusCodeData = () => {
         getStatusCodeList({})
           .then((res) => {
             setDynamicSelectOptions(prev => ({
               ...prev,
               'Status Code': res?.statusCodes?.items ?? []
             }));
           })
           .catch((err) => {
             showToast.error(err.code);
             console.error(err);
           });
       };

   const getSpecialityList = () =>{
     getSpeciality({})
     .then((res) => {
       // showToast.success(res?.code);
       console.log('res data',res)
       setDynamicSelectOptions(prev => ({
         ...prev,
         Speciality: res?.specialties?.items ?? []
       }));
     })
     .catch((err) => {
       showToast.error(err.message);
       console.error(err);
     });
    }

    const getCPTCodeList = () =>{
      getCPTCodes({})
      .then((res) => {
        // showToast.success(res?.code);
        console.log('res data',res)
        setDynamicSelectOptions(prev => ({
          ...prev,
          "CPT Code": res?.cptCodes?.items ?? []
        }));
      })
      .catch((err) => {
        showToast.error(err.message);
        console.error(err);
      });
     }

  const handleFieldChange = (sectionName: string, fieldLabel: string, value: string|boolean|undefined) => {
    const section = toCamelCase(sectionName);
    const field = toCamelCase(fieldLabel);
    setFormData((prevData: any) => ({
      ...prevData,
      [section]: {
        ...(prevData[section] as Record<string, unknown> || {}),
        [field]: value,
      },
    }));
  };

  const handleSubmit = async(e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

 console.log(formData)
 
    


 if(fieldLabel === 'ICD'){
  const payload = {
    input: {
      templateId: templateId,
      values: JSON.stringify(formData) ?? '', 
      type: 'MAIN_CLIENT',
      code: formData?.addICD?.iCD ?? ''
    },
  };

  await createIcd(payload).then((res) => {
    showToast.success(res?.code);
    onClose(res?.createIcd?.data?.icd)
   
  })
  .catch((err) => {
    showToast.error(err.message);
    console.error(err);
  });
}else if(fieldLabel === 'Diagnosis Code'){
  const payload = {
    input: {
      templateId: templateId,
      values: JSON.stringify(formData) ?? '', 
      type: 'MAIN_CLIENT',
      name:formData?.addDiagnosisCode?.diagnosisCode ?? '',
      icd: formData?.addDiagnosisCode?.iCD._id ?? ''
    },
  };

  await createDiagnosis(payload).then((res) => {
    showToast.success(res?.code);
    onClose(res?.createDiagnosis?.data?.diagnosis)
    
    // router.push('/organizations');
  })
  .catch((err) => {
    showToast.error(err.message);
    console.error(err);
  });
}else if(fieldLabel === 'CPT Code'){
  const payload = {
    input: {
      code: formData?.addCPT?.cPT ?? '',
      values: JSON.stringify(formData) ?? '',
      templateId: templateId,
      specialtyId:formData?.addCPT?.speciality._id ?? ''
  }
  };

  await createCPTCode(payload).then((res) => {
    showToast.success(res?.code);
    onClose(res?.createCptCode?.data?.cptCode)
    console.log('ICD res',res)
    // router.push('/organizations');
  })
  .catch((err) => {
    showToast.error(err.message);
    console.error(err);
  });
}else if(fieldLabel === 'Status Code'){
  console.log('formData',formData)
  const payload = {
    input: {
      processId: formData?.addStatusCode?.process?._id ?? formData?.addStatusCode?.process ?? '',
      code: formData?.addStatusCode?.statusCode ?? '',
    },
  };

  await createStatusCode(payload).then((res) => {
    showToast.success(res?.code);
    onClose(res?.createStatusCode?.data?.data)
    // router.push('/organizations');
  })
  .catch((err) => {
    showToast.error(err.message);
    console.error(err);
  });
}else if(fieldLabel === 'Action Code'){
  console.log('formData',formData)
  const payload = {
    input: {
      statusCodeId: formData?.addActionCode?.statusCode?._id ?? formData?.addActionCode?.statusCode ?? '',
      code: formData?.addActionCode?.actionCode ?? '',
    },
  };

  await createActionCode(payload).then((res) => {
    showToast.success(res?.code);
    onClose(res?.createActionCode?.data?.data)
    // router.push('/organizations');
  })
  .catch((err) => {
    showToast.error(err.message);
    console.error(err);
  });
}else{
  const payload = {
    input: {
      type: 'MAIN_CLIENT',
      name: formData?.addSpeciality?.speciality ?? '',
      values: JSON.stringify(formData) ?? '', 
      templateId: templateId,
    },
  };

  await createSpeciality(payload).then((res) => {
    showToast.success(res?.code);
    onClose(res?.createSpecialty?.data?.specialty)
    
  })
  .catch((err) => {
    showToast.error(err.message);
    console.error(err);
  });
}

// const payload = {
//   input: {
//     templateId: templateId,
//     values: JSON.stringify(formData) ?? '', 
//     type: 'MAIN_CLIENT',
//     code: formData?.addICD?.iCD ?? ''
//   },
// };




 
  };
 
const addGridRow = ({
  sectionName,
  newRow,
}: {
  sectionName: string;
  newRow: Record<string, string>;
}) => {
  const sectionKey = toCamelCase(sectionName);
  console.log('newRow', newRow,sectionName);

  setFormData((prevData: any) => ({
    ...prevData,
    [sectionKey]: [
      ...(prevData[sectionKey] || []), // get existing rows if any
      newRow, // append the new row
    ],
  }));
};

function evaluateLogicConditions(
    conditions: any[],
    formData: any,
    logicJoinType: 'AND' | 'OR' = 'AND'
  ): boolean {
    const results = conditions.map((condition) => {
      const { fieldId, operator, value: expectedValue } = condition;

      let actualValue: any = undefined;

      // Step 1: Find the field label from formTemplate using fieldId
      let fieldLabel = '';
      for (const section of formTemplate.sections) {
        const found = section.fields.find((field: any) => field.id === fieldId);
        if (found) {
          fieldLabel = found.label;
          break;
        }
      }

      if (!fieldLabel) return false;

      const fieldKey = toCamelCase(fieldLabel);

      // Step 2: Find the value from formData
      for (const sectionKey in formData) {
        const section = formData[sectionKey];
        if (section && fieldKey in section) {
          const val = section[fieldKey];
          actualValue = typeof val === 'object'
            ? val?.value ?? val?.name ?? val?.code ?? val?.id
            : val;
          break;
        }
      }

      // Step 3: Apply logic
      switch (operator) {
        case '===':
          return actualValue === expectedValue;
        case '!==':
          return actualValue !== expectedValue;
        default:
          return false;
      }
    });

    return logicJoinType === 'AND' ? results.every(Boolean) : results.some(Boolean);
  }

  const handleGridEdit = (
    sectionName: string,
    fieldLabel: string,
    index: number,
    columns: any[],
    existingRow: any
  ) => {
    setGridFieldMeta({
      sectionName,
      fieldId: fieldLabel,
      columns,
    });
    setEditIndex(index);
    setEditRowData(existingRow);
    setDrawerOpen(true);
  };

  const handleGridDelete = (
      sectionName: string,
      index: number
    ) => {
      const sectionKey = toCamelCase(sectionName);
  
      setFormData((prev: any) => {
        const dataArray = prev[sectionKey];
        if (!Array.isArray(dataArray) || index < 0 || index >= dataArray.length) {
          return prev;
        }
        const updatedArray = dataArray.filter((_, i) => i !== index); // Remove by index
        return {
          ...prev,
          [sectionKey]: updatedArray,
        };
      });
    };
  





 const renderField = (field: {
    label: string;
    field_type: string;
    placeholder: any;
    required: boolean | undefined;
    options: any[];
    id: React.Key | null | undefined;
    columns: any[];
    rows: any[];
    logicConditions?: any[];
    logicJoinType: string;
  }, sectionName: string) => {

    console.log('renderField')
    const section = toCamelCase(sectionName);
    const fieldName = toCamelCase(field.label);
    // const fieldValue = field.label;
    const rawValue = formData?.[section]?.[fieldName] ?? "";
    const options = dynamicSelectOptions[field.label] || field.options || [];

    const valueObj = typeof rawValue === 'object'
      ? rawValue
      : options.find(opt => opt.id === rawValue || opt.code === rawValue || opt.value === rawValue);

    const value: any = valueObj || rawValue;
    const tableValue = formData?.[section];

    console.log('valueeeeeee', formData)


    const getDisplayValue = (val: string | SelectOption): string => {
      console.log('valueeeeeee', val)
      if (typeof val === 'object') {
        return val?.code || val?.name || val?.value || '';
      }
      return val;
    };



    if (field?.logicConditions && field?.logicConditions?.length > 0) {
      const isVisible = evaluateLogicConditions(
        field?.logicConditions,
        formData,
        field?.logicJoinType === 'AND' ? field?.logicJoinType : 'OR'
      );
      if (!isVisible) return null;
    }
    console.log('field.field_type', field.field_type);

    switch (field.field_type) {
      case "text":
      case "email":
      case "phone":
      case "number":
      case "date":
      case "password":
        return (
          <>
            <InputLabel className="!text-[22px] font-medium text-gray-800" shrink>
              {field.label}
              {field?.required && <span style={{ color: "red" }}> *</span>}
            </InputLabel>

            <TextField
              fullWidth
              type={
                field.field_type === "phone"
                  ? "tel"
                  : field.field_type === "number"
                    ? "number"
                    : field.field_type === "date"
                      ? "date" 
                        : field.field_type === "password" ? 'password' : "text"
              }
              inputProps={
                field.field_type === "number"
                  ? { inputMode: "numeric", pattern: "[0-9]*" }
                  : {}
              }
              InputProps={{
                sx: {
                  "& input.Mui-disabled": {
                    color: "#000000",
                    WebkitTextFillColor: "#000000", //for Safari/Chrome
                  },
                  color: value === '' ? '#aca1a49e' : 'black'
                },
              }}
              placeholder={field.placeholder || field.label}
              value={getDisplayValue(value)}
              className={`${(type === 'edit' && field.label.includes('Email')) ? 'bg-gray-100' : 'bg-transparent'}`}
              disabled={type === 'edit' && field.label.includes('Email')}
              onChange={(e) => handleFieldChange(sectionName, field.label, e.target.value)}
              required={field.required}
            />
          </>

        );
      case 'multiselect':
        return (
          <TextField
            select
            fullWidth

            value={value || []} // should be an array
            onChange={(e) => handleFieldChange(sectionName, field.label, e.target.value)}
            required={field.required}
            size="small"
            slotProps={{
              select: {
                displayEmpty: true,
                multiple: true,
                renderValue: (selected: any) => {
                  if (!selected || selected === "") {
                    return <em className="text-gray-400">{field.placeholder || `Select ${field.label}`}</em>;
                  }
          
                  const options =
                    (field.options?.length > 0
                      ? field.options
                      : dynamicSelectOptions[field.label]) || [];
          
                  const found = options.find(
                    (opt: any) =>
                      opt.code === selected ||
                      opt.name === selected ||
                      opt.value === selected
                  );
          
                  return found?.code || found?.name || found?.value || selected;
                },
                open: openSelects[field.label] || false,
                onOpen: () => setOpenSelects((prev) => ({ ...prev, [field.label]: true })),
                onClose: () => setOpenSelects((prev) => ({ ...prev, [field.label]: false })),
              },
            }}
            SelectProps={{
              multiple: true,
              MenuProps: {
                PaperProps: {
                  sx: {
                    maxHeight: "12rem",
                    overflowY: "auto",
                  },
                },
              },
            }}
            InputProps={{
              sx: {
                "& input.Mui-disabled": {
                  color: "#000000",
                  WebkitTextFillColor: "#000000", //for Safari/Chrome
                },
                color: value === '' ? '#aca1a49e' : 'black'
              },
            }}
          >
            {field.options?.map((opt) => (
              <MenuItem key={opt.id} value={opt.value}>
                {opt.value}
              </MenuItem>
            ))}
          </TextField>
        )
        case "image":
              case "file_upload":
                return (
                  <TextField
                    fullWidth
                    type={
                      field.field_type === "image" || field.field_type === "file_upload" ? "file"
                        : field.field_type === "password" ? 'password' : "text"
                    }
        
                    InputProps={{
                      sx: {
                        "& input.Mui-disabled": {
                          color: "#000000",
                          WebkitTextFillColor: "#000000", //for Safari/Chrome
                        },
                      },
                    }}
                    placeholder={field.placeholder || field.label}
                    // value={value}
                    className={`${(type === 'edit' && field.label.includes('Email')) ? 'bg-gray-100' : 'bg-transparent'}`}
                    disabled={type === 'edit' && field.label.includes('Email')}
                    onChange={(e) => {
                      const target = e.target as HTMLInputElement;
                      handleFieldChange(sectionName, field.label, ((field.field_type === "image" || field.field_type === "file_upload") ? target.files?.[0].name : target.value));
                    }}
                    // onChange={(e) => handleFieldChange(sectionName, field.label, (( field.field_type === "image" || field.field_type === "file_upload")?e.target?.files[0].name:e.target.value))}
                    required={field.required}
                  />
                );
      case "select":
        return (
          <>
            <InputLabel className="!text-[22px] font-medium text-gray-800" shrink>
              {field.label}
              {field?.required && <span style={{ color: "red" }}> *</span>}
            </InputLabel>
            <TextField
              select
              size="small"
              fullWidth
              value={
                field.label === "Status" && (!value || value === "")
                  ? "Active"
                  : getDisplayValue(value)
              }
              disabled={
                field.label === "Diagnosis Code"
                  ? !formData?.[section]?.[toCamelCase('ICD')]
                  : field.label === "CPT Code"
                    ? !formData?.[section]?.[toCamelCase('Speciality')]
                    : false
              }
              SelectProps={{
                multiple: false,
                MenuProps: {
                  PaperProps: {
                    sx: {
                      maxHeight: "12rem",
                      overflowY: "auto",
                    },
                  },
                },
              }}
              
              InputProps={{
                sx: {
                  "& input.Mui-disabled": {
                    color: "#000000",
                    WebkitTextFillColor: "#000000", //for Safari/Chrome
                  },
                },
              }}
              inputRef={selectRef}
              onChange={(e) => {
                const selectedValue = e.target.value;
                console.log('selectedValue', selectedValue)
                const options = dynamicSelectOptions[field.label] || field.options || [];
                const fullOption = options.find(opt =>
                  opt.code === selectedValue ||
                  opt.name === selectedValue ||
                  opt.value === selectedValue
                );
                handleFieldChange(sectionName, field.label, fullOption || selectedValue);
              }}
              required={field.required}
              slotProps={{
                select: {
                  displayEmpty: true,
                  multiple: false,
                  renderValue: (selected: any) => {
                    if (!selected || selected === "") {
                      return <em className="text-gray-400">{field.placeholder || `Select ${field.label}`}</em>;
                    }
            
                    const options =
                      (field.options?.length > 0
                        ? field.options
                        : dynamicSelectOptions[field.label]) || [];
            
                    const found = options.find(
                      (opt: any) =>
                        opt.code === selected ||
                        opt.name === selected ||
                        opt.value === selected
                    );
            
                    return found?.code || found?.name || found?.value || selected;
                  },
                  open: openSelects[field.label] || false,
                  onOpen: () => setOpenSelects((prev) => ({ ...prev, [field.label]: true })),
                  onClose: () => setOpenSelects((prev) => ({ ...prev, [field.label]: false })),
                },
              }}
            >
              {(dynamicSelectOptions[field.label] || field.options || []).map((opt) => (
                <MenuItem key={opt.id} value={opt.code || opt.name || opt.value}>
                  {opt.code || opt.name || opt.value}
                </MenuItem>
              ))}

            </TextField>
          </>
        );
      case "global_select":
        console.log("global:", field);

        return (
          <>
            <InputLabel className="!text-[22px] font-medium text-gray-800" shrink>
              {field.label}
              {field?.required && <span style={{ color: "red" }}> *</span>}
            </InputLabel>
            <TextField
              select
              fullWidth
              size="small"
              required={field.required}
              value={getDisplayValue(value)}
              inputRef={selectRef}
              onChange={(e) => {
                const selectedValue = e.target.value;
                console.log('selectedValue', selectedValue)
                const options = dynamicSelectOptions[field.label] || field.options || [];
                const fullOption = options.find(opt =>
                  opt.code === selectedValue ||
                  opt.name === selectedValue ||
                  opt.value === selectedValue
                );
                handleFieldChange(sectionName, field.label, fullOption || selectedValue);
              }}
              slotProps={{
                select: {
                  displayEmpty: true,
                  multiple: false,
                  renderValue: (selected: any) => {
                    if (!selected || selected === "") {
                      return <em className="text-gray-400">{field.placeholder || `Select ${field.label}`}</em>;
                    }
            
                    const options =
                      (field.options?.length > 0
                        ? field.options
                        : dynamicSelectOptions[field.label]) || [];
            
                    const found = options.find(
                      (opt: any) =>
                        opt.code === selected ||
                        opt.name === selected ||
                        opt.value === selected
                    );
            
                    return found?.code || found?.name || found?.value || selected;
                  },
                  open: openSelects[field.label] || false,
                  onOpen: () => setOpenSelects((prev) => ({ ...prev, [field.label]: true })),
                  onClose: () => setOpenSelects((prev) => ({ ...prev, [field.label]: false })),
                },
              }}
            >
              <MenuItem disabled value="">
                {field.placeholder || `Select ${field.label}`}
              </MenuItem>

              {isGlobalLoading ? (
                <MenuItem disabled value="">
                  Loading options...
                </MenuItem>
              ) : globalOptions.length === 0 ? (
                <MenuItem disabled value="">
                  No options found
                </MenuItem>
              ) : (
                globalOptions.map((opt) => (
                  <MenuItem key={opt.id} value={opt.value}>
                    {opt.value}
                  </MenuItem>
                ))
              )}
            </TextField>
          </>


        );
      case "textarea":
        return (
          <>
            <InputLabel className="!text-[22px] font-medium text-gray-800" shrink>
              {field.label}
              {field?.required && <span style={{ color: "red" }}> *</span>}
            </InputLabel>
            <TextField
              fullWidth
              multiline
              minRows={3}
              placeholder={field.placeholder || field.label}
              value={getDisplayValue(value)}
              onChange={(e) => handleFieldChange(sectionName, field.label, e.target.value)}
              required={field.required}
            />
          </>
        );
      case "checkboxes":
        return (
          // <div className="flex gap-2 items-start">
          <FormGroup>
            {field.options.map((option) => (
              <FormControlLabel
                key={option.id}
                control={
                  <Checkbox
                    checked={value?.includes(option.id)}
                    onChange={(e) => {
                      const newValue = e.target.checked
                        ? [...(value || []), option.id]
                        : (value || []).filter((v: string) => v !== option.id);
                      handleFieldChange(sectionName, field.label, newValue);
                    }}
                  />
                }
                label={option.value}
              />
            ))}
          </FormGroup>
        )
      case "grid":
        const gridRows = Array.isArray(tableValue) ? tableValue : []; // value from formData
        console.log('gridRows', gridRows);
        return (
          <div key={field.id} className="px-4 pb-4 overflow-auto">
            <div style={{ overflowX: "auto" }}>
              <Table
                sx={{
                  border: "1px solid #E2E8F0",
                  tableLayout: "fixed",
                  minWidth: '100%', // ensures scroll when many columns
                  width: "100%",
                }}
              >
                <TableHead sx={{ backgroundColor: "#f0fdff" }}>
                  <TableRow>
                    {field.columns.map((col) => {
                      return (
                        <TableCell
                          key={col.id}
                          sx={{
                            color: "#1E40AF",
                            fontWeight: 600,
                            width: "200px",
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                          }}
                        >
                          {col.name}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {gridRows.length > 0 ? (
                    gridRows.map((row, rowIndex) => (
                      <TableRow key={rowIndex}>
                        {field.columns.map((col) => {
                          const isAction = col.name === "Actions" || col.name === "Action";
                          return (
                            <TableCell
                              key={col.id}
                              className={isAction ? "sticky right-0 z-10" : ""}
                              sx={{
                                width: isAction ? "200px" : "auto",
                                maxWidth: isAction ? "200px" : "auto",
                                whiteSpace: "nowrap",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                              }}
                            >
                              {isAction ? (
                                <div className="flex gap-2.5 items-center">
                                  <button
                                    type="button"
                                    onClick={() =>
                                      handleGridEdit(
                                        sectionName,
                                        field.label,
                                        rowIndex,
                                        field.columns,
                                        row
                                      )
                                    }
                                    className="p-2 rounded hover:bg-blue-50 transition-colors"
                                  >
                                    {editIcon}
                                  </button>
                                  <button
                                    type="button"
                                    onClick={() => handleGridDelete(sectionName, rowIndex)}
                                    className="p-2 rounded hover:bg-red-50 transition-colors"
                                  >
                                    {deleteIcon}
                                  </button>
                                </div>
                              ) : (
                                row?.[toCamelCase(col.name)] ?? row?.[col.id] ?? ""
                              )}
                            </TableCell>
                          );
                        })}
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell
                        colSpan={field.columns.length}
                        align="center"
                        sx={{ color: "#64748B", fontSize: "14px" }}
                      >
                        No Data Found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>


        );

      default:
        return <div>Unsupported field type</div>;
    }
  };


  return (
    <div className="mt-4">
          <div className="space-y-6">
            <Card className="border border-gray-300 !p-0 !rounded-lg">
              
    <form onSubmit={handleSubmit} className="space-y-6">
      {formTemplate?.sections?.map((section:{id:string,name:string,fields:any[]}) => (
        <div key={section.id}>
           <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2 bg-blue-50 px-2 py-1 pt-[10px]">
                          <Typography variant="h6">{section.name}</Typography>
          
                          {/* Check if any grid field exists */}
                          {section.fields.some((f) => f.field_type === "grid") && (
                            <button
   onClick={() =>
   {
    setGridFieldMeta({
      sectionName: section.name,
      fieldId: section.id,
      columns: section.fields[0].columns,
    });  setDrawerOpen(true)}
  }
      type="button"
      className="text-sm font-200 text-gray-700 bg-none px-4 py-2 rounded flex"
    >
                              <Icon><Image src={addIcon} alt="add" width={20} height={20} /></Icon>
                              <span>Add Row</span>
                            </button>
                          )}
                        </div>
              <div className={`grid ${columnClasses} gap-4 p-4`}>

          {section.fields.filter((field) => field.field_type !== "grid").map((field) => (
            <div key={field.id}>
              {/* <InputLabel className="!text-[22px] font-medium text-gray-800" shrink>
                                     {field.label}
                                     {field?.required && <span style={{ color: "red" }}> *</span>}
                                   </InputLabel> */}
              {renderField(field, section.name)}
            </div>
            
          ))}
        </div>
         {section.fields.filter((field) => field.field_type === "grid").map((field) => (
            <div key={field.id}>
{renderField(field, section.name)}
            </div>
             ))}
        </div>
      ))}
      <div className="w-full flex items-end !justify-end mb-6 !pr-4">
 <button
  type="submit"
  className="bg-[#1969AE] hover:bg-[#155b96] text-white font-bold py-2 px-6 rounded "
  // onClick={()=>handleSaveAndClose()}
>
  Save &amp; Close
</button>
</div>
    </form>
     </Card>
          </div>
         {gridFieldMeta && (
        <CreateDrawer
          title={gridFieldMeta.sectionName}
          open={drawerOpen}
          onClose={() => setDrawerOpen(false)}
          editRow={editRowData}
          columns={gridFieldMeta?.columns}
          onSave={(rowData) => {
            if (editIndex !== null && editRowData) {
              // Update existing row
              const sectionKey = toCamelCase(gridFieldMeta.sectionName);
              setFormData((prevData: any) => {
                const currentRows = [...(prevData[sectionKey] || [])];
                currentRows[editIndex] = rowData;
                return {
                  ...prevData,
                  [sectionKey]: currentRows,
                };
              });
              setEditIndex(null);
              setEditRowData(null);
            } else {
              // Add new row
              addGridRow({
                sectionName: gridFieldMeta.sectionName,
                newRow: rowData,
              });
            }
            setDrawerOpen(false);
          }}

        />
      )}
          </div>
  );
};

export default ClientOnboardingForm;
