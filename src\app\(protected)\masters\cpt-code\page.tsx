/* eslint-disable @typescript-eslint/no-explicit-any */
// import { useRouter } from 'next/navigation';
'use client';
import { setClientTable } from "@/features/client/clientSlice";
import React from "react";
import { useDispatch, useSelector } from "react-redux";
// import ClientDataTable from "../../ClientDataTable";
import { TableData } from "@/types/user";
import { convertFormJsonFromClients, transformedClients } from "@/utils/generic";
import ClientDataTable from "@/app/[components]/tableGrid/ClientDataTable";
import { getAllCpt } from "@/api/masters/cpt-code/cptCode";
import { getTemplates } from "@/api/templates/templates";
import { setHeaders, setHeadersDefault } from "@/features/headers/headersSlice";
import Loader from "@/components/loader/loader";
import { RootState } from "@/store";

  export default function Page() {
  const headerProcess:any =  useSelector((state: RootState) => state.headerProcess);
    // const { id } = useParams();
  const dispatch = useDispatch();
  const [query, setQuery] = React.useState({
        page: 1,
            limit: 10,
            search: '',
           sortBy: '',
        sortOrder: 'asc',
            // filters:{},
        });
  const [pagination, setPagination] = React.useState(1);

  const [loader, setLoader] = React.useState(false);
  const [roles,setRoles]= React.useState<any>([])
  
  React.useEffect(() => {
    console.log('headerProcess', headerProcess);
  
    const role=headerProcess?.role?.permissions?.filter((item:{moduleName:string}) =>
        item.moduleName.includes('Masters')
      )[0].subModules?.filter((data:{moduleName:string})=>data.moduleName=="CPT Dictionary")[0].permissions
      setRoles(role);
    }, [headerProcess]);

    React.useEffect(() => {
     handleGetApi()
     console.log('each');
    }, [query]);
    
  const handleGetApi=(view?:any)=>{
  
     setLoader(true)
        // const payload=query
          getTemplates({ search: "", filters:{key:"cpt", type:'Master',isActive:true}}).then((res) => {
              const template = res.templates.data.templates[0];
              console.log('template', template);
               if(view==undefined){
              dispatch(setHeaders(res.templates.data.templates[0]?.view_summary?.inGrid))
              dispatch(setHeadersDefault(res.templates.data.templates[0]?.view_summary?.default))
               }
              const result = Object.fromEntries(res.templates.data.templates[0]?.view_summary?.inGrid.map((key: any) => [key, 1]));
              
              const payload = {
                ...query,
                selectedFields: result,
              }; 

              console.log('payload',payload)
              getAllCpt(view==undefined?payload:view).then((res)=>{
        console.log('each second',res);
        setPagination(res.cpts.pagination)
          const data = transformedClients(res.cpts.items);
          
                    const tableData = convertFormJsonFromClients(data);
                    console.log('Cpts Table',tableData)
        dispatch(setClientTable(tableData as TableData));
        setLoader(false)
        }).catch((err)=>{
          setLoader(false)
          console.error(err);
        })
              })   .catch((err) => {
              setLoader(false)
              console.error(err);
            });
  
  
  //   getActionCodes(query).then((res)=>{
  // console.log('res11',res);
  // setPagination(res.actionStatusCodeMaps.pagination)
  //     const data = convertFormJsonFromMasters(res.actionStatusCodeMaps.items);
      
  //     dispatch(setClientTable(data as TableData));
  //     }).catch((err)=>{
  //       console.error(err);
  //     })
    }

return( <>{loader&& <Loader />}
  <ClientDataTable title={"CPT Dictionary"} handleGetApi={handleGetApi} query={query} pagination={pagination} setQuery={setQuery} role={roles}/>;
  </>)

}

  