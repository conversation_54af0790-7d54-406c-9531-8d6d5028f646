/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';
import { Drawer } from "@mui/material";
import { useState } from "react";
import CreateClientPage from './cpt-code/Form'
import { getTemplates } from "@/api/templates/templates";
import React from "react";
import Loader from "@/components/loader/loader";
const CreateCPTDrawer = ({
  open,
  onClose,
  fieldName,
}: {
  open: boolean;
  onClose: (data:any) => void;
  fieldName: string;
  onSave: (rowData: Record<string, string>) => void;
}) => {

  console.log(fieldName)
  const [loader,setLoader] = useState(false)
  // const [rowData, setRowData] = useState<Record<string, string>>({});
    // const [keyWord,setKeyWord] = useState('')
  // const handleChange = (colId: string, value: string) => {
  //   setRowData((prev) => ({ ...prev, [toCamelCase(colId)]: value }));
  // };
 
  // const handleSave = () => {
  //     const newRow = {
  //       // id: `row_${Date.now()}`,
  //       ...rowData,
  //     };
  //     console.log('newRow',newRow);
      
  //     onSave(newRow);
  //     // if (closeDrawer) {
  //       setRowData({});
  //       onClose();
  //     // }
  //   };

   const [form, setForm] = useState({});
   const [templateId, setTemplateId] = useState('')
      React.useEffect(() => {
        
        let keyData 
        if(fieldName === 'ICD'){
          keyData = 'icd-code'
        }else if(fieldName === 'Diagnosis Code'){
          keyData = 'diagnois-code'
        }else if(fieldName === 'CPT Code'){
          keyData = 'cpt-code'
        }else if(fieldName === 'Status Code'){
          keyData = 'status-code'
        }else if(fieldName === 'Action Code'){
          keyData = 'action-code'
        }else{
          keyData = 'speciality'
        }
    if(open)
      setLoader(true)
      {  getTemplates({ search: "", filters:{key:keyData, type:'Master',isActive:true}}).then((res)=>{
   const template = res.templates.data.templates[0];
        console.log('template',template);
        if (template && template.fields) {
          const fieldsData = template.fields;
          if (typeof fieldsData === 'string') {
            try {
              const unescaped = fieldsData.replace(/^"|"$/g, '').replace(/\\"/g, '"');
  
  // Step 2: Parse it
  const parsedJSON = JSON.parse(unescaped);
  
  console.log('parsedJSON',parsedJSON[0]);
              // console.log('fieldsData',JSON.parse(JSON.parse(fieldsData)));
              
              // const parsedFields = JSON.parse(fieldsData);
              setTemplateId(template._id);
              setForm(parsedJSON[0]);
               } catch (error) {
              console.error("Error parsing JSON:", error);
              setForm({})
            }
            } else {
            setTemplateId(template._id);
            setForm(fieldsData);
          }
        } else {
          console.warn("Template or fields property is missing.");
        }
        }).catch((err)=>{
          console.error(err);
          setForm({})
        }).finally(()=>{
          setLoader(false)
        })
      }
      },[fieldName,open])


  return (
    <>
    {loader && <Loader/>}
    <Drawer anchor="right" open={open} onClose={onClose}>
      <div className="w-[830px] p-6 flex flex-col h-full">
      <CreateClientPage formTemplate={form} type="create"  onClose={(data) => {
        onClose(data)
        console.log('close data',data)
  }} clientTyoe={'MAIN_CLIENT'} templateId={templateId} fieldLabel={fieldName}/>
      </div>
    </Drawer>
    </>
    
  );
};

export default CreateCPTDrawer;
