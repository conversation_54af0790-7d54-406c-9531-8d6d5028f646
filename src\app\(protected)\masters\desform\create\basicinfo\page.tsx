"use client";
import {
  TextField,
  Typo<PERSON>,
  InputLabel,
  MenuItem,
  Select,
  Box,
  Paper,
  Switch,
} from "@mui/material";
import { useState, useEffect } from "react";
import {
  createTemplate,
  getByTemplateId,
  updateTemplate,
} from "@/api/dynamicTemplate/template";
import { showToast } from "@/components/toaster/ToastProvider";
import { useRouter } from "next/navigation";
import { useSearchParams } from "next/navigation";
import { getOrganizationUserPagination } from "@/api/organizations/organizations";
import { getSubModulePermissionCommon } from "@/utils/generic";

export default function Page() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const templateId = searchParams.get("id");
  const [rowData, setRowData] = useState<Record<string, string | boolean>>({
    useTemplate: false, // default value for toggle
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [mainClientOptions, setMainClientOptions] = useState<
    { _id: string; name: string }[]
  >([]);
  const [subClientOptions, setSubClientOptions] = useState<
    { _id: string; name: string }[]
  >([]);
  const showClientFields =
    typeof rowData.templateType === "string" &&
    rowData.templateType.toLowerCase() !== "master" &&
    rowData.templateType.toLowerCase() !== "ticketing";

  useEffect(() => {
    if (showClientFields) {
      getOrganizationUserPagination({
        input: { filters: { type: "main_client" } },
      })
        .then((res) => {
          setMainClientOptions(
            res?.getOrganisationsWithPagination?.users || []
          );
        })
        .catch(() => setMainClientOptions([]));
    }
  }, [showClientFields]);

  useEffect(() => {
    if (showClientFields && rowData.organisationId) {
      getOrganizationUserPagination({
        input: { filters: { main_client: rowData.organisationId } },
      })
        .then((res) => {
          setSubClientOptions(res?.getOrganisationsWithPagination?.users || []);
        })
        .catch(() => setSubClientOptions([]));
    } else {
      setSubClientOptions([]);
    }
  }, [showClientFields, rowData.organisationId]);

  useEffect(() => {
    if (templateId) {
      getByTemplateId(templateId)
        .then((res) => {
          const template = res?.template?.data?.template;
          if (template) {
            setRowData({
              templateName: template.name || "",
              templateType: template.type || "",
              status: template.isActive ? "Active" : "In-Active",
              organisationId: template.organisationId || "",
              subOrganisationId: template.subOrganisationId || "",
              useTemplate: !!template.useTemplate, // ensure boolean
            });
          }
        })
        .catch((err) => {
          console.error("Failed to fetch template data", err);
          showToast.error("Unable to load template for editing.");
        });
    }
  }, [templateId]);

  // Ensure toggles are always boolean
  const handleChange = (colId: string, value: string | boolean) => {
    const fieldType = requiredFields.find((f) => f.id === colId)?.type;
    if (fieldType === "toggle") {
      setRowData((prev) => ({ ...prev, [colId]: !!value }));
    } else {
      setRowData((prev) => ({ ...prev, [colId]: value }));
    }
  };

  type TemplateInput = {
    name: string;
    type: string;
    key?: string;
    organisationId?: string;
    subOrganisationId?: string;
    useTemplate?: boolean;
    id?: never;
  };

  const handleSave = async () => {
    try {
      setIsSubmitting(true);

      const missingFields = requiredFields
        .filter((field) => {
          if (!field.required) return false;
          if (
            (field.id === "organisationId" ||
              field.id === "subOrganisationId") &&
            !showClientFields
          )
            return false;
          // For toggle, allow false as valid value
          if (field.type === "toggle") return false;
          return !rowData[field.id];
        })
        .map((field) => field.name);

      if (missingFields.length > 0) {
        showToast.error(
          `Please fill in all required fields: ${missingFields.join(", ")}`
        );
        setIsSubmitting(false);
        return;
      }

      const isEdit = !!templateId;
      let response;
      if (isEdit && typeof templateId === "string") {
        // Update flow: do NOT send key or useTemplate
        const updateInput: TemplateInput = {
          name: rowData.templateName as string,
          type: rowData.templateType as string,
        };
        if (showClientFields) {
          updateInput.organisationId = rowData.organisationId as string;
          updateInput.subOrganisationId = rowData.subOrganisationId as string;
        }
        response = await updateTemplate({
          input: { ...updateInput, id: templateId },
        });
        showToast.success("Template updated successfully");
        router.push(
          `/masters/desform/create/template?id=${response?.updateTemplate?.data?.template?._id}`
        );
      } else {
        // Create flow: send key and useTemplate
        const input: TemplateInput = {
          name: rowData.templateName as string,
          type: rowData.templateType as string,
          key: rowData.templateName as string,
          useTemplate: !!rowData.useTemplate,
        };
        if (showClientFields) {
          input.organisationId = rowData.organisationId as string;
          input.subOrganisationId = rowData.subOrganisationId as string;
        }
        response = await createTemplate({ input });
        showToast.success("Template created successfully");
        router.push(
          `/masters/desform/create/template?id=${response?.createTemplate?.data?.template?._id}`
        );
        setRowData({ useTemplate: false }); // reset toggle
      }
    } catch (error) {
      console.error("Error saving template:", error);
      showToast.error("Failed to save template. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  interface OptionType {
    _id: string;
    name: string;
    id?: string;
  }

  interface RequiredField {
    id: string;
    name: string;
    required: boolean;
    type: "text" | "select" | "toggle";
    options?: string[] | OptionType[];
    hide?: boolean;
    disabled?: boolean; // <-- add this
    getOptionLabel?: (option: OptionType) => string;
    getOptionValue?: (option: OptionType) => string;
  }

  const requiredFields: RequiredField[] = [
    {
      id: "templateName",
      name: "Template Name",
      required: true,
      type: "text",
    },
    {
      id: "templateType",
      name: "Template Type",
      required: true,
      type: "select",
      options: [
        "provider_credentials",
        "patient_scheduling",
        "vob",
        "medical_coding",
        "prior_authorization",
        "billing_&_rejection",
        "payment_posting",
        "account_recievable_&_denial",
        "patient_invoicing",
        "Master",
        "ticketing",
      ],
    },
    {
      id: "organisationId",
      name: "Main Client",
      required: showClientFields,
      type: "select",
      options: mainClientOptions as OptionType[],
      hide: !showClientFields,
      disabled: !!templateId,
      getOptionLabel: (option: OptionType) => option.name,
      getOptionValue: (option: OptionType) => option._id,
    },
    {
      id: "subOrganisationId",
      name: "Sub Client",
      required: false,
      type: "select",
      options: subClientOptions as OptionType[],
      hide: !showClientFields,
      disabled: !!templateId, // <-- disable during update
      getOptionLabel: (option: OptionType) => option.name,
      getOptionValue: (option: OptionType) => option._id,
    },
    {
      id: "useTemplate",
      name: "Use Template",
      required: false,
      type: "toggle",
      hide: !!templateId, // Hide toggle in update flow
    },
  ];

  useEffect(() => {
    if (!showClientFields) {
      setRowData((prev) => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { MainClient, subClient, ...rest } = prev;
        return rest;
      });
    }
  }, [showClientFields]);

  const renderField = (field: RequiredField) => {
    if (field.hide) return null;
    const value = rowData[field.id] ?? (field.type === "toggle" ? false : "");

    return (
      <Box key={field.id} className="flex flex-col mb-4 w-full">
        <InputLabel
          htmlFor={field.id}
          shrink
          className="text-lg font-semibold text-gray-800 mb-1 select-none"
        >
          {field.name}
          {field.required && <span className="text-red-500 ml-1">*</span>}
        </InputLabel>

        {field.type === "select" ? (
          <Select
            fullWidth
            id={field.id}
            value={value}
            onChange={(event) =>
              handleChange(field.id, (event.target as HTMLInputElement).value)
            }
            size="small"
            sx={{ borderRadius: "5px" }}
            required={field.required}
            displayEmpty={true}
            disabled={
              field.disabled || // <-- use field.disabled
              !getSubModulePermissionCommon(
                "Masters",
                "Des Form",
                "Edit Template"
              )?.isEnabled
            }
          >
            <MenuItem disabled value="">
              Select {field.name}
            </MenuItem>
            {Array.isArray(field.options)
              ? field.options.map((option: OptionType | string) => {
                  const label = field.getOptionLabel
                    ? field.getOptionLabel(option as OptionType)
                    : typeof option === "string"
                      ? option
                          .replace(/_/g, " ")
                          .toLowerCase()
                          .replace(/\b\w/g, (c: string) => c.toUpperCase())
                      : (option as OptionType).name;
                  const valueOpt = field.getOptionValue
                    ? field.getOptionValue(option as OptionType)
                    : typeof option === "string"
                      ? option
                      : (option as OptionType).id;
                  return (
                    <MenuItem key={valueOpt} value={valueOpt}>
                      {label}
                    </MenuItem>
                  );
                })
              : null}
          </Select>
        ) : field.type === "toggle" ? (
          <Switch
            checked={!!value}
            onChange={(e) => handleChange(field.id, e.target.checked)}
            disabled={
              !getSubModulePermissionCommon(
                "Masters",
                "Des Form",
                "Edit Template"
              )?.isEnabled
            }
          />
        ) : (
          <TextField
            fullWidth
            id={field.id}
            value={value}
            onChange={(e) => handleChange(field.id, e.target.value)}
            size="small"
            disabled={
              !getSubModulePermissionCommon(
                "Masters",
                "Des Form",
                "Edit Template"
              )?.isEnabled
            }
            variant="outlined"
            placeholder={`Enter ${field.name}`}
            required={field.required}
            multiline={field.id === "description"}
            rows={field.id === "description" ? 3 : 1}
          />
        )}
      </Box>
    );
  };

  const drawerTitle = templateId
    ? `Edit Basic Information`
    : `Create Basic Information`;

  return (
    <Paper
      elevation={2}
      className="w-full  p-8 flex flex-col bg-[#f8fbff] rounded-xl"
      sx={{
        borderRadius: 2,
        boxShadow: "0 2px 8px 0 rgba(0,0,0,0.07)",
        marginTop: 4,
      }}
    >
      <div className="flex justify-between items-center mb-4">
        <div className="flex flex-col md:flex-row  md:items-center md:justify-between gap-2 bg-blue-50 px-2 py-1 pt-[10px] w-full">
          <Typography variant="h6" className="font-semibold text-gray-900">
            {drawerTitle}
          </Typography>
        </div>
      </div>
      <div className="flex-1 overflow-auto">
        <Box
          className="grid grid-cols-1 md:grid-cols-3 gap-x-6 gap-y-4"
          sx={{ marginTop: 2 }}
        >
          {requiredFields.map(renderField)}
        </Box>
      </div>

      <div className="flex justify-end mt-6">
        {getSubModulePermissionCommon("Masters", "Des Form", "Edit Template")
          ?.isEnabled && (
          <button
            className={`text-white px-6 py-2 rounded transition-colors ${
              isSubmitting ? "bg-gray-400 cursor-not-allowed" : ""
            }`}
            style={!isSubmitting ? { backgroundColor: "#1465ab" } : {}}
            onClick={handleSave}
            disabled={isSubmitting}
          >
            {isSubmitting ? "Saving..." : templateId ? "Update" : "Save"}
          </button>
        )}
      </div>
    </Paper>
  );
}
