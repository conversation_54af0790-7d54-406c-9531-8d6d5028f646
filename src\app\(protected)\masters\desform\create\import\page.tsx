"use client";
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  TextField,
  Box,
  Accordion,
  AccordionSummary,
  Typography,
  AccordionDetails,
  Button,
  Autocomplete,
  CircularProgress
} from "@mui/material";
import * as XLSX from "xlsx";
import { Delete, Edit, Save } from "@mui/icons-material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { getPreSignedUrl } from "@/api/file/file";
import { useSearchParams } from "next/navigation";
import { importData, ImportTableData } from "@/api/import/import";
import { showToast } from "@/components/toaster/ToastProvider";
import { toNormalCase } from "@/utils/generic";
import { toCamelCase } from "@/components/gridTable/tableUtils";
// import { unique } from 'next/dist/build/utils';

interface DocumentRow {
  id: number | "new";
  systemAttribute: string;
  emrMapping: string;
  required: boolean;
  unique: boolean;
}

const initialRows: DocumentRow[] = [
  {
    id: 1,
    systemAttribute: "Provider Name",
    emrMapping: "Provider Name",
    required: true,
    unique: false,
  },
  {
    id: 2,
    systemAttribute: "Speciality",
    emrMapping: "Speciality",
    required: false,
    unique: false,
  },
  {
    id: 3,
    systemAttribute: "Patient First Name",
    emrMapping: "First Name",
    required: false,
    unique: false,
  },
  {
    id: 4,
    systemAttribute: "Middle Name",
    emrMapping: "Middle Name",
    required: true,
    unique: false,
  },
  {
    id: 5,
    systemAttribute: "Last Name",
    emrMapping: "Last Name",
    required: false,
    unique: false,
  },
  {
    id: 6,
    systemAttribute: "Primary Payer",
    emrMapping: "Primary Payer",
    required: false,
    unique: false,
  },
  {
    id: 7,
    systemAttribute: "Primary Member ID",
    emrMapping: "Primary Member ID",
    required: true,
    unique: false,
  },
  {
    id: 8,
    systemAttribute: "Received Date",
    emrMapping: "Received Date",
    required: true,
    unique: false,
  },
];

export default function DocumentsUploadTable() {
  const [rows, setRows] = useState<DocumentRow[]>(initialRows);
  const [editRowId, setEditRowId] = useState<number | "new" | null>(null);
  const [editData, setEditData] = useState<Partial<DocumentRow>>({});
  const [headerList, setHeaderList] = useState<string[] | []>([]);
  const [userOptions, setUserOptions] = useState<any>([]);
  const [isLoad, setIsLoad] = useState(false);
  const [templateName, setTemplateName] = useState("");
  // const [downloadOption, setDownloadOption] = useState("");
  // const id = useSelector((state: RootState) => state.user.id);
  // const selectRef = useRef<HTMLSelectElement>(null);
  const [loader, setLoader] = useState(false);
  const searchParams = useSearchParams();
  const templateId = searchParams.get("id") || "";
  console.log("rows", rows);

  useEffect(() => {
    setUserOptions(
      (headerList ?? []).map((item) => ({
        id: toCamelCase(item),
        name: item,
      }))
    );
  }, [headerList]);

  useEffect(() => {
    importData({ templateId: templateId })
      .then((res) => {
        setTemplateName(res.getImportConfigurationByTemplateId.collectionName);
        const mappingJson = res.getImportConfigurationByTemplateId.mappingJson;

        const requiredFields =
          res.getImportConfigurationByTemplateId.requiredFields;

        const uniqueFields =
          res.getImportConfigurationByTemplateId.uniqueFields;

        // Conversion
        const converted = Object.keys(mappingJson).map((key, index) => ({
          id: index + 1,
          systemAttribute: key,
          emrMapping: mappingJson[key],
          required: requiredFields.includes(key),
          unique: uniqueFields.includes(key),
        }));

        console.log(converted);
        const outputJsonString = JSON.stringify(converted, null, 2);

        console.log("outputJsonString", outputJsonString, converted);
        setRows(converted);
      })
      .catch((err) => {
        console.error(err);
      });
  }, []);

  const handleStartEdit = (row: DocumentRow) => {
    setEditRowId(row.id);
    setEditData({
      systemAttribute: row.systemAttribute,
      emrMapping: row.emrMapping,
      required: row.required,
      unique: row.unique,
    });
  };

  const handleChange = (field: keyof DocumentRow, value: any) => {
    setEditData((prev) => ({ ...prev, [field]: value }));
  };

  const handleImport = () => {
    const mappingJson: Record<string, string> = {};
    const requiredFields: string[] = [];
    const uniqueFields: string[] = [];

    rows.forEach((item) => {
      mappingJson[item.systemAttribute] = item.emrMapping;

      if (item.required) {
        requiredFields.push(item.systemAttribute);
      }

      if (item.unique) {
        uniqueFields.push(item.systemAttribute);
      }
    });
    setLoader(true);
    const payload = {
      input: {
        uniqueFields: uniqueFields,
        collectionName: templateName.includes("Provider Credential")
          ? "tickets"
          : templateName,
        templateId: templateId,
        mappingJson: mappingJson,
        requiredFields: requiredFields,
      },
    };
    console.log("payload", payload);
    ImportTableData(payload)
      .then((res) => {
        console.log("res", res);
        setEditRowId(null);
        showToast.success(res.createImportConfiguration.message);
        setLoader(false);
      })
      .catch((err) => {
        setLoader(false);
        showToast.error(err.message);
      });
  };
  const handleSave = () => {
    if (!editData.systemAttribute || !editData.emrMapping) {
      alert("System Attribute and EMR Mapping are required.");
      return;
    }

    if (editRowId === "new") {
      const newRow: DocumentRow = {
        id: Date.now(),
        systemAttribute: editData.systemAttribute,
        emrMapping: editData.emrMapping,
        required: editData.required ?? false,
        unique: editData.unique ?? false,
      };
      setRows((prev) => [...prev, newRow]);
    } else {
      setRows((prev) =>
        prev.map((r) => (r.id === editRowId ? { ...r, ...editData } : r))
      );
    }
    setEditRowId(null);
    setEditData({});
  };

  const handleCancel = () => {
    
    setEditRowId(null);
    setEditData({});
  };

  const handleDelete = (id: number) => {
    setRows((prev) => prev.filter((r) => r.id !== id));
  };

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const path = `import/${file.name}`; // <-- desired path in storage
      const payload = {
        input: { filename: path as string, contentType: "application/pdf" },
      };
      setLoader(true);
      try {
        const uploadPath = await getPreSignedUrl(payload);

        console.log("uploadPath", uploadPath.generateUploadUrl.data.uploadUrl);
        // const path = `organization/profile/${value}`; // <-- desired path in storage
        const uploadResponse = await fetch(
          uploadPath.generateUploadUrl.data.uploadUrl,
          {
            method: "PUT",
            body: file,
            headers: {
              "Content-Type": "image/png", // fallback
            },
          }
        );
        const fileName = file.name.toLowerCase();

        if (fileName.endsWith(".csv")) {
          const text = await file.text();
          const lines = text.split(/\r?\n/);
          const headerRow = lines[0].split(",");
          console.log("CSV headers:", headerRow);
        } else if (fileName.endsWith(".xls") || fileName.endsWith(".xlsx")) {
          const data = await file.arrayBuffer();
          const workbook = XLSX.read(data, { type: "array" });
          const sheetName = workbook.SheetNames[0];
          const sheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(sheet, { header: 1 });
          const headerRow: any = jsonData[0] || [];
          console.log("Excel headers:", headerRow);
          setHeaderList(headerRow);
          setIsLoad(true);
          setLoader(false);
        } else {
          alert("Please upload a .csv or .xls/.xlsx file.");
          setLoader(false);
        }

        if (!uploadResponse.ok) {
          throw new Error("Something went wrong. Please try again.");
        }

        // Save the file path or accessible URL to formData
        //  setFormData((prevData: any) => ({
        //    ...prevData,
        //    [section]: {
        //      ...(prevData[section] || {}),
        //      [field]: path, // Assuming this is returned by getPreSignedUrl
        //    },
        //  }));
        console.log(uploadPath, "uploadPath");
      } catch (e) {
        setLoader(false);
        console.error("Image upload failed:", e);
      }
      // process your file
    }
  };
  // const handleSelect = async (
  //   e:
  //     | React.ChangeEvent<Omit<HTMLInputElement, "value"> & { value: string }>
  //     | (Event & { target: { value: string; name: string } })
  // ) => {
  //   setDownloadOption(e.target.value);
  //   const array = rows.map((item) => item.emrMapping);
  //   if (e.target.value == "withoutData") {
  //     downloadExcelWithHeaders(array);
  //   } else {
  //     const payload = {
  //       input: {
  //         collection: templateName,
  //         createdBy: id,
  //         fields: array,
  //         fileType: "xlsx",
  //         selectedRow: [],
  //       },
  //     };
  //     await exportTableData(payload)
  //       .then((res) => {
  //         console.log(res);
  //         showToast.success(res.startExport.message);
  //       })
  //       .catch((err) => {
  //         console.error(err);
  //       });
  //   }
  // };
  return (
    <div className="py-4 space-y-8 bg-[white] !rounded-[16px] h-[100%]">
      <Box sx={{ width: "100%" }}>
        <Accordion defaultExpanded className="!mt-3">
          <AccordionSummary
            expandIcon={<ExpandMoreIcon />}
            className="!mb-0 !bg-[#7592F90D] !min-h-[40px]"
            sx={{
              "& .MuiAccordionSummary-content": {
                marginY: "0px !important",
              },
            }}
          >
            <div className="w-full flex justify-between items-center">
              <Typography
                variant="h6"
                sx={{ color: "#2B3674", fontWeight: 600, fontSize: "16px" }}
                className="!my-3"
              >
                {"Import Mapping"}
              </Typography>
              <div className="flex justify-between items-center">
                {/* <FormControl fullWidth>
                  <Select
                    displayEmpty
                    defaultValue=""
                    inputRef={selectRef}
                    labelId="download-option-label"
                    className="w-[250px] !h-[30px]"
                    value={downloadOption}
                    onClick={(e) => e.stopPropagation()}
                    // label="Download Option"
                    onChange={(e) => {
                      e.stopPropagation();
                      handleSelect(e);
                    }}
                    inputProps={{ "aria-label": "Download Option" }}
                  >
                    <MenuItem value="" disabled className="!hidden">
                      Download Sample
                    </MenuItem>
                    <MenuItem value="withData">Download With Data</MenuItem>
                    <MenuItem value="withoutData">
                      Download Without Data
                    </MenuItem>
                  </Select>
                </FormControl> */}
                <Button
                  // disabled={isAdded}
                  variant="outlined"
                  component="label"
                  className="!text-[12px] !h-[30px] text-gray-700 !p-2 !m-0 w-[200px] !shadow-none"
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                  disabled={loader}
                >
                  {loader ? (
                    <CircularProgress sx={{ color: "white" }} size={20} />
                  ) : (
                    "Load Fields"
                  )}
                  <input
                    type="file"
                    hidden
                    accept=".csv, .xls, .xlsx"
                    onChange={(e) => {
                      e.stopPropagation();
                      handleFileUpload(e);
                    }}
                  />
                </Button>
                {/* {editRowId !== 'new' && ( <button
                    // disabled={isAdded}
                    type="button"
                    className="text-sm font-200 text-gray-700 bg-none px-4 py-2 rounded flex"
                    onClick={(e)=> {e.stopPropagation();handleStartAdd()}}
                  >
                    <Icon><Image src={addIcon} alt="add" width={20} height={20} /></Icon>
                    <span>Add</span>
                  </button>)} */}
              </div>
            </div>
          </AccordionSummary>
          <AccordionDetails className="!p-0">
            <div className="pt-3">
              <TableContainer component={Paper} className="shadow rounded-lg">
                <Table size="small">
                  <TableHead sx={{ backgroundColor: "#f0fdff" }}>
                    <TableRow>
                      <TableCell sx={{ color: "#1E40AF", fontWeight: 600 }}>
                        System Attribute
                      </TableCell>
                      <TableCell sx={{ color: "#1E40AF", fontWeight: 600 }}>
                        EMR Mapping
                      </TableCell>
                      <TableCell sx={{ color: "#1E40AF", fontWeight: 600 }}>
                        Required
                      </TableCell>
                      <TableCell sx={{ color: "#1E40AF", fontWeight: 600 }}>
                        Unique
                      </TableCell>
                      <TableCell sx={{ color: "#1E40AF", fontWeight: 600 }}>
                        Action
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {rows.length !== 0 ? (
                      rows.map((row) =>
                        editRowId === row.id ? (
                          <TableRow key={row.id}>
                            <TableCell>
                              {editData.systemAttribute}
                              {/* <TextField
                      size="small"
                      value={editData.systemAttribute || ''}
                      onChange={(e) => handleChange('systemAttribute', e.target.value)}
                      fullWidth
                    />*/}
                            </TableCell>
                            <TableCell>
                              {isLoad ? (
                                <Autocomplete
                                  size="small"
                                  fullWidth
                                  className="h-[40px]"
                                  options={userOptions}
                                  getOptionLabel={(option: { name: string }) =>
                                    `${option.name}`
                                  }
                                  value={
                                    userOptions.find(
                                      (u: { name: string }) =>
                                        u.name === editData.emrMapping
                                    ) || null
                                  }
                                  onChange={(_, newValue: any) => {
                                    // setOprationValue({
                                    //   ...operationValue,
                                    //   [role]: newValue ? newValue.id : null,
                                    // });
                                    handleChange("emrMapping", newValue.name);
                                  }}
                                  renderInput={(params) => (
                                    <TextField
                                      {...params}
                                      variant="outlined"
                                      placeholder="Select User"
                                      InputProps={{
                                        ...params.InputProps,
                                        endAdornment: (
                                          <>{params.InputProps.endAdornment}</>
                                        ),
                                      }}
                                    />
                                  )}
                                  isOptionEqualToValue={(
                                    option: any,
                                    value: any
                                  ) => option?.name === value?.name}
                                />
                              ) : (
                                <TextField
                                  size="small"
                                  value={editData.emrMapping || ""}
                                  onChange={(e) =>
                                    handleChange("emrMapping", e.target.value)
                                  }
                                  fullWidth
                                />
                              )}
                            </TableCell>
                            <TableCell>
                              <input
                                type="checkbox"
                                checked={editData.required}
                                onChange={(e) =>
                                  handleChange("required", e.target.checked)
                                }
                              />
                            </TableCell>
                            <TableCell>
                              <input
                                type="checkbox"
                                checked={editData.unique}
                                onChange={(e) =>
                                  handleChange("unique", e.target.checked)
                                }
                              />
                            </TableCell>
                            <TableCell>
                              <IconButton color="primary" onClick={handleSave}>
                                <Save fontSize="small" />
                              </IconButton>
                              <IconButton color="error" onClick={handleCancel}>
                                <Delete fontSize="small" />
                              </IconButton>
                            </TableCell>
                          </TableRow>
                        ) : (
                          <TableRow key={row.id}>
                            <TableCell>{toNormalCase(row.systemAttribute)}</TableCell>
                            <TableCell>{toNormalCase(row.emrMapping)}</TableCell>
                            <TableCell>{row.required ? "Yes" : "No"}</TableCell>
                            <TableCell>{row.unique ? "Yes" : "No"}</TableCell>
                            <TableCell>
                              <IconButton
                                color="primary"
                                onClick={() => handleStartEdit(row)}
                              >
                                <Edit fontSize="small" />
                              </IconButton>
                              <IconButton
                                color="error"
                                onClick={() => handleDelete(row.id as number)}
                              >
                                <Delete fontSize="small" />
                              </IconButton>
                            </TableCell>
                          </TableRow>
                        )
                      )
                    ) : (
                      <TableRow>
                        <TableCell colSpan={5}>No Records found</TableCell>
                      </TableRow>
                    )}
                    {editRowId === "new" && (
                      <TableRow>
                        <TableCell>
                          <Autocomplete
                            size="small"
                            fullWidth
                            className="h-[40px]"
                            options={userOptions}
                            getOptionLabel={(option: { name: string }) =>
                              `${option.name}`
                            }
                            value={
                              userOptions.find(
                                (u: { name: string }) => u.name === ""
                              ) || null
                            }
                            onChange={(_, newValue: any) => {
                              // setOprationValue({
                              //   ...operationValue,
                              //   [role]: newValue ? newValue.id : null,
                              // });
                              handleChange("emrMapping", newValue);
                            }}
                            renderInput={(params) => (
                              <TextField
                                {...params}
                                variant="outlined"
                                placeholder="Select User"
                                InputProps={{
                                  ...params.InputProps,
                                  endAdornment: (
                                    <>{params.InputProps.endAdornment}</>
                                  ),
                                }}
                              />
                            )}
                            isOptionEqualToValue={(option: any, value: any) =>
                              option?.id === value?.id
                            }
                          />
                          {/* <TextField
                    size="small"
                    value={editData.systemAttribute || ''}
                    onChange={(e) => handleChange('systemAttribute', e.target.value)}
                    fullWidth
                  /> */}
                        </TableCell>
                        <TableCell>
                          <TextField
                            size="small"
                            value={editData.emrMapping || ""}
                            onChange={(e) =>
                              handleChange("emrMapping", e.target.value)
                            }
                            fullWidth
                          />
                        </TableCell>
                        <TableCell>No</TableCell>
                        <TableCell>
                          <IconButton color="primary" onClick={handleSave}>
                            <Save fontSize="small" />
                          </IconButton>
                          <IconButton color="error" onClick={handleCancel}>
                            <Delete fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </div>
          </AccordionDetails>
        </Accordion>
        <Button
          variant="outlined"
          className="!text-[14px] !h-[30px] text-gray-700 !p-4 !shadow-none !my-5"
          onClick={() => handleImport()}
          disabled={loader}
        >
          {loader ? (
            <CircularProgress sx={{ color: "white" }} size={20} />
          ) : (
            "Save"
          )}
        </Button>
      </Box>
    </div>
  );
}
