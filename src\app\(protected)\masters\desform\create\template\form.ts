export const form =  {
  "id": "step-2e00c98e-8682-458f-b8df-60376db78889",
  "name": "Step 3",
  "sections": [
      {
          "id": "d58a5bf2-1b64-4cb1-bfed-fbf4f6d0f6f8",
          "name": "Basic Information",
          "fields": [
              {
                  "id": "field-c73ca9f6-3aa8-4aed-bb35-e00d58ee2e5f",
                  "label": "image field",
                  "field_type": "image",
                  "logic_rules": []
              },
              {
                  "id": "field-28a188ed-928c-4157-a49e-6407200650df",
                  "label": "Legal Buisness Name",
                  "field_type": "text",
                  "logic_rules": []
              },
              {
                  "id": "field-590fc769-d936-4fd8-9a6f-e31c8000deca",
                  "label": "Doing Buisness As (DBA)",
                  "field_type": "text",
                  "logic_rules": []
              },
              {
                  "id": "field-4de42297-4ce8-4597-b8d2-21d404f2e44f",
                  "label": "Group NPI Number",
                  "field_type": "number",
                  "logic_rules": []
              },
              {
                  "id": "field-e22c6dec-5840-4eed-9100-a64f4fc9a6d9",
                  "label": "Tax Identification Number (TIN/EIN)",
                  "field_type": "text",
                  "logic_rules": [],
                  "required": true
              },
              {
                  "id": "field-cf5c740f-1243-47b4-bdf6-e61928ad11ca",
                  "label": "Organization Type",
                  "field_type": "select",
                  "logic_rules": [],
                  "options": [
                      {
                          "id": "option-1748693221888",
                          "value": "Public"
                      },
                      {
                          "id": "option-1748693229680",
                          "value": "Private"
                      }
                  ]
              },
              {
                  "id": "field-0cdce557-8c02-4b19-bff9-8809bce91cc2",
                  "label": "Incorporation Date",
                  "field_type": "date",
                  "logic_rules": []
              },
              {
                  "id": "field-5f6fdc51-42fb-4066-8075-5f97f39e807b",
                  "label": "State of Incorporation",
                  "field_type": "select",
                  "logic_rules": []
              },
              {
                  "id": "field-e95497b4-964f-4652-99a8-3e121e46dbf5",
                  "label": "Medicare Group Id",
                  "field_type": "text",
                  "logic_rules": []
              },
              {
                  "id": "field-1361dd74-badb-4b1c-b0d2-dd0d15e725ee",
                  "label": "Medicaid Group Id",
                  "field_type": "text",
                  "logic_rules": []
              },
              {
                  "id": "field-df82f27a-21dd-43f7-aa66-54400531fd36",
                  "label": "CLIA Number",
                  "field_type": "number",
                  "logic_rules": []
              },
              {
                  "id": "field-7b6dbe72-50fa-436d-9fce-36947867161d",
                  "label": "NAICS Code",
                  "field_type": "text",
                  "logic_rules": []
              },
              {
                  "id": "field-ddf5559e-9f3d-4b8e-9ce8-e3bd8c294f02",
                  "label": "Practise Code",
                  "field_type": "select",
                  "logic_rules": []
              },
              {
                  "id": "field-a1b510d9-82bd-40dd-b67f-703d1ba13225",
                  "label": "Speciality",
                  "field_type": "select",
                  "logic_rules": []
              }
          ]
      },
      {
          "id": "5b0f24f8-af43-4591-b0d2-3bb5a06dd5f8",
          "name": "Contact Information",
          "fields": [
              {
                  "id": "field-960a4db4-e1eb-4431-a4b6-c4c8e93ac3d8",
                  "label": "Legal Business Name",
                  "field_type": "text",
                  "logic_rules": []
              },
              {
                  "id": "field-85b3e381-c3a7-4e08-876a-cb574066e414",
                  "label": "Client Onboard Date",
                  "field_type": "date",
                  "logic_rules": []
              },
              {
                  "id": "field-32e9e92f-8dd5-4a66-a1a3-8ef0915065b8",
                  "label": "Client Id",
                  "field_type": "text",
                  "logic_rules": []
              },
              {
                  "id": "field-3f784840-55b9-4975-b1b9-7b406895594a",
                  "label": "Client Email",
                  "field_type": "email",
                  "logic_rules": []
              },
              {
                  "id": "field-bf8e7a4a-85fa-4fbd-a3e2-fc42b5c531ce",
                  "label": "Telephone",
                  "field_type": "phone",
                  "logic_rules": []
              },
              {
                  "id": "field-7e0c1800-a6ce-4333-9ac1-93356a6e6289",
                  "label": "Fax",
                  "field_type": "text",
                  "logic_rules": []
              },
              {
                  "id": "field-52007b00-2ba3-4521-875f-ad5d6a332f32",
                  "label": "Address",
                  "field_type": "text",
                  "logic_rules": []
              },
              {
                  "id": "field-91afa36f-7a65-4bb1-8a3c-171f222a48ad",
                  "label": "Address 2",
                  "field_type": "text",
                  "logic_rules": []
              },
              {
                  "id": "field-1a84e870-fd63-4d26-8e55-187c96b75695",
                  "label": "City",
                  "field_type": "select",
                  "logic_rules": []
              },
              {
                  "id": "field-a8774293-e87b-4250-9e91-2f883ac4bdad",
                  "label": "State/Province",
                  "field_type": "select",
                  "logic_rules": []
              },
              {
                  "id": "field-8394ad9b-651d-4c09-bfda-b480844e7b4c",
                  "label": "Zip/Postal Code",
                  "field_type": "number",
                  "logic_rules": []
              },
              {
                  "id": "field-c1cdffb7-94b4-48b5-a73d-f5e457e1ac76",
                  "label": "Country",
                  "field_type": "select",
                  "logic_rules": []
              }
          ]
      },
      {
          "id": "point-of-contact-section",
          "name": "Point of Contact",
          "fields": [
              {
                  "id": "field-6b497979-8558-4fd6-acbb-d2102491408d",
                  "label": "Point of Contact",
                  "field_type": "grid",
                  "logic_rules": [],
                  "columns": [
                      {
                          "name": "Name",
                          "fieldType": "text",
                          "id": "col_1748694044712"
                      },
                      {
                          "name": "Email Id",
                          "fieldType": "email",
                          "id": "col_1748694064807"
                      },
                      {
                          "name": "Mobile Phone",
                          "fieldType": "number",
                          "id": "col_1748694118159"
                      },
                      {
                          "name": "isPrimary",
                          "fieldType": "text",
                          "id": "col_1748694154958"
                      }
                  ],
                  "rows": [
                      {
                          "id": "row_1748694023832",
                          "col_1748694044712": "",
                          "col_1748694064807": "",
                          "col_1748694118159": "",
                          "col_1748694154958": ""
                      }
                  ]
              }
          ]
      },
      {
          "id": "automation-mapping-section",
          "name": "Automation Mapping",
          "fields": [
              {
                  "id": "field-5481db01-42f6-4d56-bf48-fdbb4d4378d0",
                  "label": "Automation Mapping",
                  "field_type": "grid",
                  "logic_rules": [],
                  "columns": [
                      {
                          "name": "Name",
                          "fieldType": "text",
                          "id": "col_1748694214561"
                      },
                      {
                          "name": "Email Id",
                          "fieldType": "text",
                          "id": "col_1748694231517"
                      },
                      {
                          "name": "Process",
                          "fieldType": "text",
                          "id": "col_1748694259678"
                      }
                  ],
                  "rows": [
                      {
                          "id": "row_1748694238557",
                          "col_1748694214561": "",
                          "col_1748694231517": "",
                          "col_1748694259678": ""
                      }
                  ]
              }
          ]
      },
      {
          "id": "practice-locations-section",
          "name": "Practice Location(s)",
          "fields": [
              {
                  "id": "field-625a74d3-026a-43f2-a510-7e4fa5944c70",
                  "label": "Practice Location(s)",
                  "field_type": "grid",
                  "logic_rules": [],
                  "columns": [
                      {
                          "name": "Practice Location Name",
                          "fieldType": "text",
                          "id": "col_1748694436019"
                      },
                      {
                          "name": "Location Type",
                          "fieldType": "text",
                          "id": "col_1748694453283"
                      },
                      {
                          "name": "Phone",
                          "fieldType": "text",
                          "id": "col_1748694469218"
                      },
                      {
                          "name": "Fax",
                          "fieldType": "text",
                          "id": "col_1748694477821"
                      },
                      {
                          "name": "Location NPI",
                          "fieldType": "text",
                          "id": "col_1748694491213"
                      },
                      {
                          "name": "Email for Location",
                          "fieldType": "email",
                          "id": "col_1748694513514"
                      },
                      {
                          "name": "isPrimary",
                          "fieldType": "text",
                          "id": "col_1748694530364"
                      },
                      {
                          "name": "City",
                          "fieldType": "text",
                          "id": "col_1748694544198"
                      },
                      {
                          "name": "State",
                          "fieldType": "text",
                          "id": "col_1748694558453"
                      },
                      {
                          "name": "Zip",
                          "fieldType": "text",
                          "id": "col_1748694567434"
                      },
                      {
                          "name": "Servicing Address",
                          "fieldType": "text",
                          "id": "col_1748694582948"
                      },
                      {
                          "name": "Billing Address",
                          "fieldType": "text",
                          "id": "col_1748694596460"
                      },
                      {
                          "name": "Mailing Address",
                          "fieldType": "text",
                          "id": "col_1748694607314"
                      }
                  ],
                  "rows": [
                      {
                          "id": "row_1748694456755",
                          "col_1748694436019": "",
                          "col_1748694453283": "",
                          "col_1748694469218": "",
                          "col_1748694477821": "",
                          "col_1748694491213": "",
                          "col_1748694513514": "",
                          "col_1748694530364": "",
                          "col_1748694544198": "",
                          "col_1748694558453": "",
                          "col_1748694567434": "",
                          "col_1748694582948": "",
                          "col_1748694596460": "",
                          "col_1748694607314": ""
                      }
                  ]
              }
          ]
      },
      {
          "id": "credentialing-contact-section",
          "name": "Credentialing Contact",
          "fields": [
              {
                  "id": "field-45e6b3b1-f452-4849-8e0a-158bf80efeac",
                  "label": "Credentialing Contact",
                  "field_type": "grid",
                  "logic_rules": [],
                  "columns": [
                      {
                          "name": "Contact Name",
                          "fieldType": "text",
                          "id": "col_1748694685347"
                      },
                      {
                          "name": "Title",
                          "fieldType": "text",
                          "id": "col_1748694698477"
                      },
                      {
                          "name": "Phone Number",
                          "fieldType": "number",
                          "id": "col_1748694711916"
                      },
                      {
                          "name": "Email Address",
                          "fieldType": "email",
                          "id": "col_1748694736882"
                      },
                      {
                          "name": "Alternate Contact",
                          "fieldType": "text",
                          "id": "col_1748694747942"
                      }
                  ],
                  "rows": [
                      {
                          "id": "row_1748694688738",
                          "col_1748694685347": "",
                          "col_1748694698477": "",
                          "col_1748694711916": "",
                          "col_1748694736882": "",
                          "col_1748694747942": ""
                      }
                  ]
              }
          ]
      },
      {
          "id": "licensure-accrediation-section",
          "name": "Licensure & Accrediation",
          "fields": [
              {
                  "id": "field-6a8dc1d8-5bfd-4a6b-bd8f-5be534a0238d",
                  "label": "Licensure & Accrediation",
                  "field_type": "grid",
                  "logic_rules": [],
                  "columns": [
                      {
                          "name": "State",
                          "fieldType": "select",
                          "id": "col_1748694824664"
                      },
                      {
                          "name": "State Business License Number",
                          "fieldType": "number",
                          "id": "col_1748694843722"
                      },
                      {
                          "name": "Expiry Date",
                          "fieldType": "date",
                          "id": "col_1748694857033"
                      },
                      {
                          "name": "Accrediation Body",
                          "fieldType": "text",
                          "id": "col_1748694885715"
                      },
                      {
                          "name": "Version",
                          "fieldType": "text",
                          "id": "col_1748694896242"
                      },
                      {
                          "name": "Accrediation Date & Expiry",
                          "fieldType": "date",
                          "id": "col_1748694914265"
                      }
                  ],
                  "rows": [
                      {
                          "id": "row_1748694928713",
                          "col_1748694824664": "",
                          "col_1748694843722": "",
                          "col_1748694857033": "",
                          "col_1748694885715": "",
                          "col_1748694896242": "",
                          "col_1748694914265": ""
                      }
                  ]
              }
          ]
      },
      {
          "id": "documents-section",
          "name": "Documents to Upload",
          "fields": [
              {
                  "id": "field-6fc6bdb5-3a7e-4d13-9988-429ca8a94508",
                  "label": "Documents to Upload",
                  "field_type": "grid",
                  "logic_rules": [],
                  "columns": [
                      {
                          "name": "W-9 Form / PAN",
                          "fieldType": "text",
                          "id": "col_1748695127893"
                      },
                      {
                          "name": "Buisness License",
                          "fieldType": "text",
                          "id": "col_1748695141640"
                      },
                      {
                          "name": "Accrediation Certificate",
                          "fieldType": "text",
                          "id": "col_1748695167095"
                      },
                      {
                          "name": "Void Check / Bank Details",
                          "fieldType": "text",
                          "id": "col_1748695221261"
                      },
                      {
                          "name": "IRS Letter (EIN Proof)",
                          "fieldType": "text",
                          "id": "col_1748695240593"
                      },
                      {
                          "name": "Expiration Date",
                          "fieldType": "date",
                          "id": "col_1748695264858"
                      },
                      {
                          "name": "Reminder Days",
                          "fieldType": "date",
                          "id": "col_1748695285497"
                      },
                      {
                          "name": "Renew By",
                          "fieldType": "date",
                          "id": "col_1748695297105"
                      },
                      {
                          "name": "Issue Date",
                          "fieldType": "date",
                          "id": "col_1748695309243"
                      },
                      {
                          "name": "Last Updated Date",
                          "fieldType": "date",
                          "id": "col_1748695328229"
                      },
                      {
                          "name": "Version",
                          "fieldType": "text",
                          "id": "col_1748695336858"
                      },
                      {
                          "name": "Status",
                          "fieldType": "text",
                          "id": "col_1748695345748"
                      }
                  ],
                  "rows": [
                      {
                          "id": "row_1748695349969",
                          "col_1748695127893": "",
                          "col_1748695141640": "",
                          "col_1748695167095": "",
                          "col_1748695221261": "",
                          "col_1748695240593": "",
                          "col_1748695264858": "",
                          "col_1748695285497": "",
                          "col_1748695297105": "",
                          "col_1748695309243": "",
                          "col_1748695328229": "",
                          "col_1748695336858": "",
                          "col_1748695345748": ""
                      }
                  ]
              }
          ]
      },
      {
          "id": "2e6e7462-e3bf-4386-a785-d081b6a36f5b",
          "name": "Other Login Details",
          "fields": [
              {
                  "id": "field-19f92b5a-d185-401d-8e1c-939774a945f8",
                  "label": "Name",
                  "field_type": "text",
                  "logic_rules": []
              },
              {
                  "id": "field-7244ab16-a443-4fe4-b0ee-19de66d6f4b7",
                  "label": "Link",
                  "field_type": "text",
                  "logic_rules": []
              },
              {
                  "id": "field-185e2ec0-457b-4b05-ba11-d18754debfb5",
                  "label": "Username",
                  "field_type": "text",
                  "logic_rules": []
              },
              {
                  "id": "field-872165d7-2b34-42a0-b55b-a4fef5238a18",
                  "label": "Password",
                  "field_type": "text",
                  "logic_rules": []
              }
          ]
      }
  ]
}