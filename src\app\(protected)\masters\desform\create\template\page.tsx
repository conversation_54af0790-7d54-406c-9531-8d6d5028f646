/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { useState, useEffect } from "react";
import { Card } from "../../../../../[components]/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/app/[components]/dialog";
import { v4 as uuidv4 } from "uuid";
import {
  DragDropContext,
  Droppable,
  Draggable,
  type DropResult,
} from "@hello-pangea/dnd";
import Button from "@/app/[components]/Button1";
import {
  Box,
  CircularProgress,
  Divider,
  FormControl,
  InputLabel,
  Select,
  TextField,
  Typography,
} from "@mui/material";
import AddCircleIcon from "@mui/icons-material/AddCircle";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import "./style.css";
import { FieldRenderer } from "../../../../../../components/questionsbuilder/FieldRenderer";
import { CustomizeFieldMenu } from "@/components/questionsbuilder/filedsList";
import { FieldEditor } from "@/components/questionsbuilder/FieldEditor";
import FieldActionsMenu from "@/components/questionsbuilder/actionbox";
import type {
  FieldType,
  SectionType,
  StepType,
} from "@/components/questionsbuilder/types";
import {
  getByTemplateId,
  getTemplate,
  updateTemplate,
  switchTemplateVersion,
  createTemplateVersion,
  cloneTemplate,
} from "@/api/dynamicTemplate/template";
import { getOrganizationUserPagination } from "@/api/organizations/organizations";
import { showToast } from "@/components/toaster/ToastProvider";
import { useRouter } from "next/navigation";
import { toCamelCase } from "@/components/gridTable/tableUtils";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import IconButton from "@mui/material/IconButton";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import { CustomFieldDrawer } from "@/components/questionsbuilder/customFieldPopup";
import { getSubModulePermissionCommon } from "@/utils/generic";

// GridColumn is not in shared types, so keep it local if needed
// Extend FieldType locally to add grid-specific and htmlContent fields for this file
export interface GridColumn {
  id: string;
  name: string;
  fieldType: string;
}

// Local extension of FieldType for this file
interface LocalFieldType extends FieldType {
  columns?: GridColumn[];
  rows?: Record<string, unknown>[];
  htmlContent?: string;
}
// Extend FieldType for local use
interface LocalFieldType extends FieldType {
  columns?: GridColumn[];
  rows?: Record<string, unknown>[];
  htmlContent?: string;
}

export default function Dynamicform() {
  const [steps, setSteps] = useState<StepType[]>([]);
  const [templateData, setTemplatedata] = useState<any>([]);
  const router = useRouter();
  const [selectedStep, setSelectedStep] = useState<string>("step-1");
  const [editField, setEditField] = useState<LocalFieldType | null>(null);
  const [versionHistory, setVersionHistory] = useState<any>([]);
  const [, setEditLabel] = useState<string>("");
  const [editSectionId, setEditSectionId] = useState<string>("");
  const [editingNameId, setEditingNameId] = useState<string>("");
  const [nameEditValue, setNameEditValue] = useState<string>("");
  const [fieldValues, setFieldValues] = useState<Record<string, unknown>>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedStateId, setSelectedStateId] = useState<string | null>(null);
  // const [drawerOpen, setDrawerOpen] = useState(false);
  const [openCustomFieldSection, setOpenCustomFieldSection] = useState<
    string | null
  >(null);
  const [stepMenuAnchorEl, setStepMenuAnchorEl] = useState<null | HTMLElement>(
    null
  );
  const [showHiddenFields, setShowHiddenFields] = useState<
    Record<string, boolean>
  >({});

  const [stepMenuStepId, setStepMenuStepId] = useState<string | null>(null);
  const [editPosition, setEditPosition] = useState<number>(1);
  const searchParams = new URLSearchParams(window.location.search);
  const id = searchParams.get("id");

  // useEffect(() => {
  //     if (steps && !Array.isArray(steps)) {
  //         console.error("Steps is not an array:", steps);
  //         setSteps(Array.isArray(steps) ? steps : []);
  //     }
  // }, [steps]);
  useEffect(() => {
    const isLoaded =
      Array.isArray(steps) &&
      steps.length > 0 &&
      steps.every((step) => Array.isArray(step.sections));

    if (isLoaded) {
      sessionStorage.removeItem("expectFields");
      sessionStorage.removeItem("reloadedOnce");
    }
  }, [steps]);

  useEffect(() => {
    if (!id) return;
    fetchTemplate();
  }, [id]);

  const fetchTemplate = async () => {
    if (!id) return;
    setLoading(true);
    try {
      const res = await getByTemplateId(id as string);
      const template = res?.template?.data?.template;
      if (!template) {
        console.error("Invalid response format:", res);
        showToast.error("Failed to load template data");
        return;
      }
      setVersionHistory(res?.template?.data);
      setTemplatedata(template);
      const rawFields = template?.fields;
      let parsedFields = [];
      try {
        if (typeof rawFields === "string" && rawFields.trim()) {
          parsedFields = JSON.parse(rawFields);
        } else if (Array.isArray(rawFields)) {
          parsedFields = rawFields;
        } else {
          parsedFields = [];
        }
      } catch (err) {
        console.error("Error parsing fields:", err);
      }

      setSteps(parsedFields);
      if (parsedFields.length > 0) {
        setSelectedStep(parsedFields[0].id);
      }
    } catch (error) {
      console.error("Error loading template:", error);
      showToast.error("Failed to load template");
    } finally {
      setLoading(false);
    }
  };
  const evaluateVisibilityCondition = (
    condition: string | undefined,
    allFields: FieldType[]
  ) => {
    if (!condition) return true;

    try {
      const fieldPattern = /\{([^}]+)\}/g;
      let evaluatedCondition = condition;

      let match = fieldPattern.exec(condition);
      while (match !== null) {
        const fieldId = match[1];
        const fieldValue = fieldValues[fieldId];
        const field = allFields.find((f) => f.id === fieldId);

        if (field) {
          if (field.field_type === "select") {
            evaluatedCondition = evaluatedCondition.replace(
              match[0],
              typeof fieldValue === "string"
                ? `'${fieldValue}'`
                : String(fieldValue)
            );
          } else if (
            field.field_type === "multiselect" ||
            field.field_type === "checkboxes"
          ) {
            const arrayValue = Array.isArray(fieldValue)
              ? fieldValue
              : typeof fieldValue === "string"
                ? fieldValue.split(",").filter(Boolean)
                : [];

            evaluatedCondition = evaluatedCondition.replace(
              match[0],
              JSON.stringify(arrayValue)
            );
          } else {
            evaluatedCondition = evaluatedCondition.replace(
              match[0],
              typeof fieldValue === "string"
                ? `'${fieldValue}'`
                : String(fieldValue)
            );
          }
        }
        match = fieldPattern.exec(condition);
      }

      evaluatedCondition = evaluatedCondition.replace(
        /\bcontains\b/g,
        "includes"
      );
      evaluatedCondition = evaluatedCondition
        .replace(/\band\b/gi, "&&")
        .replace(/\bor\b/gi, "||");

      evaluatedCondition = evaluatedCondition.replace(
        /(\[.*?\]) includes '([^']*)'/g,
        '$1.includes("$2")'
      );

      evaluatedCondition = evaluatedCondition
        .replace(/=/g, "===")
        .replace(/!===/g, "!==");

      return new Function(`return ${evaluatedCondition}`)();
    } catch (error) {
      console.error("Error evaluating visibility condition:", error, condition);
      return true;
    }
  };
  const getFieldVisibilityMap = (
    allFields: FieldType[]
  ): Record<string, boolean> => {
    const visibility: Record<string, boolean> = {};

    if (!Array.isArray(allFields)) {
      console.error("allFields is not an array:", allFields);
      return visibility;
    }

    allFields.forEach((field) => {
      const isVisible = evaluateVisibilityCondition(field.visibleIf, allFields);
      visibility[field.id] = isVisible;

      if (!isVisible && fieldValues[field.id] !== undefined) {
        setFieldValues((prev) => {
          const newValues = { ...prev };
          delete newValues[field.id];
          return newValues;
        });
      }
    });

    return visibility;
  };

  const allFields = Array.isArray(steps)
    ? steps?.flatMap(
        (s) => s.sections?.flatMap((sec) => sec?.fields || []) || []
      )
    : [];

  const fieldVisibilityMap = getFieldVisibilityMap(allFields || []);
  useEffect(() => {
    if (!Array.isArray(steps)) {
      console.error("Steps is not an array in useEffect:", steps);
      return;
    }

    const allFields = steps?.flatMap(
      (step) => step.sections?.flatMap((section) => section.fields || []) || []
    );

    const initialValues: Record<string, unknown> = {};
    allFields?.forEach((field) => {
      if (field.field_type === "select" && field.options?.length) {
        initialValues[field.id] = field.options[0].value;
      } else if (field.field_type === "multiselect") {
        initialValues[field.id] = [];
      } else if (field.field_type === "checkboxes") {
        initialValues[field.id] = [];
      } else if (field.field_type === "toggle") {
        initialValues[field.id] = false;
      } else {
        initialValues[field.id] = "";
      }
    });

    setFieldValues((prev) => ({
      ...initialValues,
      ...prev,
    }));
  }, [steps]);

  console.log("step", steps);

  const canAddStepper =
    templateData?.type !== "Master" && templateData?.type !== "Ticketing";

  const addStep = () => {
    if (canAddStepper || steps.length === 0) {
      const newStepId = `step-${uuidv4()}`;
      const newStep: StepType = {
        id: newStepId,
        name: `Step ${steps?.length + 1}`,
        sections: [],
      };
      setSteps([...steps, newStep]);
      setSelectedStep(newStepId);
    } else {
      showToast.error(
        "Only one stepper can be added for Master/Ticketing templates."
      );
    }
  };

  const addSectionToStep = (stepId: string) => {
    const newSection: SectionType = {
      id: uuidv4(),
      name: "New Section",
      fields: [],
    };

    const updatedSteps = steps?.map((step) => {
      if (step.id !== stepId) return step;
      return {
        ...step,
        sections: [...step.sections, newSection],
      };
    });
    setSteps(updatedSteps);
  };

  const addFieldToSection = (
    stepId: string,
    sectionId: string,
    fieldType: string,
    fieldLabel: string,
    cloneOfId?: string
  ) => {
    const newField: FieldType = {
      id: `field-${uuidv4()}`,
      label: `${fieldLabel} field`,
      placeholder:
        fieldLabel.includes("Image") || fieldLabel.includes("File Upload")
          ? fieldLabel
          : fieldType.includes("global_select") || fieldLabel.includes("Select")
            ? `Select ${fieldLabel}`
            : `Enter ${fieldLabel}`,
      field_type: fieldType as FieldType["field_type"],
      globals_name: fieldLabel,
      options:
        fieldType === "checkboxes" ||
        fieldType === "select" ||
        fieldType === "multiselect"
          ? [
              { id: uuidv4(), value: "option 1" },
              { id: uuidv4(), value: "option 2" },
            ]
          : [],
      ...(fieldType === "grid" ? { columns: [], rows: [] } : {}),
      ...(cloneOfId ? { cloneOf: cloneOfId } : {}),
    };
    // If this is a clone, hide the parent field
    let updatedSteps = steps;
    if (cloneOfId) {
      updatedSteps = steps.map((step) => ({
        ...step,
        sections: step.sections.map((section) => ({
          ...section,
          fields: section.fields.map((field) =>
            field.id === cloneOfId ? { ...field, hidden: true } : field
          ),
        })),
      }));
    }
    updatedSteps = updatedSteps?.map((step) => {
      if (step.id !== stepId) return step;
      return {
        ...step,
        sections: step.sections.map((section) => {
          if (section.id !== sectionId) return section;
          return {
            ...section,
            fields: [...section.fields, newField],
          };
        }),
      };
    });
    setSteps(updatedSteps);
  };
  const deleteField = (stepId: string, sectionId: string, fieldId: string) => {
    const updatedSteps = steps?.map((step) => {
      if (step.id !== stepId) return step;
      return {
        ...step,
        sections: step.sections.map((section) => {
          if (section.id !== sectionId) return section;
          return {
            ...section,
            fields: section.fields.filter((field) => field.id !== fieldId),
          };
        }),
      };
    });
    setSteps(updatedSteps);
  };

  const deleteSection = (stepId: string, sectionId: string) => {
    const updatedSteps = steps?.map((step) => {
      if (step.id !== stepId) return step;
      return {
        ...step,
        sections: step.sections.filter((section) => section.id !== sectionId),
      };
    });
    setSteps(updatedSteps);
  };

  const mapFieldOptions = (field: FieldType, sectionId: string) => {
    const mappedColumns =
      (field as LocalFieldType).columns?.map(
        (col: GridColumn, idx: number) => ({
          ...col,
          id: col.id || `col-${idx}`,
          name: col.name || `Column ${idx + 1}`,
          fieldType: "string",
        })
      ) ?? undefined;

    const baseOptions = {
      id: field?.id,
      label: field?.label,
      field_type: field?.field_type as FieldType["field_type"],
      required: field?.required,
      options: field?.options?.map((opt) => opt.value),
      defaultValue: field?.placeholder,
      htmlContent: (field as LocalFieldType)?.htmlContent,
      placeholder: field?.placeholder,
      globals_name: field?.globals_name,
      columns: mappedColumns,
      rows: (field as LocalFieldType).rows,
      onDelete: () => deleteField(selectedStep, sectionId, field.id),
    };

    if (field.field_type === "grid") {
      return {
        ...baseOptions,
        onChange: (value: string | number | boolean | string[] | null) => {
          if (
            value &&
            typeof value === "object" &&
            "columns" in value &&
            "rows" in value
          ) {
            const { columns, rows } = value as {
              columns: GridColumn[];
              rows: Record<string, unknown>[];
            };
            const updatedSteps = steps.map((step) => {
              if (step.id !== selectedStep) return step;
              return {
                ...step,
                sections: step.sections.map((section) => {
                  if (!section.fields.some((f) => f.id === field.id))
                    return section;
                  return {
                    ...section,
                    fields: section.fields.map((f) =>
                      f.id === field.id ? { ...f, columns, rows } : f
                    ),
                  };
                }),
              };
            });
            setSteps(updatedSteps);
          }
        },
      };
    } else {
      return {
        ...baseOptions,
        onChange: (value: string | number | boolean | string[] | null) => {
          handleFieldValueChange(field.id, value);
        },
      };
    }
  };
  // const handleIdentifierChange = async (identifier: string) => {
  //   setSelectedIdentifier(identifier);
  //   const response = await fetch(`/api/prefile-fields?identifier=${identifier}`);
  //   const data = await response.json();
  //   setPrefileFields(data.fields); // assuming API returns fields
  // };
  const updateStepOrSectionName = () => {
    // Step edit
    const stepIndex = steps.findIndex((step) => step.id === editingNameId);
    if (stepIndex !== -1) {
      // Reorder steps if position changed
      const updatedSteps = [...steps];
      updatedSteps[stepIndex] = {
        ...updatedSteps[stepIndex],
        name: nameEditValue,
      };
      if (editPosition - 1 !== stepIndex) {
        const [moved] = updatedSteps.splice(stepIndex, 1);
        updatedSteps.splice(editPosition - 1, 0, moved);
      }
      setSteps(updatedSteps);
      setEditingNameId("");
      setNameEditValue("");
      return;
    }
    // Section edit
    const stepIdx = steps.findIndex((step) =>
      step.sections.some((sec) => sec.id === editingNameId)
    );
    if (stepIdx !== -1) {
      const updatedSteps = [...steps];
      const sectionIndex = updatedSteps[stepIdx].sections.findIndex(
        (sec) => sec.id === editingNameId
      );
      updatedSteps[stepIdx].sections[sectionIndex] = {
        ...updatedSteps[stepIdx].sections[sectionIndex],
        name: nameEditValue,
      };
      if (editPosition - 1 !== sectionIndex) {
        const [moved] = updatedSteps[stepIdx].sections.splice(sectionIndex, 1);
        updatedSteps[stepIdx].sections.splice(editPosition - 1, 0, moved);
      }
      setSteps(updatedSteps);
      setEditingNameId("");
      setNameEditValue("");
      return;
    }
    setEditingNameId("");
    setNameEditValue("");
  };

  const onDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    // Use | as delimiter to avoid issues with dashes in IDs
    const [sourceStepId, sourceSectionId] =
      result.source.droppableId.split("|");
    const [destStepId, destSectionId] =
      result.destination.droppableId.split("|");

    if (sourceStepId !== destStepId || sourceSectionId !== destSectionId) {
      console.log("Cross-section dragging not implemented");
      return;
    }

    const sourceIndex = result.source.index;
    const destIndex = result.destination.index;

    setSteps((prevSteps) => {
      return prevSteps.map((step) => {
        if (step.id !== sourceStepId) return step;

        return {
          ...step,
          sections: step.sections.map((section) => {
            if (section.id !== sourceSectionId) return section;

            const updatedFields = Array.from(section.fields);
            const [movedField] = updatedFields.splice(sourceIndex, 1);
            updatedFields.splice(destIndex, 0, movedField);

            return {
              ...section,
              fields: updatedFields.map((field, index) => ({
                ...field,
                position: index + 1,
              })),
            };
          }),
        };
      });
    });
  };
  const handleFieldValueChange = (
    fieldId: string,
    value: string | number | boolean | string[] | null
  ) => {
    if (!Array.isArray(steps)) {
      console.error("Steps is not an array in handleFieldValueChange:", steps);
      return;
    }

    const field = steps
      .flatMap(
        (step) =>
          step.sections?.flatMap((section) => section.fields || []) || []
      )
      .find((f) => f?.id === fieldId);

    let normalizedValue = value;

    if (
      field?.field_type === "multiselect" ||
      field?.field_type === "checkboxes"
    ) {
      normalizedValue = Array.isArray(value)
        ? value
        : typeof value === "string" && value
          ? [value]
          : [];
    }

    // If this field is a clone, update the parent as well
    if (field && (field as any).cloneOf) {
      setFieldValues((prev) => ({
        ...prev,
        [fieldId]: normalizedValue,
        [(field as any).cloneOf]: normalizedValue,
      }));
    } else {
      // If this field is a parent and has a clone, update the clone as well
      const clone = steps
        .flatMap(
          (step) =>
            step.sections?.flatMap((section) => section.fields || []) || []
        )
        .find((f) => (f as any).cloneOf === fieldId);
      if (clone) {
        setFieldValues((prev) => ({
          ...prev,
          [fieldId]: normalizedValue,
          [clone.id]: normalizedValue,
        }));
      } else {
        setFieldValues((prev) => ({
          ...prev,
          [fieldId]: normalizedValue,
        }));
      }
    }
  };

  const handleSave = async () => {
    const templateFields = {
      fields: steps,
    };

    const allFields = steps.flatMap((step) =>
      step.sections.flatMap((section) => section.fields)
    );
    const inGrid = allFields
      .filter((field) => field.show_in_grid || field.only_in_grid)
      .map((field) => toCamelCase(field.label));

    const defaultFields = allFields
      .filter((field) => field.is_default)
      .map((field) => toCamelCase(field.label));
    const isImport = allFields
      .filter((field) => field.is_import)
      .map((field) => toCamelCase(field.label));

    const view_summary = {
      inGrid,
      isImport,
      default: defaultFields,
    };
    const searchParams = new URLSearchParams(window.location.search);
    const id = searchParams.get("id");
    if (id) {
      updateTemplate({
        input: {
          id,
          view_summary,
          fields: JSON.stringify(templateFields.fields),
        },
      });
      await getTemplate({
        filters: {},
        sortBy: "",
        sortOrder: "",
        page: 1,
        limit: 10,
        search: "",
      });
      router.push("/masters/desform");
      showToast.success("Template updated successfully");
    }
  };
  const [selectedVersion, setSelectedVersion] = useState<string | null>(null);
  const [showVersionConfirmDialog, setShowVersionConfirmDialog] =
    useState(false);
  const [versionAction, setVersionAction] = useState<{
    type: "upgrade" | "downgrade";
    targetVersion: string;
    message: string;
  } | null>(null);

  // Clone functionality state
  const [showCloneDialog, setShowCloneDialog] = useState(false);
  const [cloneData, setCloneData] = useState({
    templateName: "",
    mainClientId: "",
    subClientId: "",
  });
  const [mainClients, setMainClients] = useState<any[]>([]);
  const [subClients, setSubClients] = useState<any[]>([]);
  const [cloneLoading, setCloneLoading] = useState(false);

  // Get current version as number for comparison
  const getCurrentVersionNumber = () => {
    return parseFloat(templateData?.version || "1.0");
  };

  // Handle version selection
  const handleVersionChange = (selectedVersionValue: string) => {
    const currentVersion = getCurrentVersionNumber();
    const targetVersion = parseFloat(selectedVersionValue);

    console.log("Version change:", {
      currentVersion,
      targetVersion,
      selectedVersionValue,
    });

    if (targetVersion === currentVersion) {
      // Current version - should be disabled, but just in case
      console.log("Selected current version, ignoring");
      return;
    }

    if (targetVersion > currentVersion) {
      // Higher version - show upgrade confirmation
      console.log("Upgrading to higher version");
      setVersionAction({
        type: "upgrade",
        targetVersion: selectedVersionValue,
        message: `Do you want to move to version ${selectedVersionValue}?`,
      });
      setShowVersionConfirmDialog(true);
    } else {
      // Lower version - show downgrade/create new version confirmation
      const nextVersion = (currentVersion + 0.1).toFixed(1);
      console.log("Creating new version from lower version");
      setVersionAction({
        type: "downgrade",
        targetVersion: selectedVersionValue,
        message: `Do you want to use version ${selectedVersionValue} as version ${nextVersion}?`,
      });
      setShowVersionConfirmDialog(true);
    }
  };

  // Handle version confirmation
  const handleVersionConfirm = async () => {
    if (!versionAction) return;

    try {
      setLoading(true);

      if (versionAction.type === "upgrade") {
        // Call API to switch to higher version
        await switchToVersion(versionAction.targetVersion);
        showToast.success(
          `Successfully moved to version ${versionAction.targetVersion}`
        );
      } else {
        // Call API to create new version based on selected version
        const nextVersion = (getCurrentVersionNumber() + 0.1).toFixed(1);
        await createNewVersionFromExisting(
          versionAction.targetVersion,
          nextVersion
        );
        showToast.success(
          `Successfully created version ${nextVersion} based on version ${versionAction.targetVersion}`
        );
      }

      // Refresh template data
      await fetchTemplate();
    } catch (error) {
      console.error("Version operation failed:", error);
      showToast.error("Failed to change version. Please try again.");
    } finally {
      setLoading(false);
      setShowVersionConfirmDialog(false);
      setVersionAction(null);
      setSelectedVersion(null);
    }
  };

  // Handle version confirmation cancel
  const handleVersionCancel = () => {
    setShowVersionConfirmDialog(false);
    setVersionAction(null);
    setSelectedVersion(null);
  };

  // API function to switch to existing version
  const switchToVersion = async (version: string) => {
    if (!id) throw new Error("Template ID is required");

    await switchTemplateVersion({
      input: {
        templateId: id,
        version: version,
      },
    });
  };

  // API function to create new version from existing version
  const createNewVersionFromExisting = async (
    sourceVersion: string,
    newVersion: string
  ) => {
    if (!id) throw new Error("Template ID is required");

    await createTemplateVersion({
      input: {
        templateId: id,
        sourceVersion: sourceVersion,
        newVersion: newVersion,
      },
    });
  };

  // Clone functionality handlers
  const handleCloneClick = async () => {
    setShowCloneDialog(true);
    setCloneData({
      templateName: `${templateData?.name || "Template"} - Copy`,
      mainClientId: "",
      subClientId: "",
    });

    // Load main clients
    try {
      const res = await getOrganizationUserPagination({
        input: { filters: { type: "main_client" }, orgType: "MAIN_CLIENT" },
      });
      setMainClients(res?.getOrganisationsWithPagination?.users || []);
    } catch (error) {
      console.error("Error loading main clients:", error);
      setMainClients([]);
    }
  };

  const handleCloneDataChange = (field: string, value: string) => {
    setCloneData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Load sub-clients when main client changes
    if (field === "mainClientId" && value) {
      loadSubClients(value);
    } else if (field === "mainClientId" && !value) {
      setSubClients([]);
      setCloneData((prev) => ({ ...prev, subClientId: "" }));
    }
  };

  const loadSubClients = async (mainClientId: string) => {
    try {
      const res = await getOrganizationUserPagination({
        input: { filters: { main_client: mainClientId } },
      });
      setSubClients(res?.getOrganisationsWithPagination?.users || []);
    } catch (error) {
      console.error("Error loading sub-clients:", error);
      setSubClients([]);
    }
  };

  const handleCloneSubmit = async () => {
    if (!cloneData.templateName.trim()) {
      showToast.error("Template name is required");
      return;
    }

    if (!cloneData.mainClientId) {
      showToast.error("Main client is required");
      return;
    }

    try {
      setCloneLoading(true);

      const clonePayload = {
        input: {
          name: cloneData.templateName.trim(),
          id: templateData?._id,
          organisationId: cloneData.mainClientId,
          subOrganisationId: cloneData.subClientId || undefined,
        },
      };

      await cloneTemplate(clonePayload);
      showToast.success("Template cloned successfully!");

      // Close dialog and reset state
      setShowCloneDialog(false);
      setCloneData({
        templateName: "",
        mainClientId: "",
        subClientId: "",
      });
      setMainClients([]);
      setSubClients([]);
    } catch (error) {
      console.error("Error cloning template:", error);
      showToast.error("Failed to clone template. Please try again.");
    } finally {
      setCloneLoading(false);
    }
  };

  const handleCloneCancel = () => {
    setShowCloneDialog(false);
    setCloneData({
      templateName: "",
      mainClientId: "",
      subClientId: "",
    });
    setMainClients([]);
    setSubClients([]);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-[60vh]">
        <CircularProgress />
      </div>
    );
  }

  function handleStepMenuOpen(
    event: React.MouseEvent<HTMLElement>,
    stepId: string
  ) {
    setStepMenuAnchorEl(event.currentTarget);
    setStepMenuStepId(stepId);
  }

  function handleStepMenuClose() {
    setStepMenuAnchorEl(null);
    setStepMenuStepId(null);
  }

  function handleEditStep(id: string, name: string) {
    setEditingNameId(id);
    setNameEditValue(name);
    const stepIndex = steps.findIndex((s) => s.id === id);
    setEditPosition(stepIndex + 1);
    handleStepMenuClose();
    handleStepMenuClose();
  }

  function handleDeleteStep(stepId: string) {
    setSteps((prevSteps) => {
      const updatedSteps = prevSteps.filter((step) => step.id !== stepId);
      // If the deleted step was selected, select the first step if available
      if (selectedStep === stepId && updatedSteps.length > 0) {
        setSelectedStep(updatedSteps[0].id);
      } else if (updatedSteps.length === 0) {
        setSelectedStep("");
      }
      return updatedSteps;
    });
    handleStepMenuClose();
  }

  // Format version history for display
  const formatVersionHistory = () => {
    if (
      !versionHistory?.versionHistory ||
      !Array.isArray(versionHistory.versionHistory)
    ) {
      return [];
    }

    return versionHistory.versionHistory.map((version: any) => ({
      id: version.id,
      version: version.version,
      name: version.name,
      createdAt: new Date(version.createdAt).toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      }),
    }));
  };

  // Add this type definition above VersionHistoryList or at the top of the file
  // type VersionHistoryItem = {
  //   id: string;
  //   version: string | number;
  //   name: string;
  //   createdAt: string;
  // };

  // Version History Display Component
  // const VersionHistoryList = () => {
  //   const versions = formatVersionHistory();

  //   if (versions.length === 0) {
  //     return (
  //       <div className="text-gray-500 text-sm p-4">
  //         No version history available
  //       </div>
  //     );
  //   }

  //   return (
  //     <div className="max-h-60 overflow-y-auto">
  //       {versions.map((version: VersionHistoryItem, index: number) => (
  //         <div
  //           key={version.id}
  //           className={`p-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer ${
  //             index === 0 ? "bg-blue-50" : ""
  //           }`}
  //           onClick={() => handleVersionChange(version.version.toString())}
  //         >
  //           <div className="flex justify-between items-start">
  //             <div className="flex-1">
  //               <div className="flex items-center gap-2">
  //                 <span className="font-medium text-sm">
  //                   Version {version.version}
  //                 </span>
  //                 {index === 0 && (
  //                   <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
  //                     Current
  //                   </span>
  //                 )}
  //               </div>
  //               <div className="text-xs text-gray-600 mt-1 truncate">
  //                 {version.name}
  //               </div>
  //             </div>
  //             {/* <div className="text-xs text-gray-500 ml-2">
  //           {version.createdAt}
  //         </div> */}
  //           </div>
  //         </div>
  //       ))}
  //     </div>
  //   );
  // };
  return (
    <>
      <div>
        <div className="flex items-center justify-between gap-4">
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2 bg-blue-100 text-blue-800 rounded-full">
              <span className="text-sm font-medium px-2 py-1">
                Current Version:
              </span>
              <span className="px-2 py-1  text-sm">
                v{templateData?.version || "1.0"}
              </span>
            </div>

            <IconButton size="small" onClick={handleCloneClick}>
              <ContentCopyIcon fontSize="small" />
            </IconButton>
          </div>
          <div className="flex items-center gap-2">
            <FormControl size="small" className="min-w-[90px]">
              <Select
                value={selectedVersion || ""}
                onChange={(e) => handleVersionChange(e.target.value)}
                displayEmpty
                className="text-xs"
                sx={{
                  fontSize: "13px",
                  height: 28,
                  minHeight: 28,
                  padding: 0,
                  ".MuiSelect-select": {
                    padding: "2px 28px 2px 8px",
                    minHeight: "unset",
                    display: "flex",
                    alignItems: "center",
                  },
                  ".MuiOutlinedInput-notchedOutline": {
                    borderColor: "#c4c4c4",
                  },
                  ".MuiSvgIcon-root": {
                    right: "0px !important",
                    top: "50%",
                    transform: "translateY(-50%)",
                    fontSize: 20,
                  },
                  borderRadius: "6px",
                  background: "#fff",
                }}
                MenuProps={{
                  PaperProps: {
                    sx: {
                      fontSize: "13px",
                    },
                  },
                }}
              >
                <MenuItem
                  value=""
                  disabled
                  sx={{ fontSize: "13px", minHeight: 28 }}
                >
                  Version
                </MenuItem>
                {formatVersionHistory().map((versionItem: any) => {
                  const currentVersion = getCurrentVersionNumber();
                  const isCurrentVersion =
                    versionItem.version === currentVersion;

                  return (
                    <MenuItem
                      key={versionItem.id}
                      value={versionItem.version.toString()}
                      disabled={isCurrentVersion}
                      sx={{
                        backgroundColor: isCurrentVersion
                          ? "#f5f5f5"
                          : "inherit",
                        color: isCurrentVersion ? "#999" : "inherit",
                        "&.Mui-disabled": {
                          opacity: 0.6,
                        },
                        fontSize: "13px",
                        minHeight: 28,
                      }}
                    >
                      <div className="flex flex-col">
                        <span>
                          V {versionItem.version}{" "}
                          {isCurrentVersion && "(Current)"}
                        </span>
                        {/* <span className="text-xs text-gray-500">
                          {versionItem.createdAt}
                        </span> */}
                      </div>
                    </MenuItem>
                  );
                })}
              </Select>
            </FormControl>
            {getSubModulePermissionCommon(
              "Masters",
              "Des Form",
              "Edit Template"
            )?.isEnabled && (
              <Button
                className="buttonstyle bg-teal-500 hover:bg-teal-600 float-right"
                onClick={handleSave}
                text="Save Changes"
              >
                Save Template
              </Button>
            )}
          </div>
        </div>
      </div>
      <div className="p-6 space-y-6">
        {/* Stepper UI */}
        <div className="flex space-x-1 mb-6">
          {Array.isArray(steps) &&
            steps.map((step, index) => {
              const isActive = step.id === selectedStep;
              return (
                <Box
                  key={step.id}
                  onClick={() => setSelectedStep(step.id)}
                  className={`flex-1 flex items-center justify-center py-3 px-5 cursor-pointer transition-all duration-200 ${
                    isActive
                      ? "bg-gradient-to-r from-[#A357DF] to-[#0C4DB2] text-white rounded-r-full mx-2 m-0"
                      : "bg-[#EDF4FA] text-[#9DA7C1]"
                  }`}
                >
                  <div className="flex items-center space-x-2">
                    <div
                      className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-semibold ${
                        isActive
                          ? "bg-white text-[#0C4DB2] border-2 border-[#00B8B0]"
                          : "border border-[#C2C4D0] text-[#A3AED0]"
                      }`}
                    >
                      {index + 1}
                    </div>
                    <span
                      className={`text-sm ${
                        isActive ? "text-white font-medium" : "text-[#9DA7C1]"
                      }`}
                    >
                      {step.name}
                    </span>
                    {/* Step actions menu */}
                    {getSubModulePermissionCommon(
                      "Masters",
                      "Des Form",
                      "Edit Template"
                    )?.isEnabled && (
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleStepMenuOpen(e, step.id);
                        }}
                      >
                        <MoreVertIcon
                          sx={{ color: isActive ? "#fff" : undefined }}
                          fontSize="small"
                        />
                      </IconButton>
                    )}
                  </div>
                </Box>
              );
            })}
          {getSubModulePermissionCommon("Masters", "Des Form", "Edit Template")
            ?.isEnabled && (
            <button
              type="button"
              onClick={addStep}
              className="flex items-center space-x-2 p-2 text-gray-700 hover:text-gray-900 text-sm font-medium"
            >
              <AddCircleIcon />
              <span>Add Stepper</span>
            </button>
          )}
          {/* Step actions dropdown menu */}
          <Menu
            anchorEl={stepMenuAnchorEl}
            open={Boolean(stepMenuAnchorEl)}
            onClose={handleStepMenuClose}
          >
            <MenuItem
              onClick={() => {
                const step = steps.find((s) => s.id === stepMenuStepId);
                if (step) handleEditStep(step.id, step.name);
              }}
            >
              Edit
            </MenuItem>
            <MenuItem
              onClick={() => {
                if (stepMenuStepId) handleDeleteStep(stepMenuStepId);
              }}
              disabled={steps.length <= 1}
            >
              Delete
            </MenuItem>
          </Menu>
        </div>

        {/* Sections and Fields */}
        <div className="space-y-6">
          <Card className="border border-gray-300 backdrop-blur-sm bg-white/90 shadow-xl hover:shadow-4xl transition-shadow duration-300 box-size !rounded-[4px]">
            {Array.isArray(steps) &&
              steps
                .find((step) => step.id === selectedStep)
                ?.sections?.map((section) => (
                  <div key={section.id}>
                    <div className="flex justify-between items-center p-3 bg-blue-50">
                      <Typography
                        variant="h6"
                        tabIndex={0}
                        role="button"
                        onDoubleClick={() => {
                          setEditingNameId(section.id);
                          setNameEditValue(section.name);
                        }}
                        onKeyUp={(e) => {
                          if (e.key === "Enter" || e.key === " ") {
                            setEditingNameId(section.id);
                            setNameEditValue(section.name);
                          }
                        }}
                        onKeyDown={(e) => {
                          if (e.key === "Enter" || e.key === " ") {
                            setEditingNameId(section.id);
                            setNameEditValue(section.name);
                          }
                        }}
                        onClick={() => {
                          if (
                            getSubModulePermissionCommon(
                              "Masters",
                              "Des Form",
                              "Edit Template"
                            )?.isEnabled
                          ) {
                            setEditingNameId(section.id);
                            setNameEditValue(section.name);
                          }
                        }}
                      >
                        {section.name}
                      </Typography>
                      <div className="flex items-center gap-2">
                        {/* <label className="flex items-center text-xs ml-2 font-100 text-gray-700">
                          <input
                            type="checkbox"
                            checked={!!showHiddenFields[section.id]}
                            onChange={() =>
                              setShowHiddenFields((prev) => ({
                                ...prev,
                                [section.id]: !prev[section.id],
                              }))
                            }
                            className="mr-1"
                          />
                          Hidden Fields
                        </label> */}
                        <CustomizeFieldMenu
                          sectionId={section.id}
                          onAddField={(sectionId, fieldType, fieldLabel) => {
                            addFieldToSection(
                              selectedStep,
                              sectionId,
                              fieldType,
                              fieldLabel
                            );
                          }}
                        />
                        <CustomFieldDrawer
                          open={openCustomFieldSection === section.id}
                          onClose={() => setOpenCustomFieldSection(null)}
                          customFormFields={
                            section.fields.filter(
                              (f) => f.only_in_custom_form
                            ) as any
                          }
                        />

                        <FieldActionsMenu
                          onEdit={() => {
                            setEditingNameId(section.id);
                            setNameEditValue(section.name);
                            const step = steps.find(
                              (s) => s.id === selectedStep
                            );
                            const sectionIndex =
                              step?.sections.findIndex(
                                (sec) => sec.id === section.id
                              ) ?? 0;
                            setEditPosition(sectionIndex + 1);
                          }}
                          onDelete={() => {
                            deleteSection(selectedStep, section.id);
                          }}
                          onShowCustomFields={() =>
                            setOpenCustomFieldSection(section.id)
                          }
                          hiddenFieldsChecked={!!showHiddenFields[section.id]}
                          onToggleHiddenFields={() =>
                            setShowHiddenFields((prev) => ({
                              ...prev,
                              [section.id]: !prev[section.id],
                            }))
                          }
                        />
                      </div>
                    </div>
                    <Divider className="mb-4" />
                    <DragDropContext onDragEnd={onDragEnd}>
                      <Droppable
                        droppableId={`${selectedStep}|${section.id}`}
                        direction="vertical"
                      >
                        {(provided) => {
                          // --- Begin dynamic ordering logic ---
                          // Get visible fields
                          const visibleFields = section.fields.filter(
                            (field) =>
                              fieldVisibilityMap[field.id] &&
                              (!field.only_in_grid ||
                                !!showHiddenFields[section.id]) &&
                              (!field.only_in_custom_form ||
                                !!showHiddenFields[section.id]) &&
                              !(field as any).hidden
                          );

                          const orderedFields = [...visibleFields];

                          // Generic dynamic ordering: for each field with visibleIf, move it after its controlling field
                          section.fields.forEach((field) => {
                            if (field.visibleIf) {
                              // Try to extract the controlling field id from the visibleIf string (e.g., "{field-xyz} === 'Yes'")
                              const match =
                                field.visibleIf.match(/\{([^}]+)\}/);
                              if (match) {
                                const controllingFieldId = match[1];
                                const controlledFieldIndex =
                                  orderedFields.findIndex(
                                    (f) => f.id === field.id
                                  );
                                const controllerIndex = orderedFields.findIndex(
                                  (f) => f.id === controllingFieldId
                                );
                                if (
                                  controlledFieldIndex !== -1 &&
                                  controllerIndex !== -1 &&
                                  controlledFieldIndex < controllerIndex
                                ) {
                                  // Remove controlled field from its current position
                                  const [controlledField] =
                                    orderedFields.splice(
                                      controlledFieldIndex,
                                      1
                                    );
                                  // Insert after the controlling field
                                  orderedFields.splice(
                                    controllerIndex + 1,
                                    0,
                                    controlledField
                                  );
                                }
                              }
                            }
                          });
                          // --- End dynamic ordering logic ---

                          return (
                            <div
                              {...provided.droppableProps}
                              ref={provided.innerRef}
                              className="grid grid-cols-3 gap-4 p-4"
                            >
                              {orderedFields.map((field, index) => (
                                <Draggable
                                  key={field.id}
                                  draggableId={field.id}
                                  index={index}
                                >
                                  {(provided) => (
                                    <div
                                      ref={provided.innerRef}
                                      {...provided.draggableProps}
                                      {...provided.dragHandleProps}
                                      className={`col-span-1 ${field.field_type === "grid" ? "col-span-3" : ""}`}
                                    >
                                      <div className="mt-1">
                                        <FieldRenderer
                                          field={
                                            mapFieldOptions(
                                              field,
                                              section.id
                                            ) as any
                                          }
                                          setSelectedStateId={
                                            setSelectedStateId
                                          }
                                          selectedStateId={selectedStateId}
                                          value={
                                            fieldValues[field.id] as
                                              | string
                                              | number
                                              | boolean
                                              | string[]
                                              | null
                                              | undefined
                                          }
                                          onChange={(value) =>
                                            handleFieldValueChange(
                                              field.id,
                                              value
                                            )
                                          }
                                          onEdit={() => {
                                            setEditField(
                                              field as LocalFieldType
                                            );
                                            setEditLabel(field.label);
                                            setEditSectionId(section.id);
                                          }}
                                        />
                                      </div>
                                    </div>
                                  )}
                                </Draggable>
                              ))}
                              {provided.placeholder}
                            </div>
                          );
                        }}
                      </Droppable>
                    </DragDropContext>
                  </div>
                ))}
          </Card>
          {getSubModulePermissionCommon("Masters", "Des Form", "Edit Template")
            ?.isEnabled && (
            <div className="mb-6 ">
              <button
                type="button"
                onClick={() => addSectionToStep(selectedStep)}
                className="flex items-center space-x-2 p-2 text-gray-700 hover:text-gray-900 text-sm font-medium"
              >
                <AddCircleIcon />
                <span>Add Section</span>
              </button>
            </div>
          )}
        </div>

        {/* Only render FieldEditor if both editField and editSectionId are set */}
        {editField && editSectionId && (
          <FieldEditor
            open={!!editField}
            field={editField}
            onClose={() => {
              setEditField(null);
              setEditSectionId("");
            }}
            onSave={(updatedField) => {
              const safeField: LocalFieldType = {
                ...updatedField,
                field_type: updatedField.field_type,
                columns: updatedField.columns?.map((col) => ({
                  ...col,
                  fieldType:
                    typeof col.fieldType === "string"
                      ? col.fieldType
                      : "string",
                })),
              };
              // If this is a clone, update parent as well
              let updatedSteps = steps.map((step) => {
                if (step.id !== selectedStep) return step;
                return {
                  ...step,
                  sections: step.sections.map((section) => {
                    if (section.id !== editSectionId) return section;
                    return {
                      ...section,
                      fields: section.fields.map((field) =>
                        field.id === safeField.id ? safeField : field
                      ),
                    };
                  }),
                };
              });
              // Find and update the parent/clone if needed
              const allFields = steps.flatMap(
                (step) =>
                  step.sections?.flatMap((section) => section.fields || []) ||
                  []
              );
              if ((safeField as any).cloneOf) {
                // Update parent field in steps
                updatedSteps = updatedSteps.map((step) => ({
                  ...step,
                  sections: step.sections.map((section) => ({
                    ...section,
                    fields: section.fields.map((field) =>
                      field.id === (safeField as any).cloneOf
                        ? {
                            ...field,
                            ...safeField,
                            id: field.id,
                            cloneOf: undefined,
                            hidden: false,
                          }
                        : field
                    ),
                  })),
                }));
              } else {
                // If this is a parent, update its clone
                const clone = allFields.find(
                  (f) => (f as any).cloneOf === safeField.id
                );
                if (clone) {
                  updatedSteps = updatedSteps.map((step) => ({
                    ...step,
                    sections: step.sections.map((section) => ({
                      ...section,
                      fields: section.fields.map((field) =>
                        field.id === clone.id
                          ? {
                              ...field,
                              ...safeField,
                              id: clone.id,
                              cloneOf: safeField.id,
                            }
                          : field
                      ),
                    })),
                  }));
                }
              }
              setSteps(updatedSteps);
              setEditField(null);
              setEditSectionId("");
            }}
            allFields={steps?.flatMap((step) =>
              step?.sections?.flatMap((section) => section?.fields)
            )}
            steps={steps}
            setSteps={setSteps}
          />
        )}

        <Dialog
          open={!!editingNameId}
          onOpenChange={() => {
            setEditingNameId("");
          }}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Name & Position</DialogTitle>
            </DialogHeader>
            <InputLabel className="lable-color">Name</InputLabel>
            <TextField
              name="editName"
              type="text"
              fullWidth
              placeholder="Enter new name"
              value={nameEditValue}
              onChange={(e) => setNameEditValue(e.target.value)}
            />
            <InputLabel className="lable-color mt-2">Position</InputLabel>
            <select
              value={editPosition}
              onChange={(e) => setEditPosition(Number(e.target.value))}
              style={{
                width: "100%",
                padding: "8px 12px",
                marginTop: "8px",
                borderRadius: "4px",
                border: "1px solid #c4c4c4",
                backgroundColor: "#fff",
                fontSize: "16px",
                fontFamily: "Roboto, sans-serif",
                boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.05)",
                appearance: "none",
                WebkitAppearance: "none",
                MozAppearance: "none",
                backgroundImage:
                  "url(\"data:image/svg+xml;utf8,<svg fill='gray' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/></svg>\")",
                backgroundRepeat: "no-repeat",
                backgroundPosition: "right 10px center",
                backgroundSize: "16px 16px",
              }}
            >
              {editingNameId && steps.some((s) => s.id === editingNameId)
                ? steps.map((_, idx) => (
                    <option key={idx + 1} value={idx + 1}>
                      {idx + 1}
                    </option>
                  ))
                : (() => {
                    const step = steps.find((s) => s.id === selectedStep);
                    return step?.sections.map((_, idx) => (
                      <option key={idx + 1} value={idx + 1}>
                        {idx + 1}
                      </option>
                    ));
                  })()}
            </select>
            <DialogFooter>
              <Button
                text="Save"
                className="buttonstyle bg-teal-500 hover:bg-teal-600"
                onClick={updateStepOrSectionName}
              >
                Save
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Clone Template Dialog */}
        <Dialog open={showCloneDialog} onOpenChange={setShowCloneDialog}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Clone Template</DialogTitle>
            </DialogHeader>
            <div className="py-4 space-y-4">
              {/* Template Name */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  Template Name *
                </label>
                <TextField
                  fullWidth
                  size="small"
                  value={cloneData.templateName}
                  onChange={(e) =>
                    handleCloneDataChange("templateName", e.target.value)
                  }
                  placeholder="Enter template name"
                  variant="outlined"
                />
              </div>

              {/* Main Client */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  Main Client *
                </label>
                <select
                  value={cloneData.mainClientId}
                  onChange={(e) =>
                    handleCloneDataChange("mainClientId", e.target.value)
                  }
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 hover:border-gray-400 transition-colors"
                  style={{ minHeight: "40px" }}
                >
                  <option value="" disabled>
                    Select Main Client
                  </option>
                  {mainClients.map((client: any) => (
                    <option key={client._id} value={client._id}>
                      {client.name ||
                        client.organizationName ||
                        client.fullName}
                    </option>
                  ))}
                </select>
              </div>

              {/* Sub Client */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  Sub Client
                </label>
                <select
                  value={cloneData.subClientId}
                  onChange={(e) =>
                    handleCloneDataChange("subClientId", e.target.value)
                  }
                  disabled={!cloneData.mainClientId}
                  className={`w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 hover:border-gray-400 transition-colors ${
                    !cloneData.mainClientId
                      ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                      : ""
                  }`}
                  style={{ minHeight: "40px" }}
                >
                  <option value="">Select Sub Client (Optional)</option>
                  {subClients.map((client: any) => (
                    <option key={client._id} value={client._id}>
                      {client.name ||
                        client.organizationName ||
                        client.fullName}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <DialogFooter>
              <Button
                text="Cancel"
                variant="outline"
                onClick={handleCloneCancel}
                className="mr-2"
                disabled={cloneLoading}
              >
                Cancel
              </Button>
              <Button
                text={cloneLoading ? "Cloning..." : "Clone Template"}
                onClick={handleCloneSubmit}
                className="buttonstyle bg-teal-500 hover:bg-teal-600"
                disabled={cloneLoading}
              >
                {cloneLoading ? "Cloning..." : "Clone Template"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Version Confirmation Dialog */}
        <Dialog
          open={showVersionConfirmDialog}
          onOpenChange={setShowVersionConfirmDialog}
        >
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Version Change Confirmation</DialogTitle>
            </DialogHeader>
            <div className="py-4">
              <p className="text-sm text-gray-600">{versionAction?.message}</p>
            </div>
            <DialogFooter>
              <Button
                text="Cancel"
                variant="outline"
                onClick={handleVersionCancel}
                className="mr-2"
              >
                Cancel
              </Button>
              <Button
                text={
                  versionAction?.type === "upgrade"
                    ? "Move to Version"
                    : "Create New Version"
                }
                onClick={handleVersionConfirm}
                className="buttonstyle bg-teal-500 hover:bg-teal-600"
              >
                {versionAction?.type === "upgrade"
                  ? "Move to Version"
                  : "Create New Version"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </>
  );
}
// function setSelectedIdentifier(identifier: string) {
//     throw new Error("Function not implemented.");
// }

// function setPrefileFields(fields: any) {
//     throw new Error("Function not implemented.");
// }
