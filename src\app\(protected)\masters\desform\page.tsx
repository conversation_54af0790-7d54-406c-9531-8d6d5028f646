"use client";

import * as React from "react";
import { useDispatch } from "react-redux";
import { setClientTable } from "@/features/client/clientSlice";
import { TableData, Column } from "@/types/user";
import { CommonDataTable } from "@/components/gridTable/CommonDataTable";
import { getTemplate, deleteTemplate } from "@/api/dynamicTemplate/template";
import { formatDate } from "@/components/gridTable/tableUtils";
import { useRouter } from "next/navigation";
type QueryState = {
  filters: Record<string, unknown>;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  page: number;
  limit: number;
  search: string;
};
export default function Page() {
  const dispatch = useDispatch();
  const router = useRouter();
  const [tableData, setTableData] = React.useState<TableData | null>(null);
  const [pagination, setPagination] = React.useState({
    page: 1,
    total: 0,
    totalPages: 1,
    limit: 10,
  });
  const [query, setQuery] = React.useState<QueryState>({
    filters: {},
    sortBy: "",
    sortOrder: "asc",
    page: 1,
    limit: 10,
    search: "",
  });
  const handleGetApi = () => {
    const payload = {
      filters: query.filters,
      sortBy: String(query.sortBy),
      sortOrder: String(query.sortOrder ?? "asc"),
      page: Number(query.page),
      limit: Number(query.limit),
      search: String(query.search),
    };
    getTemplate(payload)
      .then((res) => {
        const templates = res.templates.data.templates;
        const staticColumns: Column[] = [
          {
            id: "name",
            title: "Form Name",
            width: "w-[180px]",
            hasFilter: true,
            type: "text",
            visible: true,
            sortable: true,
            filterType: "text",
            placeholder: "Search Form Name...",
          },
          {
            id: "type",
            title: "Form Type",
            width: "w-[140px]",
            hasFilter: true,
            type: "text",
            visible: true,
            sortable: true,
            filterType: "text",
            placeholder: "Search Form Type...",
          },
          {
            id: "isActive",
            title: "Status",
            width: "w-[120px]",
            hasFilter: true,
            type: "boolean",
            visible: true,
            sortable: true,
            filterType: "text",
            placeholder: "Search Status...",
          },
          {
            id: "createdAt",
            title: "Created At",
            width: "w-[160px]",
            hasFilter: true,
            type: "text",
            visible: true,
            sortable: true,
            filterType: "text",
            placeholder: "Search Created At...",
          },
          {
            id: "version",
            title: "Version",
            width: "w-[100px]",
            hasFilter: true,
            type: "number",
            visible: true,
            sortable: true,
            filterType: "text",
            placeholder: "Search Version...",
          },
        ];

        const dataRows: Record<string, string>[] = [];
        templates.forEach((template: Record<string, unknown>) => {
          const row: Record<string, string> = { id: String(template._id) };
          staticColumns.forEach((col) => {
            let fieldValue = template[col.id];
            if (
              col.id === "createdAt" &&
              fieldValue &&
              (typeof fieldValue === "string" ||
                typeof fieldValue === "number" ||
                fieldValue instanceof Date)
            ) {
              fieldValue = formatDate(fieldValue);
            }
            if (col.id === "isActive") {
              row[col.id] = fieldValue ? "Active" : "In-Active";
            } else if (fieldValue !== undefined && fieldValue !== null) {
              row[col.id] =
                typeof fieldValue === "boolean"
                  ? fieldValue
                    ? "true"
                    : "false"
                  : String(fieldValue);
            } else {
              row[col.id] = "";
            }
          });
          dataRows.push(row);
        });

        const columns = staticColumns;

        const tableData: TableData = {
          tableConfig: {
            columns: columns,
            settings: {
              defaultSortColumn: columns.length > 0 ? columns[0].id : "",
              defaultSortDirection: "asc",
              rowsPerPage: 10,
              selectable: true,
              filterable: true,
              responsive: true,
            },
          },
          data: dataRows,
        };

        setPagination({
          page: res.templates.data.pagination.page,
          total: res.templates.data.pagination.total,
          totalPages: res.templates.data.pagination.totalPages,
          limit: res.templates.data.pagination.limit,
        });

        dispatch(setClientTable(tableData));
        setTableData(tableData);
      })
      .catch((err) => {
        console.error(err);
      });
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteTemplate(id);
      return true;
    } catch (error) {
      console.error("Error deleting template:", error);
      throw error;
    }
  };

  const handleEdit = async (id: string) => {
    router.push(`/masters/desform/create/basicinfo?id=${id}`);
  };

  const setPage = (page: number) => {
    setQuery((prev) => ({ ...prev, page }));
  };
  const setPageSize = (limit: number) => {
    setQuery((prev) => ({ ...prev, limit, page: 1 }));
  };

  React.useEffect(() => {
    handleGetApi();
  }, [query]);

  const handleSetQuery = (
    update:
      | Record<string, unknown>
      | ((prev: Record<string, unknown>) => Record<string, unknown>)
  ) => {
    setQuery((prev) => {
      const next = typeof update === "function" ? update(prev) : update;

      let filters = prev.filters;
      let search: string = prev.search;
      if (
        next.filters &&
        typeof next.filters === "object" &&
        !Array.isArray(next.filters)
      ) {
        const cleaned = Object.fromEntries(
          Object.entries(next.filters).filter(([, v]) =>
            Array.isArray(v)
              ? v.length > 0 && v[0] !== ""
              : v !== "" && v !== null && v !== undefined
          )
        );
        const mapped = Object.fromEntries(
          Object.entries(cleaned).map(([k, v]) => {
            const value = Array.isArray(v) && v.length === 1 ? v[0] : v;
            return [k, value];
          })
        );
        if (mapped.name && typeof mapped.name === "string") {
          search = mapped.name;
        }
        filters = Object.keys(mapped).length > 0 ? mapped : {};
      } else if (typeof next.filters === "object") {
        filters = (next.filters ?? {}) as Record<string, unknown>;
      }

      if (typeof next.search === "string") {
        search = next.search;
      }

      return {
        ...prev,
        ...next,
        filters,
        search,
      };
    });
  };

  return tableData ? (
    <>
      <CommonDataTable
        title="Dynamic Template List"
        tableData={tableData}
        handleGetApi={handleGetApi}
        pagination={pagination}
        setQuery={handleSetQuery}
        addButtonText="Add New Template"
        addButtonPath="/masters/desform/create/basicinfo"
        onAddClick={() => {
          router.push("/masters/desform/create/basicinfo");
        }}
        onDelete={handleDelete}
        onEdit={handleEdit}
        setPage={setPage}
        setPageSize={setPageSize}
      />
    </>
  ) : (
    <div className="flex justify-center items-center h-64">
      <p>Loading data...</p>
    </div>
  );
}
