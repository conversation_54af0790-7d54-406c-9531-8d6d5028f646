"use client";
import { useEffect, useMemo, useState } from "react";
import Link from "next/link";
import { usePathname, useSearchParams } from "next/navigation";
import { ChevronsLeft, ChevronsRight } from "lucide-react";
import { setSideNav } from "@/features/client/clientSlice";
import { useDispatch, useSelector } from "react-redux";
import payer from "../../../assests/payerIcon.svg";
import cpt from "../../../assests/cptIcon.svg";
import exception from "../../../assests/exceptionIcon.svg";
import status from "../../../assests/statusIcon.svg";
import action from "../../../assests/actionIcon.svg";
import scenario from "../../../assests/scenarioIcon.svg";
import speciality from "../../../assests/specialityIcon.svg";
import ansi from "../../../assests/ansiIcon.svg";
import desform from "../../../assests/desform.svg.svg";

import Image from "next/image";
import { RootState } from "@/store";
import { getByTemplateId } from "@/api/Globals/globals";
export default function ClientsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [collapsed, setCollapsed] = useState(false);
  const pathname = usePathname();
  const dispatch = useDispatch();
  const searchParams = useSearchParams();
  const templateId = searchParams.get("id");
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const headerProcess: any = useSelector((state: RootState) => state.headerProcess);
  console.log('headerProcess', headerProcess);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [roles, setRoles] = useState<any>([])
  const [templateName, setTemplateName] = useState('')
  console.log('roles', roles);
  useEffect(() => {
    setRoles(
      headerProcess?.role?.permissions?.filter((item: { moduleName: string }) =>
        item.moduleName.includes('Masters')
      )
    );
  }, [headerProcess]);
  useEffect(() => {
    dispatch(setSideNav(collapsed));
  }, [collapsed]);

  const isDesformScenario =
    pathname.includes("/masters/desform/create") ||
    pathname.includes("/masters/desform/edit");


  useEffect(() => {
    const getName = async () => {
      console.log('templateId',templateId);
      
      const res = await getByTemplateId(templateId||'');
      const templateName = res?.template?.data?.template?.key;
      console.log('template', templateName);
      setTemplateName(templateName)
    }
    if(templateId!==null)
    {getName();}
  }, [templateId])

  const links = useMemo(() => {
      const idParam = templateId ? `?id=${templateId}` : "";
    if (isDesformScenario) {
    
    const desformLinks = [
      {
        href: `/masters/desform/create/basicinfo${idParam}`,
        text: "Basic Info",
        icon: <Image src={desform} alt="desform" width={20} height={20} />,
      },
      {
        href: `/masters/desform/create/template${idParam}`,
        text: "DES",
        icon: <Image src={desform} alt="desform" width={20} height={20} />,
      },
      {
        href: `/masters/desform/create/rule-filter${idParam}`,
        text: "Rule Filter",
        icon: <Image src={desform} alt="desform" width={20} height={20} />,
      },
    ];
console.log('templateName',templateName);

    // Conditionally add Import
    if (
      templateName !== '' &&
      templateName !== 'payer' &&
      templateName !== 'organization' &&
      templateName !== 'sub-organization' &&
      templateName !== 'provider'
    ) {
      desformLinks.push({
        href: `/masters/desform/create/import${idParam}`,
        text: "Import",
        icon: <Image src={desform} alt="desform" width={20} height={20} />,
      });
    }

    return desformLinks;
  }

    const allLinks = [
      {
        href: `/masters/payer`,
        text: "Payer",
        icon: <Image src={payer} alt="provider" width={20} height={20} />,
      },
      {
        href: `/masters/cpt-code`,
        text: "CPT Dictionary",
        icon: <Image src={cpt} alt="provider" width={20} height={20} />,
      },
      {
        href: `/masters/action-status`,
        text: "Action and Status",
        icon: <Image src={action} alt="provider" width={20} height={20} />,
      },
      {
        href: `/masters/exception`,
        text: "Exception",
        icon: <Image src={exception} alt="provider" width={20} height={20} />,
      },
      {
        href: `/masters/scenario`,
        text: "Scenario",
        icon: <Image src={scenario} alt="provider" width={20} height={20} />,
      },
      {
        href: `/masters/specialty`,
        text: "Specialty",
        icon: <Image src={speciality} alt="provider" width={20} height={20} />,
      },
      {
        href: `/masters/bin-status`,
        text: "Bin Status",
        icon: <Image src={status} alt="provider" width={20} height={20} />,
      },
      {
        href: `/masters/ansi-code`,
        text: "ANSI Code",
        icon: <Image src={ansi} alt="provider" width={20} height={20} />,
      },
      {
        href: `/masters/desform`,
        text: "Des Form",
        icon: <Image src={desform} alt="desform" width={20} height={20} />,
      },
      {
        href: `/masters/expiration`,
        text: "Expiration",
        icon: <Image src={payer} alt="provider" width={20} height={20} />,
      },
    ];

    if (!isDesformScenario) {
      return allLinks.filter(link => {
        if(link.text=='Expiration')
        {
          return true
        }const submodule = roles[0]?.subModules?.find(
          (item: { moduleName: string }) => item.moduleName === link.text
        );

        return submodule?.isEnabled;
      });

    }
  }, [isDesformScenario, templateId, roles, templateName]);

  return (
    <div className="flex" style={{ height: "calc(100vh - 191px)" }}>

      <aside
        className={`bg-[#F8F8F9] transition-all duration-300 ${collapsed ? "w-20" : "w-64"
          }`}
      >
        {/* Toggle Collapse Button */}
        <button
          onClick={() => setCollapsed(!collapsed)}
          className="flex items-center justify-end w-full py-4 pr-3"
        >
          {collapsed ? <ChevronsRight size={20} /> : <ChevronsLeft size={20} />}
        </button>

        {/* Navigation Links */}
        <nav className="flex flex-col">
          {links?.map(({ href, text, icon }) => {
            const isActive = pathname.startsWith(href);
            return (
              <Link
                key={href}
                href={href}
                className={`${collapsed && "justify-center"} flex items-center  px-4 py-3 text-sm font-medium ${isActive
                    ? "text-gray-900 bg-blue-50 border-l-4 active:border-[#1465ab] bg-white"
                    : "text-gray-700 hover:bg-white hover:border-l-4 hover:border-[#1465ab] active:bg-white active:border-l-4 active:border-[#1465ab]"
                  }`}
              >
                <span className={`${collapsed ? "mr-0" : "mr-3"}`}>{icon}</span>
                {!collapsed && <span>{text}</span>}
              </Link>
            );
          })}
        </nav>
      </aside>

      {/* Main Content */}
      <main className="flex-1 overflow-auto p-4">{children}</main>
    </div>
  );
}
