'use client';
import React, { useState } from 'react'
import CreateClientPage from "../../Form";
import { getTemplates } from '@/api/templates/templates';
import Loader from '@/components/loader/loader';
import { getSubModulePermissionCommon } from '@/utils/generic';
// import { form } from "../../../organizations/create/form"
// import { Form as ClientForm } from "@/types/cl
const Page = () => {
   const [form, setForm] = useState({});
   const [templateId, setTemplateId] = useState('')
     const [flattedValues, setFlattedValues] = useState([]);
  const [loader, setLoader] = React.useState(false)

      React.useEffect(() => {
 getTemplates({ search: "", filters:{key:"payer", type:'Master',isActive:true}})
    .then((res) => {
      const template = res.templates.data.templates[0];
      console.log('template',template);
       setFlattedValues(res.templates.data.templates[0]?.view_summary?.inGrid);
      if (template && template.fields) {
        const fieldsData = template.fields;
        if (typeof fieldsData === 'string') {
          try {
            const unescaped = fieldsData.replace(/^"|"$/g, '').replace(/\\"/g, '"');
            console.log('unescaped', unescaped);

            // Step 2: Parse it
            const parsedJSON = JSON.parse(unescaped)[0];

            console.log('parsedJSON', parsedJSON);
            // console.log('fieldsData',JSON.parse(JSON.parse(fieldsData)));

            // const parsedFields = JSON.parse(fieldsData);
            setTemplateId(template._id);
            setForm(parsedJSON);
          } catch (error) {
            console.error("Error parsing JSON:", error);
          }
        } else {
          setTemplateId(template._id);
          setForm(fieldsData);
        }
      } else {
        console.warn("Template or fields property is missing.");
      }
      setLoader(false)
    }).catch((err) => {
      console.error(err);
    })
      }, [])
   const getPermission=()=>{
            const permission =getSubModulePermissionCommon('Masters','Payer', 'Edit')?.isEnabled??false
            return permission
          }
  return (
     loader ? <Loader /> : <>
     <div>
        <CreateClientPage formTemplate={form} type="edit"  clientTyoe={""} templateId={templateId} flattedValues={flattedValues} access={getPermission()}/>
      </div>
      </>
  )
}

export default Page
