/* eslint-disable @typescript-eslint/no-explicit-any */
// import { useRouter } from 'next/navigation';
"use client";
import { setClientTable } from "@/features/client/clientSlice";
import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
// import ClientDataTable from "../../ClientDataTable";
import { TableData } from "@/types/user";
import {
  convertFormJsonFromClients,
  transformedClients,
} from "@/utils/generic";
import ClientDataTable from "@/app/[components]/tableGrid/ClientDataTable";
import { getAllPayers } from "@/api/masters/payer/payer";
import Loader from "@/components/loader/loader";
import { getTemplates } from "@/api/templates/templates";
import { setHeaders, setHeadersDefault } from "@/features/headers/headersSlice";
import { RootState } from "@/store";

export default function Page() {
  const dispatch = useDispatch();
  const headerProcess: any = useSelector(
    (state: RootState) => state.headerProcess
  );

  const [query, setQuery] = React.useState({
    page: 1,
    limit: 10,
    search: "",
    sortBy: "",
    sortOrder: "asc",
    // filters:{},
  });
  const [pagination, setPagination] = React.useState(1);
  const [loader, setLoader] = React.useState(false);
  const [roles, setRoles] = React.useState<any>([]);
  const [error, setError]=useState(false)

  React.useEffect(() => {
    console.log("headerProcess", headerProcess);

    const role = headerProcess?.role?.permissions
      ?.filter((item: { moduleName: string }) =>
        item.moduleName.includes("Masters")
      )[0]
      .subModules?.filter(
        (data: { moduleName: string }) => data.moduleName == "Payer"
      )[0].permissions;
    setRoles(role);
  }, [headerProcess]);

  React.useEffect(() => {
    handleGetApi();
    console.log("each");
  }, [query]);

  const handleGetApi = (view?: any) => {
     dispatch(setClientTable({} as TableData));
    setLoader(true);
    setLoader(true);
    getTemplates({
      search: "",
      filters: { key: "payer", type:'Master', isActive: true },
    }).then((res) => {
      const template = res.templates.data.templates[0];
      console.log("template", template);
      if (view == undefined) {
        dispatch(
          setHeaders(res?.templates?.data?.templates[0]?.view_summary?.inGrid)
        );
        dispatch(
          setHeadersDefault(
            res?.templates?.data?.templates[0]?.view_summary?.default
          )
        );
      }
      const result = Object?.fromEntries(
        res?.templates?.data?.templates[0]?.view_summary?.inGrid.map(
          (key: any) => [key, 1]
        )
      );
      const payload = {
        ...query,
        ["selectedFields"]: result,
      };
        dispatch(setClientTable({} as TableData));
      getAllPayers(view == undefined ? payload : view)
        .then((res) => {
          setPagination(res?.payers?.pagination);
          const data = transformedClients(res?.payers?.payers);
          const tableData = convertFormJsonFromClients(data);
          dispatch(setClientTable(tableData as TableData));
          setLoader(false);
        })
        .catch((err) => {
          console.error(err);
          setLoader(false);
          setError(true)
        })
        .catch((err) => {
          setLoader(false);
          setError(true)
          console.error(err);
        });
    });
  };

  return (
    <>
      {loader && <Loader />}
      {!error &&<ClientDataTable
        title={"Payer"}
        handleGetApi={handleGetApi}
        pagination={pagination}
        query={query}
        setQuery={setQuery}
        role={roles}
      />}
    </>
  );
}
