/* eslint-disable prefer-const */
/* eslint-disable @typescript-eslint/no-explicit-any */
'use client'

import React from 'react'
import { getGlobalOptions } from "@/api/Globals/globals";
import { toCamelCase } from "@/components/gridTable/tableUtils";
import {  Button, Checkbox, CircularProgress, FormControlLabel, FormGroup,  InputLabel,  MenuItem, Table, TableBody, TableCell, TableHead, TableRow, TextField, Tooltip } from "@mui/material";
import { useEffect, useState } from "react";
import { Add } from '@mui/icons-material';
import AttachFileIcon from "@mui/icons-material/AttachFile";
import { getImagedUrl } from '@/api/file/file';
import "./style.css";

  interface Field {
  label: string;
  logicJoinType:any;logicConditions:any;
  field_type: string;
  placeholder: any;
  visibleIf:any;
  name: string;
  globals_name: string;
  required?: boolean;
  options: any[];
  id: React.Key | null | undefined;
  columns: any[];
  rows: any[];
}

interface SelectOption {
  id?: string;
  code?: string;
  name?: string;
  value?: string;
  [key: string]: any; // For additional properties
}

interface RenderFieldProps {
  field: Field;
  sectionName: string;
  formData: any;
  handleFieldChange: any;
  type: string;
  handleGridEdit: any;
  handleGridDelete: any;
  editIcon: any;
  deleteIcon: any;
    dynamicSelectOptions:any;
  evaluateLogicConditions:any;
  selectRef:any;
  openSelects:any,
  setOpenSelects:any;
  handleAddICD:any;
  pathname:any;
  access:boolean;
  loader?:boolean;
}
 const RenderForms: React.FC<RenderFieldProps> = ({
  field,
  sectionName,
  formData,
  handleFieldChange,
  type,
  handleGridEdit,
  handleGridDelete,
  editIcon,
  deleteIcon,
  dynamicSelectOptions,
  evaluateLogicConditions,
  selectRef,
  openSelects,
  setOpenSelects,
  handleAddICD,
  pathname,
  access,
  loader

}) => {
    console.log('renderField')
    const [globalOptions, setGlobalOptions] = useState<
            { id: string; value: string }[]
          >([]);
            const [isGlobalLoading, setIsGlobalLoading] = useState(false);
    const section = toCamelCase(sectionName);
    const fieldName = toCamelCase(field.label);
    // const fieldValue = field.label;
    const rawValue = formData?.[section]?.[fieldName] ?? "";
    const options = (field.options?.length > 0 
      ? field.options : dynamicSelectOptions[field.label] ) || [];

    const valueObj = typeof rawValue === 'object'
      ? rawValue
      : options.find((opt:any) => opt.id === rawValue || opt.code === rawValue || opt.value === rawValue);

    const value: any = valueObj || rawValue;
    const tableValue = formData?.[section];

    console.log('valueeeeeee', value)

   useEffect(() => {
       console.log('field.field_type === "global_select" && field.label', field.field_type, field.label);
   
       if (field.field_type === "global_select" && field.label) {
         const fetchGlobalOptions = async () => {
           if (!field.label) return;
           setIsGlobalLoading(true);
           // console.log(' Object.entries(formData?.[section as keyof typeof formData])', Object.entries(formData!==null&&formData?.[section as keyof typeof formData]));
   
           try {
   
             let payload: any = {
               name: field.globals_name ?? "",
   
             }
             if (field.globals_name !== 'City') {
               
             
             // const cleanedLabel = field.globals_name;
             const res = await getGlobalOptions(payload);
             console.log("Fetched global options:", res);
   
             // const data = await res.json();
             const data = res?.getGlobalByName;
             if (Array.isArray(data)) {
               setGlobalOptions(data);
             } else {
               console.error("Unexpected global field data", data);
               setGlobalOptions([]);
   
             }
           }
           } catch (err) {
             console.error("Error fetching global options:", err);
             setGlobalOptions([]);
           } finally {
             setIsGlobalLoading(false);
           }
         };
         if (field.globals_name !== undefined) { fetchGlobalOptions(); }
       }
     }, [field.label, field.field_type,]);
     const fetchImageUrl = async (filename: string) => {
         try {
           const res = await getImagedUrl({ filename });
           console.log(res.generateViewUrl.data.viewUrl);
     
           // Optional fetch check if you want:
           const response = await fetch(res.generateViewUrl.data.viewUrl, {
             method: "GET",
             headers: {
               "Content-Type": "*",
             },
           });
           console.log(response);
           window.open(res.generateViewUrl.data.viewUrl, "_blank");
           // setViewUrl(res.generateViewUrl.data.viewUrl);
         } catch (err) {
           console.error(err);
           // setError('Failed to load image');
         }
       };
     useEffect(()=>{
       const fetchGlobalOptions = async () =>{
       let payload: any = {
               name: field.globals_name ?? "",
   
             }
             if (field.globals_name == 'City') {
               const targetData = formData?.[section as keyof typeof formData];
               if (targetData !== undefined && targetData && typeof targetData === 'object') {
                 const result = Object.entries(targetData)
                   .find(([key]) => key.includes('state') || key.includes('State'));
                 const stateId = result ? result[1] : undefined;
                 payload['stateId'] = stateId
               }
             }
             // const cleanedLabel = field.globals_name;
             const res = await getGlobalOptions(payload);
             console.log("Fetched global options:", res);
   
             // const data = await res.json();
             const data = res?.getGlobalByName;
             if (Array.isArray(data)) {
               setGlobalOptions(data);
             } else {
               console.error("Unexpected global field data", data);
               setGlobalOptions([]);
   
             }
       }
   fetchGlobalOptions()
     },[formData])

    const getDisplayValue = (val: string | SelectOption): string => {
      console.log('valueeeeeee', val)
      if (typeof val === 'object') {
        return val?.code || val?.name || val?.value || '';
      }
      return val;
    };



    if (field?.logicConditions && field?.logicConditions?.length > 0) {
      const isVisible = evaluateLogicConditions(
        field?.logicConditions,
        formData,
        field?.logicJoinType === 'AND' ? field?.logicJoinType : 'OR'
      );
      if (!isVisible) return null;
    }
    console.log('field.field_type', field.field_type);

    switch (field.field_type) {
      case "text":
      case "email":
      case "phone":
      case "number":
      case "date":
      case "password":
        return (
          <>
            <Tooltip title={field.label}  placement="bottom-start">
                                       <InputLabel
                                        className="!text-[22px] font-medium text-gray-800"
                                        shrink
                                      >
                                        {field.label}
                                        {field?.required && (
                                          <span style={{ color: "red" }}> *</span>
                                        )}
                                      </InputLabel>
                                      </Tooltip>
            <TextField
              size="small"
              fullWidth
              type={
                field.field_type === "phone"
                  ? "tel"
                  : field.field_type === "number"
                    ? "number"
                    : field.field_type === "date"
                      ? "date" :  field.field_type === "password" ? 'password' : "text"
              }
              inputProps={
                field.field_type === "number"
                  ? { inputMode: "numeric", pattern: "[0-9]*" }
                  : {}
              }
              InputProps={{
                sx: {
                  "& input.Mui-disabled": {
                    color: "#000000",
                    WebkitTextFillColor: "#000000", //for Safari/Chrome
                  },
                  color: value === '' ? '#aca1a49e' : 'black'
                },
              }}
              placeholder={field.placeholder || field.label}
              value={getDisplayValue(value)}
              className={`${(type === 'edit' && field.label.includes('Email')) ? 'bg-gray-100' : 'bg-transparent'}`}
              disabled={!access && type === 'edit' && field.label.includes('Email')}
              onChange={(e) => handleFieldChange(sectionName, field.label, e.target.value)}
              required={field.required}
            />
          </>

        );
         case "checkbox":
                return (
                  <>
                  <Tooltip title={field.label}  placement="bottom-start">
                                             <InputLabel
                                              className="!text-[22px] font-medium text-gray-800"
                                              shrink
                                            >
                                              {field.label}
                                              {field?.required && (
                                                <span style={{ color: "red" }}> *</span>
                                              )}
                                            </InputLabel>
                                            </Tooltip>
                 <FormGroup>
                                   {field.options?.map((option) => (
                                     <FormControlLabel
                                       key={option.id}
                                       disabled={!access}
                                       control={
                                         <Checkbox
                                           checked={value?.includes(option.id)}
                                           onChange={(e) => {
                                             const newValue = e.target.checked
                                               ? [...(value || []), option.id]
                                               : (value || []).filter((v: string) => v !== option.id);
                                             handleFieldChange(sectionName, field.label, newValue);
                                           }}
                                         />
                                       }
                                       label={option.value}
                                     />
                                   ))}
                                 </FormGroup>
                    </>
                )
        case "image":
              case "file_upload":
                return (
                  <>
                  <Tooltip title={field.label}  placement="bottom-start">
                                             <InputLabel
                                              className="!text-[22px] font-medium text-gray-800"
                                              shrink
                                            >
                                              {field.label}
                                              {field?.required && (
                                                <span style={{ color: "red" }}> *</span>
                                              )}
                                            </InputLabel>
                                            </Tooltip>
                   <div className="flex w-full flex-col sm:flex-row">
          {/* File upload button with icon */}
          <label
            htmlFor={`file-upload-${field.label}`}
            className="flex items-center justify-center border border-gray-300 rounded-l px-3 bg-white cursor-pointer min-w-[44px] h-[40px]"
            style={{ borderRight: "none" }}
          >
            <AttachFileIcon className="text-gray-500" />
            <input
              id={`file-upload-${field.label}`}
              type="file"
              disabled={!access}
              style={{ display: "none" }}
              aria-label="upload file"
              onChange={(e) => {
                const target = e.target as HTMLInputElement;
                handleFieldChange(sectionName, field.label, target.files?.[0]);
              }}
              required={field.required}
            />
          </label>
          {/* File name display */}
          <div className="flex items-center border border-gray-300 rounded-r px-3 bg-white w-full min-h-[40px] overflow-x-auto">
            {value ? (

              <a
                className="truncate text-blue-500 underline cursor-pointer w-full"
                onClick={() => fetchImageUrl(value)}
                title={typeof value === "string" ? value : value.name}
              >
                {typeof value === "string" ? value : value.name}
              </a>
            ) : (
              <span className="text-gray-400 text-sm">No file selected</span>
            )}
          </div>
        </div>
         </>
                );
      case 'multiselect':
        return (
          <>
  <Tooltip title={field.label} placement="bottom-start">
    <InputLabel className="!text-[22px] font-medium text-gray-800" shrink>
      {field.label}
      {field?.required && <span style={{ color: "red" }}> *</span>}
    </InputLabel>
  </Tooltip>

  <TextField
    select
    fullWidth
    size="small"
    disabled={!access}
    value={Array.isArray(value) ? value : []} // Ensures value is always an array
    onChange={(e) => {
      const selected = typeof e.target.value === "string"
        ? e.target.value.split(",") // fallback if value becomes string
        : e.target.value;
      handleFieldChange(sectionName, field.label, selected);
    }}
    required={field.required}
    SelectProps={{
      multiple: true,
      renderValue: (selected) =>
        (selected as string[]).join(", ") || field.placeholder || "Select...",
    }}
    slotProps={{
      select: {
        displayEmpty: true,
        multiple: true,
        renderValue: (selected: any) => {
          if (!selected || selected === "") {
            return <em className="text-gray-400">{field.placeholder || `Select ${field.label}`}</em>;
          }
  
          const options =
            (field.options?.length > 0
              ? field.options
              : dynamicSelectOptions[field.label]) || [];
  
          const found = options.find(
            (opt: any) =>
              opt.code === selected ||
              opt.name === selected ||
              opt.value === selected
          );
  
          return found?.code || found?.name || found?.value || selected;
        }
        
      },
    }}
    InputProps={{
      sx: {
        "& .MuiSelect-select": {
          color: "#000000",
          WebkitTextFillColor: "#000000", // Safari/Chrome
        },
      },
    }}
  >
    {loader ? (
  <MenuItem disabled>
    <CircularProgress size={20} />
    <span className="ml-2">Loading options...</span>
  </MenuItem>
) : field.options?.length > 0 ? (
  field.options.map((opt) => (
    <MenuItem key={opt.id || opt.value} value={opt.value}>
      {opt.value}
    </MenuItem>
  ))
) : (
  <MenuItem disabled>No records found</MenuItem>
)}

    
  </TextField>
</>

        )
        case "select":
          return (
            <>
              
                <InputLabel className="!text-[22px] font-medium text-gray-800" shrink>
                  {field.label}
                  {field?.required && <span style={{ color: "red" }}> *</span>}
                </InputLabel>
              
              <TextField
                size="small"
                select
                fullWidth
                disabled={!access}
                value={
                  (field.label === "Status" || field.label === "Active") && (!value || value === "")
                    ? "Active"
                    : getDisplayValue(value)
                }
                
                SelectProps={{
                  displayEmpty: true,
                  multiple: false,
                  MenuProps: {
                    PaperProps: {
                      sx: {
                        maxHeight: "12rem",
                        overflowY: "auto",
                      },
                    },
                  },
                }}
                InputProps={{
                  sx: {
                                       "& .MuiSelect-select":{
                      color: "#000000",
                      WebkitTextFillColor: "#000000", // Safari/Chrome
                    },
                  },
                }}
                inputRef={selectRef}
                onChange={(e) => {
                  const selectedValue = e.target.value;
                  const options = (field.options?.length > 0 
                    ? field.options : dynamicSelectOptions[field.label] ) || [];
                  const fullOption = options.find((opt: any) =>
                    opt.code === selectedValue ||
                    opt.name === selectedValue ||
                    opt.value === selectedValue
                  );
                  handleFieldChange(sectionName, field.label, fullOption || selectedValue);
                }}
                required={field.required}
                slotProps={{
                  select: {
                    displayEmpty: true,
                    multiple: false,
                    renderValue: (selected: any) => {
                      if (!selected || selected === "") {
                        return <em className="text-gray-400">{field.placeholder || `Select ${field.label}`}</em>;
                      }
              
                      const options =
                        (field.options?.length > 0
                          ? field.options
                          : dynamicSelectOptions[field.label]) || [];
              
                      const found = options.find(
                        (opt: any) =>
                          opt.code === selected ||
                          opt.name === selected ||
                          opt.value === selected
                      );
              
                      return found?.code || found?.name || found?.value || selected;
                    },
                    open: openSelects[field.label] || false,
                    onOpen: () =>
                      setOpenSelects((prev: any) => ({ ...prev, [field.label]: true })),
                    onClose: () =>
                      setOpenSelects((prev: any) => ({ ...prev, [field.label]: false })),
                    
                  },
                }}
              >
               {loader ? (
  <MenuItem disabled>
    <CircularProgress size={20} />
    <span className="ml-2">Loading options...</span>
  </MenuItem>
) : (
  (field.options?.length > 0
    ? field.options
    : dynamicSelectOptions[field.label] || []
  ).length > 0 ? (
    (field.options?.length > 0
      ? field.options
      : dynamicSelectOptions[field.label] || []
    ).map((opt: any) => (
      <MenuItem
        key={opt.id || opt.code || opt.name || opt.value}
        value={opt.code || opt.name || opt.value}
      >
        {opt.code || opt.name || opt.value}
      </MenuItem>
    ))
  ) : (
    <MenuItem disabled>No records found</MenuItem>
  )
)}

          

        
                {/* CPT Code logic */}
                {pathname.includes("/cpt-code/create/") &&
                  ["ICD", "Diagnosis Code", "CPT Code", "Speciality"].includes(field.label) && (
                    <MenuItem disableRipple>
                      <div className="flex justify-end w-full">
                        <Button
                          size="small"
                          className="!bg-[#1969AE] !hover:bg-[#155b96] !text-white !text-[12px] !px-4 !rounded !m-0"
                          startIcon={<Add />}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAddICD(field.label);
                            setOpenSelects((prev: any) => ({ ...prev, [field.label]: false }));
                            if (selectRef.current) {
                              selectRef.current.blur();
                            }
                          }}
                        >
                          Add {field.label}
                        </Button>
                      </div>
                    </MenuItem>
                  )}
        
                {/* Action/Status Code logic */}
                {pathname.includes("/action-status/create/") &&
                  ["Status Code", "Action Code"].includes(field.label) && (
                    <MenuItem disableRipple>
                      <div className="flex justify-end w-full">
                        <Button
                          size="small"
                          className="!bg-[#1969AE] !hover:bg-[#155b96] !text-white !text-[12px] !px-4 !rounded !m-0"
                          startIcon={<Add />}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAddICD(field.label);
                            setOpenSelects((prev: any) => ({ ...prev, [field.label]: false }));
                            if (selectRef.current) {
                              selectRef.current.blur();
                            }
                          }}
                        >
                          Add {field.label}
                        </Button>
                      </div>
                    </MenuItem>
                  )}
              </TextField>
            </>
          );
        
      case "global_select":
        console.log("global:", field);

        return (
          <>
          <InputLabel className="!text-[22px] font-medium text-gray-800" shrink>
                  {field.label}
                  {field?.required && <span style={{ color: "red" }}> *</span>}
                </InputLabel>
            <TextField
              select
              disabled={!access}
              fullWidth
              size="small"
 InputProps={{
                  sx: {
                       "& .MuiSelect-select":{
                      color: "#000000",
                      WebkitTextFillColor: "#000000", // Safari/Chrome
                    },
                  },
                }}
              required={field.required}
              value={getDisplayValue(value)}
              inputRef={selectRef}
              onChange={(e) => {
                const selectedValue = e.target.value;
                console.log('selectedValue', selectedValue)
                const options =
  field.options?.length > 0
    ? field.options
    : dynamicSelectOptions[field.label] || [];

const fullOption = options.find((opt: any) =>
  opt.code === selectedValue ||
  opt.name === selectedValue ||
  opt.value === selectedValue
);
                handleFieldChange(sectionName, field.label, fullOption || selectedValue);
              }}
              slotProps={{
                select: {
                  displayEmpty: true,
                    multiple: false,
                    renderValue: (selected: any) => {
                      if (!selected || selected === "") {
                        return <em className="text-gray-400">{field.placeholder || `Select ${field.label}`}</em>;
                      }
              
                      const options =
                        (field.options?.length > 0
                          ? field.options
                          : dynamicSelectOptions[field.label]) || [];
              
                      const found = options.find(
                        (opt: any) =>
                          opt.code === selected ||
                          opt.name === selected ||
                          opt.value === selected
                      );
              
                      return found?.code || found?.name || found?.value || selected;
                    },
                  open: openSelects[field.label] || false,
                  onOpen: () => setOpenSelects((prev:any) => ({ ...prev, [field.label]: true })),
                  onClose: () => setOpenSelects((prev:any) => ({ ...prev, [field.label]: false })),
                },
              }}
               SelectProps={{
                 displayEmpty: true,    
              multiple: false,
                MenuProps: {
                  PaperProps: {
                    sx: {
                      maxHeight: "12rem",
                      overflowY: "auto",
                    },
                  },
                },
              }}
            >
              { loader ? (
                 <MenuItem disabled>
                 <CircularProgress size={20} />
                 <span className="ml-2">Loading options...</span>
               </MenuItem>
              ):(
                isGlobalLoading ? (
                  <MenuItem disabled value="">
                    Loading options...
                  </MenuItem>
                ) : globalOptions.length === 0 ? (
                  <MenuItem disabled value="">
                    No options found
                  </MenuItem>
                ) : (
                  globalOptions.map((opt) => (
                    <MenuItem key={opt.id} value={opt.value}>
                      {opt.value}
                    </MenuItem>
                  ))
                )
              )}
            </TextField>
          </>


        );
      case "textarea":
        return (
          <>
          <Tooltip title={field.label}  placement="bottom-start">
                                     <InputLabel
                                      className="!text-[22px] font-medium text-gray-800"
                                      shrink
                                      disabled={!access}
                                    >
                                      {field.label}
                                      {field?.required && (
                                        <span style={{ color: "red" }}> *</span>
                                      )}
                                    </InputLabel>
                                    </Tooltip>
            <TextField
              fullWidth
              multiline
              minRows={3}
              placeholder={field.placeholder || field.label}
              value={getDisplayValue(value)}
              onChange={(e) => handleFieldChange(sectionName, field.label, e.target.value)}
              required={field.required}
            />
          </>
        );
      case "checkboxes":
        return (
          <>
          <FormGroup className="px-4">
            {field.options.map((option) => (
              <FormControlLabel
                key={option.id}
                control={
                  <Checkbox
                  disabled={!access}
                    checked={value?.includes(option.id)}
                    onChange={(e) => {
                      const newValue = e.target.checked
                        ? [...(value || []), option.id]
                        : (value || []).filter((v: string) => v !== option.id);
                      handleFieldChange(sectionName, field.label, newValue);
                    }}
                  />
                }
                label={option.value}
              />
            ))}
          </FormGroup>
          </>
        )
      case "grid":
        const gridRows = Array.isArray(tableValue) ? tableValue : []; // value from formData
        console.log('gridRows', gridRows);
        return (
          <div key={field.id} className="px-4 pb-4  overflow-x-scroll w-full">
            <div style={{ overflowX: "auto" }}>
              <Table size='small'
                
              >
                <TableHead sx={{ backgroundColor: "#f0fdff" }}>
                  <TableRow>
                    {field.columns.map((col) => {
                      return (
                        <TableCell
                          key={col.id}
                          sx={{
                            color: "#1E40AF",
                            fontWeight: 600,
                            width: "200px",
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                          }}
                        >
                          {col.name}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {gridRows.length > 0 ? (
                    gridRows.map((row, rowIndex) => (
                      <TableRow key={rowIndex}>
                        {field.columns.map((col) => {
                          const isAction = ( col.name === "Actions" || col.name === "Action");
                          return (
                            <TableCell
                              key={col.id}
                              className={isAction ? " right-0 z-10" : ""}
                              sx={{
                                width: isAction ? "200px" : "auto",
                                maxWidth: isAction ? "200px" : "auto",
                                whiteSpace: "nowrap",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                              }}
                            >
                              {isAction ? (
                                <div className="flex gap-2.5 items-center">
                                  <button
                                    type="button"
                                    onClick={() =>
                                      handleGridEdit(
                                        sectionName,
                                        field.label,
                                        rowIndex,
                                        field.columns,
                                        row
                                      )
                                    }
                                    className="p-2 rounded hover:bg-blue-50 transition-colors"
                                  >
                                    {editIcon}
                                  </button>
                                  <button
                                    type="button"
                                    onClick={() => handleGridDelete(sectionName, rowIndex)}
                                    className="p-2 rounded hover:bg-red-50 transition-colors"
                                  >
                                    {deleteIcon}
                                  </button>
                                </div>
                              ) : (
                               
                                 (row?.[toCamelCase(col.name)] == "true")
                                  ? "Yes"
                                  : (col.fieldType=='toggle')
                                    ? "No"
                                    : (row?.[toCamelCase(col.name)] ?? row?.[col.id] ?? "")
                              )}
                            </TableCell>
                          );
                        })}
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell
                        colSpan={field.columns.length}
                        align="center"
                        sx={{ color: "#64748B", fontSize: "14px" }}
                      >
                        No Data Found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>


        );

      default:
        return <div>Unsupported field type</div>;
    }
}
export const RenderForm = React.memo(RenderForms);