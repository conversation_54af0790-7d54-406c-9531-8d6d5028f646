.box-size{
    padding: 0px !important;
}
.css-1dgb21z-MuiPickersSectionList-root-MuiPickersInputBase-sectionsContainer-MuiPickersOutlinedInput-sectionsContainer {
    padding: 8.5px 0 !important
}
.css-16wblaj-MuiInputBase-input-MuiOutlinedInput-input{
    padding: 8.5px 14px !important
}
.css-kyaajy-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input {
    padding: 8.5px 14px !important;
}
.css-1dune0f-MuiInputBase-input-MuiOutlinedInput-input {
    padding: 8.5px 14px !important;
}
.css-119m9bn-MuiInputBase-root-MuiOutlinedInput-root {
    padding: 0px !important;
}
.css-lohd6h-MuiSvgIcon-root-MuiSelect-icon {
    right: 29px !important;
}
.css-d551zc-MuiSvgIcon-root-MuiSelect-icon {
    right: 29px !important;
}
.css-b7ucgq-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input {
    padding: 8.5px 14px !important;
}
.css-1ysp02-MuiButtonBase-root-MuiIconButton-root {
    margin-right: 10px !important;
}
.css-126do0e-MuiNativeSelect-select-MuiInputBase-input-MuiOutlinedInput-input {
    padding: 8.5px 14px !important;
}
/* .css-17jyosd-MuiSwitch-thumb {
    background-color: #27B8AF!important;
} */
/* .css-161ms7l-MuiButtonBase-root-MuiSwitch-switchBase.Mui-checked+.MuiSwitch-track {
    background-color: #27B8AF;
} */
 .css-161ms7l-MuiButtonBase-root-MuiSwitch-switchBase.Mui-checked+.MuiSwitch-track{
    background-color: #27B8AF !important;
}

.css-161ms7l-MuiButtonBase-root-MuiSwitch-switchBase.Mui-checked {
    color: #27B8AF !important;
}

.css-wll2hb-MuiButtonBase-root-MuiCheckbox-root.Mui-checked, .css-wll2hb-MuiButtonBase-root-MuiCheckbox-root.MuiCheckbox-indeterminate {
    color: #27B8AF !important;
}
[data-rbd-drag-handle-context-id] {
  cursor: move;
}

[data-rbd-draggable-context-id] {
  transition: all 0.2s ease;
}
[data-rbd-drag-placeholder-context-id] {
  background-color: #f7fafc;
  border: 1px dashed #cbd5e0;
  border-radius: 0.375rem;
}
table {
	width: 100%;
	border-collapse: collapse;
}
th, td {
	border: 1px solid #ccc;
	padding: 8px;
}
.buttonstyle {
    padding: 4px 10px !important;
    color: white;
    border-radius: 5px;
}
.lable-color {
    color: #A3AED0 !important;
    padding-bottom: 10px !important;
}
.customdropdown{
    color: #A3AED0 !important;
    margin-bottom:-7px !important;
}
.multiselect-Style{
    height: 40px !important;
    padding: 0px !important;
}
