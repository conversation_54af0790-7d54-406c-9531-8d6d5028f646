'use client'

import Image from 'next/image'
import React from 'react'
import tickIcon from '../../../assests/tick.jpg'

const SuccessPage = () => {
  return (
    <div>
      <div className="bg-white flex flex-col items-center justify-center px-4 text-center absolute top-[40%] left-[40%]">
           <div className="mb-6">
             <Image
               src={tickIcon}
               alt="Lock Icon"
               width={60}
               height={60}
             />
           </div>
           <h1 className="text-2xl font-semibold text-gray-800 mb-2">
             Success
           </h1>
           <p className="text-gray-600 max-w-md">
             Thanks for submitting your details successfully.
           </p>
         </div>
    </div>
  )
}

export default SuccessPage
