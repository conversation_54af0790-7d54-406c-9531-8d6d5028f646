'use client';
import { useEffect, useRef, useState } from 'react';
import { RootState } from '@/store';
import { useSelector } from 'react-redux';
// import { useRouter } from 'next/router';
import { getNotificationList } from '@/api/header/header';
import { formatDistanceToNow } from 'date-fns';
import { Avatar, Button} from '@mui/material';
import Loader from '@/components/loader/loader';

type PriorityLevel = 'HIGH' | 'MEDIUM' | 'LOW';

const priorityColors: Record<PriorityLevel, string> = {
  HIGH: '#ff4d4f',
  MEDIUM: '#faad14',
  LOW: '#52c41a'
};

export default function Notifications() {
  const [loader,setLoader] = useState(false)
  const userId = useSelector((state: RootState) => state.user.id);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [notifications, setNotifications] = useState<any[]>([]);
  const [page, setPage] = useState(1);
const [hasNext, setHasNext] = useState(true);
const [loadingMore, setLoadingMore] = useState(false);
//   const router = useRouter()
const scrollRef = useRef<HTMLDivElement | null>(null);


useEffect(() => {
      setLoader(true);
      fetchNotifications(1).finally(() => setLoader(false));
  }, []);

  const fetchNotifications = async (pageToFetch = 1) => {
    const payload = {
      input: {
        userId,
        page: pageToFetch,
        limit: 10, // or any default
      },
    };
  
    try {
      const res = await getNotificationList(payload);
      const newNotifications = res?.notificationHistory?.notifications || [];
  
      setNotifications((prev) => pageToFetch === 1 ? newNotifications : [...prev, ...newNotifications]);
      setHasNext(res?.notificationHistory?.hasNext);
      setPage(pageToFetch);
    } catch (err) {
      console.error(err);
    }
  };

  const handleBack = () => {
    if(typeof window !== 'undefined')
    {window.history.back();}
  }

  useEffect(() => {
    const handleScroll = () => {
      if (!scrollRef.current || loadingMore || !hasNext) return;
  
      const { scrollTop, scrollHeight, clientHeight } = scrollRef.current;
  
      if (scrollTop + clientHeight >= scrollHeight - 50) {
        setLoadingMore(true);
        fetchNotifications(page + 1).finally(() => setLoadingMore(false));
      }
    };
  
    const scrollContainer = scrollRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener("scroll", handleScroll);
    }
  
    return () => {
      if (scrollContainer) {
        scrollContainer.removeEventListener("scroll", handleScroll);
      }
    };
  }, [loadingMore, hasNext, page]);
  

  const getAvatarColor = (priority: string) =>
    priorityColors[priority as PriorityLevel] || '#d9d9d9';
  return (
   <>
   {loader && <Loader/>}
   <div className="w-full flex flex-col h-full bg-white rounded-lg shadow-sm">
             <h2 className="text-xl text-white bg-teal-500 font-semibold mb-4 p-4">Notifications</h2>
           
             <div ref={scrollRef} className="flex-1 overflow-y-auto px-4">
            
<div
                    className="text-sm font-200 text-gray-700  pl-4 m-0 rounded flex items-end !justify-end w-full"
                  >
                   <Button className="font-normal !h-[40px] !mt-0 text-[12px] !w-[75px] !shadow-none" onClick={handleBack}>Back</Button>
                  </div>
    {notifications.length === 0 ? (
  <div className="text-center text-sm text-gray-500 py-4">
    No records found.
  </div>
) : (
  notifications.map((note, idx) => {
    const name = (note.senderName || 'NA').slice(0, 2).toUpperCase();
    const bgColor = note.isRead ? 'bg-gray-100' : 'bg-white';
    const avatarBg = getAvatarColor(note.priority);

    return (
      <div
        key={idx}
        className={`flex items-start gap-3 p-3 mb-3 rounded-lg shadow-sm ${bgColor}`}
      >
        <Avatar sx={{ bgcolor: avatarBg }}>{name}</Avatar>
        <div className="flex-1">
          <p className="text-sm font-medium">{note.message}</p>
          <span className="text-xs text-gray-500">
            {formatDistanceToNow(new Date(note.createdAt), { addSuffix: true })}
          </span>
        </div>
      </div>
    );
  })
)}


                     {loadingMore && <Loader/>}
             </div>
             
             
           </div>
           
   </>
  );
}
