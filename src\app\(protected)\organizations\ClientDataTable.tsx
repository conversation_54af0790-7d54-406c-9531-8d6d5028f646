/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import * as React from "react";
import { TableHeader } from "./TableHeader";
import { DataTable } from "./DataTable";
// import { ClientData } from "@/types/user";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
// import Pagination from "./PaginationControls";
import { Button } from "@mui/material";
import { useParams, useRouter } from "next/navigation";
// import DeleteModal from "@/app/components/deleteModel";
import {
  deleteOrgUsers,
  deleteUser,
  getByOrganization,
} from "@/api/organizations/organizations";
import { showToast } from "@/components/toaster/ToastProvider";
import DeleteModal from "@/components/deleteModel";
import { setSingleClient } from "@/features/client/clientByIdSlice";
import Loader from "@/components/loader/loader";
import CreateDrawer from "./createDrawer";
import { clearExportId, setExportId } from "@/features/export/exportSlice";
import { getSubModulePermission } from "@/utils/generic";

export function ClientDataTable({
  title,
  handleGetApi,
  pagination,
  setQuery,
  query,
}: {
  title: string;
  handleGetApi: () => void;
  pagination?: any;
  setQuery?: any;
  query?: any;
}) {
  const tableData: any = useSelector((state: RootState) => state?.client);
  const [tableConfig, setTableConfig] = React.useState<any>();
  const [clients, setClients] = React.useState<
    { [key: string]: string | boolean }[]
  >([]);
  const [filters] = React.useState<Record<string, string[]>>({});
  const [sortColumn, setSortColumn] = React.useState<string>("");
  const [sortDirection, setSortDirection] = React.useState<
    "asc" | "desc" | null
  >("asc");
  const [page, setPage] = React.useState(1);
  const [openDelete, setOpenDelete] = React.useState(false);
  const [_id, setId] = React.useState("");
  const [loader, setLoader] = React.useState(false);
  const { id } = useParams();
  const [pageSize, setPageSize] = React.useState(10);
  const [totalItems, setTotalItems] = React.useState(0);
  const totalPages = Math.ceil(totalItems / pageSize);
  const [drawerOpen, setDrawerOpen] = React.useState(false);
  const router = useRouter();
  const dispatch = useDispatch();
  const exportId = useSelector((state: RootState) => state.exportId.exportId);
  const [orgId, setOrgId] = React.useState("");

  const [ids, setIds] = React.useState<string[]>([]);
  React.useEffect(() => {
    if (exportId.length == 0) {
      setIds([]);
    }
  }, [exportId]);

  React.useEffect(() => {
    dispatch(clearExportId());
    getByOrganization({ clientId: id })
      .then((res) => {
        setOrgId(res?.getByClient?.data?.client?.orgId);
      })
      .catch((err) => {
        console.error(err);
      });
  }, []);

  const handleCheck = (id: string) => {
    let newData: string[];

    if (ids.includes(id)) {
      newData = ids.filter((item) => item !== id);
    } else {
      newData = [...ids, id];
    }

    setIds(newData);
    dispatch(setExportId(newData));
  };

  React.useEffect(() => {
    setPage(pagination?.page);
    setTotalItems(pagination?.total);
  }, [pagination]);

  React.useEffect(() => {
    setSortColumn(query.sortBy);
    setSortDirection(query.sortOrder);
  }, [query]);

  React.useEffect(() => {
    setTableConfig(tableData.tableConfig);
    setClients(tableData.data);
  }, [tableData]);

  const handleAddClient = () => {
    if (title == "Organizations") {
      router.push("/organizations/create");
    } else if (
      title == "Organization Users" ||
      title == "Sub Organization Users"
    ) {
      setDrawerOpen(true);
    } else {
      router.push(`/organizations/${id}/subOrganization/create`);
    }
  };

  const handleCloseDelete = () => {
    setOpenDelete(false);
  };
  const handlePageChange = (newPage: number) => {
    if (newPage < 1 || newPage > totalPages) return;
    setPage(newPage);
    setQuery((prev: any) => ({
      ...prev,
      page: newPage,
    }));
  };

  const handlePageSizeChange = (newSize: number) => {
    setPageSize(newSize);
    setPage(1);
    setQuery((prev: any) => ({
      ...prev,
      limit: newSize,
    }));
  };
  React.useEffect(() => {
    setSortColumn(query.sortBy);
    setSortDirection(query.sortOrder);
  }, [query]);

  const handleToggleSelectAll = () => {
    if (!tableConfig?.settings?.selectable) return;

    const pageClientIds = filteredAndSortedClients.map(
      (client: any) => client._id
    );

    const allOnPageSelected = pageClientIds.every((id: any) =>
      ids.includes(id)
    );

    let newIds: string[];

    if (allOnPageSelected) {
      newIds = ids.filter((id) => !pageClientIds.includes(id));
    } else {
      newIds = [...new Set([...ids, ...pageClientIds])];
    }
    setIds(newIds);
    dispatch(setExportId(newIds));
  };

  const handleToggleSelect = (id: string) => {
    handleCheck(id);
  };

  const handleEdit = (id: string) => {
    setId(id);
    if (title == "Organization Users" || title == "Sub Organization Users") {
      setDrawerOpen(true);
    } else {
      setLoader(true);
      getByOrganization({ clientId: id })
        .then((res) => {
          dispatch(setSingleClient(res.getByClient.data.client));
          if (title == "Organizations") {
            router.push(`/organizations/${id}/info`);
          } else {
            router.push(`/organizations/${id}/subOrganization/info`);
          }
          setLoader(false);
        })
        .catch((err) => {
          console.error(err);
        });
    }
  };

  const handleDelete = (id: string) => {
    setId(id);
    setOpenDelete(true);
  };

  const submitDelete = () => {
    setLoader(true);
    if (title == "Organization Users" || title == "Sub Organization Users") {
      deleteOrgUsers({ id: _id })
        .then(async (res) => {
          console.log(res);
          showToast.success(
            title == "Sub Organization Users"
              ? "Sub Organization User Deleted Successfully"
              : "Organization User Deleted Successfully"
          );
          if((filteredAndSortedClients.length === 1 || filteredAndSortedClients.length === 1) && page != 1){
            handlePageChange(page-1)
          await  handleGetApi();
          }else{
            await handleGetApi();
          }
          setOpenDelete(false);
          setLoader(false);
        })
        .catch((err) => {
          showToast.error(err.message);
          setLoader(false);
          setOpenDelete(false);
        });
    } else {
      deleteUser({ input: { id: _id } })
        .then(async (res) => {
          console.log(res);
          showToast.success(res.deleteClient.data.message);
          if((filteredAndSortedClients.length === 1 || filteredAndSortedClients.length === 1) && page != 1){
            handlePageChange(page-1)
          await  handleGetApi();
          }else{
            await handleGetApi();
          }
          setOpenDelete(false);
          setLoader(false);
        })
        .catch((err) => {
          showToast.error(err.message);
          setLoader(false);
          setOpenDelete(false);
        });
    }
  };

  const handleFilterChange = (column: string, values: string[]) => {
    setQuery((prev: any) => {
      // Parse existing filters string to object (if it's a string)
      const currentFilters =
        typeof prev.filters === "string"
          ? JSON.parse(prev.filters)
          : prev.filters || {};

      const updatedFilters = {
        ...currentFilters,
        [column]: values[0],
      };

      return {
        ...prev,
        filters: JSON.stringify(updatedFilters),
        page: 1,
      };
    });
  };
  const handleSort = (column: string) => {
    setQuery((prev: any) => ({
      ...prev,
      sortBy: column,
      sortOrder: prev.sortOrder === "asc" ? "desc" : "asc",
    }));
    setSortColumn(column);
    setSortDirection((prev) => (prev === "asc" ? "desc" : "asc"));
  };

  const filteredAndSortedClients = React.useMemo(() => {
    let filtered = clients;

    // Apply filters
    if (tableConfig?.settings?.filterable) {
      Object.entries(filters).forEach(([column, values]) => {
        if (values.length > 0) {
          const columnConfig = tableConfig?.columns?.find(
            (col: { title: string }) => col.title === column
          );
          if (columnConfig) {
            filtered = filtered.filter((client) =>
              values.includes(
                String(client[columnConfig.id as keyof Record<string, string>])
              )
            );
          }
        }
      });
    }

    // Apply sorting
    if (sortColumn && sortDirection) {
      const columnConfig = tableConfig?.columns?.find(
        (col: { title: string }) => col.title === sortColumn
      );
      if (columnConfig?.sortable) {
        filtered = [...filtered].sort((a, b) => {
          const aVal = String(
            a[columnConfig.id as keyof Record<string, string>]
          ).toLowerCase();
          const bVal = String(
            b[columnConfig.id as keyof Record<string, string>]
          ).toLowerCase();

          if (sortDirection === "asc") {
            return aVal.localeCompare(bVal);
          } else {
            return bVal.localeCompare(aVal);
          }
        });
      }
    }

    return filtered;
  }, [clients, filters, sortColumn, sortDirection, tableConfig]);

  const allSelected =
    tableConfig?.settings?.selectable &&
    filteredAndSortedClients?.length > 0 &&
    filteredAndSortedClients?.every((client: any) => ids.includes(client._id));

  return (
    <main className="overflow-hidden mx-auto my-0 w-full bg-white rounded-xl max-w-[1900px] shadow-[0_1px_3px_rgba(0,0,0,0.1)]">
      <hr className="border-slate-100" />
      <div className="border border-slate-100 rounded-lg">
        <p className="px-4 mt-2 text-md font-semibold text-[#1465AB] flex justify-between items-center">
          {title}{" "}
          {getSubModulePermission(
            title == "Organizations"
              ? "Main Organization"
              : title == "Sub Organization Users"
                ? "Sub Organization Settings"
                : title == "Organization Users"
                  ? "Main Organization Settings"
                  : "Sub Organization",
            title == "Organization Users" || title == "Sub Organization Users"
              ? "Add Users"
              : "Add"
          )?.isEnabled && (
            <span>
              {" "}
              <Button
                className="!h-[40px] rounded-[3px] !m-0 !bg-teal-500 p-2 w-[auto] !inset-shadow-none"
                onClick={handleAddClient}
              >
                <span className="text-[14px] mx-2">Add New {title} </span>
              </Button>
            </span>
          )}
        </p>

        <hr className="mx-0 mt-2 border-slate-100" />
        {tableConfig?.columns?.length !== 0 ? (
          <>
            <TableHeader
              title={title}
              page={page}
              totalPages={totalPages}
              pageSize={pageSize}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              handleGetApi={handleGetApi}
              query={query}
              totalItems={totalItems}
            />
            <DataTable
              tableConfig={{
                ...tableConfig,
                settings: {
                  ...tableConfig?.settings,
                  defaultSortDirection: query.sortOrder as "asc" | "desc",
                },
              }}
              clients={filteredAndSortedClients?.map((client: any) => ({
                ...client,
                isSelected: ids.includes(client.id),
              }))}
              onToggleSelectAll={handleToggleSelectAll}
              onToggleSelect={handleToggleSelect}
              handleCheck={handleCheck}
              onEdit={handleEdit}
              onDelete={handleDelete}
              allSelected={allSelected}
              filters={filters}
              title={title}
              onFilterChange={handleFilterChange}
              onSort={handleSort}
              sortColumn={sortColumn}
              sortDirection={sortDirection}
            />
          </>
        ) : (
          <h5 className="px-4 my-4 text-md font-semibold text-center">
            No Records found
          </h5>
        )}
        {/* <Pagination
          page={page}
          totalPages={totalPages}
          pageSize={pageSize}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
        /> */}
      </div>
      {openDelete && (
        <DeleteModal
          isOpen={openDelete}
          onClose={handleCloseDelete}
          onDelete={submitDelete}
        />
      )}
      {loader && <Loader />}
      <CreateDrawer
        open={drawerOpen}
        _id={_id}
        orgId={orgId}
        onClose={() => {
          setDrawerOpen(false);
          handleGetApi();
          setId("");
          setQuery((prev: any) => ({
            ...prev,
            page: 1,
          }));
        }}
        onSave={() => {
          handleGetApi();
          setId("");
          setQuery((prev: any) => ({
            ...prev,
            page: 1,
          }));
        }}
      />
    </main>
  );
}

export default ClientDataTable;
