"use client";
import * as React from "react";

interface DropdownButtonProps {
  label: string;
  icon?: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

export function DropdownButton({
  label,
  icon,
  className = "",
  onClick,
}: DropdownButtonProps) {
  return (
    <button
      className={`flex gap-2 justify-between items-center px-4 py-2 bg-white rounded border border-solid cursor-pointer border-zinc-200 min-w-[126px] max-sm:w-full max-sm:min-w-[auto] ${className}`}
      onClick={onClick}
    >
      {icon && <div>{icon}</div>}
      <span className="text-xs text-slate-400">{label}</span>
      <div>
        <svg
          className="dropdown-arrow"
          width="10"
          height="6"
          viewBox="0 0 10 6"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          style={{ width: "10px", height: "6px" }}
        >
          <path
            d="M8.79467 0.5C9.23987 0.5 9.46307 1.03805 9.14859 1.35319L5.36432 5.14534C5.16897 5.34109 4.85182 5.34109 4.65648 5.14534L0.872209 1.35318C0.557732 1.03805 0.780929 0.499999 1.22613 0.499999L8.79467 0.5Z"
            fill="#A3AED0"
          ></path>
        </svg>
      </div>
    </button>
  );
}
