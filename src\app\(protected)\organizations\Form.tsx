/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { Card } from "@/app/[components]/card";
import { RootState } from "@/store";
import { Icon, InputLabel, Typography } from "@mui/material";
import Image from "next/image";
import addIcon from "../../../assests/AddIcon.png";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { createUser, updateUser } from "@/api/organizations/organizations";
import { showToast } from "@/components/toaster/ToastProvider";
import { useParams } from "next/navigation";
import { isDynamicSectionValid, toCamelCase } from "@/utils/generic";
import CreateDrawer from "@/app/[components]/createDrawer";
import { editIcon } from "@/app/[components]/editIcon";
import { deleteIcon } from "@/app/[components]/deleteIcon";
import { getPreSignedUrl } from "@/api/file/file";
import { RenderField } from "@/app/[components]/FormRender";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";

dayjs.extend(utc);
interface SelectOption {
  id?: string;
  code?: string;
  name?: string;
  value?: string;
  [key: string]: any; // For additional properties
}

interface FormData {
  [section: string]: {
    [field: string]: string | SelectOption | SelectOption[];
  };
}

const ClientOnboardingForm = ({
  type,
  formTemplate,
  clientTyoe,
  templateId,
  flattedValues,
  access
}: {
  type: string;
  formTemplate: any;
  clientTyoe: string;
  templateId: string;
  flattedValues?: any;
  access:boolean
}) => {
  console.log("flattedValues", flattedValues);

  const collapsed = useSelector((state: RootState) => state.sideNav.sideNav);
  const formDataById: any = useSelector((state: RootState) => state.clientById);
  console.log("formDataById", formDataById);

  const id = useSelector((state: RootState) => state.user.id);
  const userId = useParams();
  console.log("userId", userId);

  // const dispatch = useDispatch();
  const [formData, setFormData] = useState<FormData>({});
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [editIndex, setEditIndex] = useState<number | null>(null);
  const [editRowData, setEditRowData] = useState<any | null>(null);
  const [gridFieldMeta, setGridFieldMeta] = useState<{
    sectionName: string;
    fieldId: string;
    columns: any[];
  } | null>(null);

  // const [drawerOpen, setDrawerOpen] = React.useState(false);
  const isEditMode = !collapsed;
  const columnClasses = isEditMode
    ? "grid-cols-1 md:grid-cols-3"
    : "grid-cols-1 md:grid-cols-4";

  console.log("formData", formData);

  useEffect(() => {
    if (type === "edit" && formDataById) {
      if (typeof formDataById?.values == "string" || typeof formDataById?.values == "object" ) {
        const patchedData = clientTyoe === "SUB_CLIENT"?formDataById.values:JSON.parse(formDataById?.values);
        console.log("patchedData", patchedData);

        setFormData(patchedData);
      }
    }
  }, [type, formDataById]);

  const handleFieldChange = async (
    sectionName: string,
    fieldLabel: string,
    value: any
  ) => {
    const section = toCamelCase(sectionName);
    const field = toCamelCase(fieldLabel);
    console.log("value", value, fieldLabel, field);
    console.log("fieldLabel", fieldLabel, value);
    // For non-image fields
    // if (!fieldLabel.toLowerCase().includes('image')) {

    //   return;
    // }

    // For image fields

    if (fieldLabel.toLowerCase().includes("image")) {
      if (value) {
        const path = `organization/profile/${value.name}`; // <-- desired path in storage
        const payload = {
          input: { filename: path as string, contentType: "application/pdf" },
        };

        try {
          const uploadPath = await getPreSignedUrl(payload);

          console.log(
            "uploadPath",
            uploadPath.generateUploadUrl.data.uploadUrl
          );
          // const path = `organization/profile/${value}`; // <-- desired path in storage
          const uploadResponse = await fetch(
            uploadPath.generateUploadUrl.data.uploadUrl,
            {
              method: "PUT",
              body: value,
              headers: {
                "Content-Type": "image/png", // fallback
              },
            }
          );

          if (!uploadResponse.ok) {
            throw new Error("Something went wrong. Please try again.");
          }

          // Save the file path or accessible URL to formData
          setFormData((prevData: any) => ({
            ...prevData,
            [section]: {
              ...(prevData[section] || {}),
              [field]: path, // Assuming this is returned by getPreSignedUrl
            },
          }));
          console.log(uploadPath, "uploadPath");
        } catch (e) {
          console.error("Image upload failed:", e);
        }
      }
    } else if (fieldLabel.includes("time")) {
      const utcTime = dayjs(value).utc();
      setFormData((prevData: any) => ({
        ...prevData,
        [section]: {
          ...(prevData[section] || {}),
          [field]: utcTime,
        },
      }));
    } else {
      setFormData((prevData: any) => ({
        ...prevData,
        [section]: {
          ...(prevData[section] || {}),
          [field]: value,
        },
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    console.log("Form data payload:", formData);
    let clientName = null;
    let clientEmail = null;

    for (const section of formTemplate?.sections) {
      for (const field of section.fields) {
        if (field.label === "Client Name") {
          const sectionKey = toCamelCase(section.name);
          const fieldKey = toCamelCase(field.label);
          if (
            formData[sectionKey as keyof typeof formData]?.[
              fieldKey as keyof (typeof formData)[keyof typeof formData]
            ]
          ) {
            clientName =
              formData[sectionKey as keyof typeof formData][
                fieldKey as keyof (typeof formData)[keyof typeof formData]
              ];
          }
          console.log(field.label, formData, sectionKey);
        }
        if (field.label === "Client Email") {
          const sectionKey = toCamelCase(section.name);
          const fieldKey = toCamelCase(field.label);
          if (
            formData[sectionKey as keyof typeof formData]?.[
              fieldKey as keyof (typeof formData)[keyof typeof formData]
            ]
          ) {
            clientEmail =
              formData[sectionKey as keyof typeof formData][
                fieldKey as keyof (typeof formData)[keyof typeof formData]
              ];
          }
        }
      }
    }
    function extractMatchingKeys(
      obj: any[] | Record<string, any> | null,
      keysToExtract: string | string[],
      result: Record<string, any> = {}
    ): Record<string, any> {
      console.log(keysToExtract, "keysToExtract");

      const keys = Array.isArray(keysToExtract)
        ? keysToExtract
        : [keysToExtract];

      if (Array.isArray(obj)) {
        obj.forEach((item) => extractMatchingKeys(item, keys, result));
      } else if (typeof obj === "object" && obj !== null) {
        for (const key in obj) {
          if (keys.includes(key)) {
            result[key] = obj[key];
          }
          extractMatchingKeys(obj[key], keys, result);
        }
      }

      return result;
    }

    const result = extractMatchingKeys(formData, flattedValues);
    console.log(result, "result");
    const payload: any = {
      input: {
        name: clientName ?? "",
        email: clientEmail ?? "",
        flattenedValues: result,
        templateId: templateId,
        values: JSON.stringify(formData) ?? "", // must be an object
        type: clientTyoe,
        created_by: id,
      },
    };
    console.log(payload, "payload");

    if (clientTyoe === "SUB_CLIENT") {
      payload.input["main_client"] = userId?.id; // or whatever the correct ID is
      payload.input["values"]= formData // must be an object
    }

    console.log(payload);
    if (type === "edit") {
      const payloads :any= {
        input: {
          id: formDataById._id,
          flattenedValues: result,
          values: JSON.stringify(formData) ?? "", // must be an object
        },
      };
      if (clientTyoe === "SUB_CLIENT") {
      payloads.input["values"]= formData // must be an object
    }
      updateUser(payloads)
        .then(() => {
          showToast.success("UPDATED");
          if (typeof window !== "undefined") {
            window.history.back();
          }
        })
        .catch((err) => {
          showToast.error(err.message);
          console.error(err);
        });
    } else {
      if (clientName !== "" && clientEmail !== "") {
        await createUser(payload)
          .then((res) => {
            console.log(res);

            showToast.success("CREATED");
            if (typeof window !== "undefined") {
              window.history.back();
            }
          })
          .catch((err) => {
            showToast.error(err.message);
            console.error(err);
          });
      } else {
        showToast.error("Please fill in all required fields");
      }
    }
  };

  const addGridRow = ({
    sectionName,
    newRow,
  }: {
    sectionName: string;
    newRow: Record<string, string>;
  }) => {
    const sectionKey = toCamelCase(sectionName);
    console.log("newRow", newRow, sectionName);

    setFormData((prevData: any) => ({
      ...prevData,
      [sectionKey]: [
        ...(prevData[sectionKey] || []), // get existing rows if any
        newRow, // append the new row
      ],
    }));
  };

  const handleGridDelete = (sectionName: string, index: number) => {
    const sectionKey = toCamelCase(sectionName);

    setFormData((prev: any) => {
      const dataArray = prev[sectionKey];
      if (!Array.isArray(dataArray) || index < 0 || index >= dataArray.length) {
        return prev;
      }
      const updatedArray = dataArray.filter((_, i) => i !== index); // Remove by index
      return {
        ...prev,
        [sectionKey]: updatedArray,
      };
    });
  };

  const handleGridEdit = (
    sectionName: string,
    fieldLabel: string,
    index: number,
    columns: any[],
    existingRow: any
  ) => {
    setGridFieldMeta({
      sectionName,
      fieldId: fieldLabel,
      columns,
    });
    setEditIndex(index);
    setEditRowData(existingRow);
    setDrawerOpen(true);
  };
  console.log("formData", formData);

  return (
    <div className="mt-4">
      <div className="space-y-6">
        <Card className="border border-gray-300 !p-0 !rounded-lg">
          <form onSubmit={handleSubmit} className="space-y-6">
            {formTemplate?.sections?.map(
              (section: { id: string; name: string; fields: any[] }) => (
                <div key={section.id}>
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2 bg-blue-50 px-2 py-1 pt-[10px]">
                    <Typography variant="h6">{section.name}</Typography>

                    {/* Check if any grid field exists */}
                    {section.fields.some((f) => f.field_type === "grid") && (
                     access && <button
                        onClick={() => {
                          setGridFieldMeta({
                            sectionName: section.name,
                            fieldId: section.id,
                            columns: section.fields[0].columns,
                          });
                          setDrawerOpen(true);
                        }}
                        type="button"
                        className="text-sm font-200 text-gray-700 bg-none px-4 py-2 rounded flex"
                      >
                        <Icon>
                          <Image
                            src={addIcon}
                            alt="add"
                            width={20}
                            height={20}
                          />
                        </Icon>
                        <span>Add Row</span>
                      </button>
                    )}
                  </div>
                  <div className={`grid ${columnClasses} gap-4 p-4`}>
                    {section.fields
                      .filter(
                        (field) =>
                          field.field_type !== "grid" &&
                          field.field_type !== "checkboxes" &&
                          !field.only_in_grid &&
                          !field.only_in_custom_form
                      )
                      .map((field) => (
                        <div key={field.id}>
                          <InputLabel
                            className="!text-[22px] font-medium text-gray-800"
                            shrink
                          >
                            {field.label}
                            {field?.required && (
                              <span style={{ color: "red" }}> *</span>
                            )}
                          </InputLabel>
                          <RenderField
                            field={field}
                            sectionName={section.name}
                            formData={formData}
                            handleFieldChange={handleFieldChange}
                            type={type}
                            handleGridEdit={handleGridEdit}
                            handleGridDelete={handleGridDelete}
                            editIcon={editIcon}
                            deleteIcon={deleteIcon}
                            access={access}
                          />
                        </div>
                      ))}
                  </div>
                  {section.fields
                    .filter((field) => field.field_type === "grid")
                    .map((field) => (
                      <div key={field.id}>
                        <RenderField
                          field={field}
                          sectionName={section.name}
                          formData={formData}
                          handleFieldChange={handleFieldChange}
                          type={type}
                          handleGridEdit={handleGridEdit}
                          handleGridDelete={handleGridDelete}
                          editIcon={editIcon}
                          deleteIcon={deleteIcon}
                          access={access}
                        />
                      </div>
                    ))}

                  {section.fields
                    .filter((field) => field.field_type === "checkboxes")
                    .map((field) => (
                      <div key={field.id} className="px-4">
                        <InputLabel
                          className="!text-[22px] font-medium text-gray-800"
                          shrink
                        >
                          {field.label}
                          {field?.required && (
                            <span style={{ color: "red" }}> *</span>
                          )}
                        </InputLabel>
                        <RenderField
                          field={field}
                          sectionName={section.name}
                          formData={formData}
                          handleFieldChange={handleFieldChange}
                          type={type}
                          handleGridEdit={handleGridEdit}
                          handleGridDelete={handleGridDelete}
                          editIcon={editIcon}
                          deleteIcon={deleteIcon}
                           access={access}
                        />
                      </div>
                    ))}
                </div>
              )
            )}
            <div className="w-full flex items-end !justify-end mb-6 !pr-4">
              {access && <button
                type="submit"
                className="bg-[#1969AE] hover:bg-[#155b96] text-white font-bold py-2 px-6 rounded "
                disabled={!isDynamicSectionValid(formData)}
                // onClick={handleSubmit}
                // onClick={()=>handleSaveAndClose()}
              >
                Save &amp; Close
              </button>}
            </div>
          </form>
        </Card>
      </div>
      {gridFieldMeta && (
        <CreateDrawer
          title={gridFieldMeta.sectionName}
          open={drawerOpen}
          onClose={() => setDrawerOpen(false)}
          editRow={editRowData}
          columns={gridFieldMeta?.columns}
          onSave={(rowData) => {
            if (editIndex !== null && editRowData) {
              // Update existing row
              const sectionKey = toCamelCase(gridFieldMeta.sectionName);
              setFormData((prevData: any) => {
                const currentRows = [...(prevData[sectionKey] || [])];
                currentRows[editIndex] = rowData;
                return {
                  ...prevData,
                  [sectionKey]: currentRows,
                };
              });
              setEditIndex(null);
              setEditRowData(null);
            } else {
              // Add new row
              addGridRow({
                sectionName: gridFieldMeta.sectionName,
                newRow: rowData,
              });
            }
            setDrawerOpen(false);
          }}
        />
      )}
    </div>
  );
};

export default ClientOnboardingForm;
