
'use client';
import { MenuItem, Select } from "@mui/material";
import { ChevronLeft, ChevronRight } from "lucide-react";
import React, { useEffect, useState } from "react";

interface PaginationProps {
  page: number;
  totalPages: number;
  pageSize: number;
  pageSizes?: number[];
  onPageChange: (newPage: number) => void;
  onPageSizeChange: (newSize: number) => void;
}

export default function Pagination({
  page,
  totalPages,
  pageSize,
  pageSizes = [10, 25, 50, 100,250,500],
  onPageChange,
  onPageSizeChange,
}: PaginationProps) {
  const [inputValue, setInputValue] = React.useState(page);
  const [inputValues, setInputValues] = React.useState(pageSize);
  const [debouncedValue, setDebouncedValue] = useState(10);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(inputValues);
    }, 700); // debounce delay (ms)

    return () => clearTimeout(timer); // cleanup on new keystroke
  }, [inputValues]);

  useEffect(() => {
    const num = Number(debouncedValue);
    // if (!isNaN(num) && num >= 1 && num <= 999) {
      onPageSizeChange(num)
    // }
  }, [debouncedValue]);


  React.useEffect(() => {
    setInputValue(page); // keep input in sync with external changes
  }, [page]);
const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Only allow changes via up/down arrows, not manual typing
    const newValue = Number(e.target.value);
    if (newValue >= 1 && newValue <= totalPages) {
      setInputValue(newValue);
      onPageChange(newValue);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Prevent manual typing, only allow arrow keys and navigation keys
    const allowedKeys = ["ArrowUp", "ArrowDown", "Tab", "Enter", "Escape"];
    if (!allowedKeys.includes(e.key)) {
      e.preventDefault();
    }
  };
  const handleInputBlur = () => {
    if (inputValue < 1 || inputValue > totalPages || isNaN(inputValue)) {
      setInputValue(page); // reset to current if invalid
   
    }
  };

  return (
    <div className="flex items-center justify-end p-2 text-sm text-slate-400">
      {/* Per Page Dropdown */}
      <div className="flex items-center gap-2">
        <span>Per Page</span>
        {/* <select
          value={pageSize}
          onChange={(e) => onPageSizeChange(Number(e.target.value))}
          className="border border-gray-300 rounded px-2 py-1 text-gray-600 focus:outline-none focus:ring-1 focus:ring-blue-400"
        >
          {pageSizes.map((size) => (
            <option key={size} value={size}>
              {size}
            </option>
          ))}
          <option>
              <TextField placeholder="custom"/>
            </option>
        </select> */}
              <Select
              size="small"
               className="!h-[30px] !w-[105px] border-[0px] shadow-none border-gray-200 rounded px-2 py-1 text-gray-600 focus:outline-none focus:ring-1 focus:ring-blue-400"
        labelId="page-size-label"
  value={pageSizes.includes(pageSize!) ? pageSize : '__custom__'}
        MenuProps={{
    PaperProps: {
      sx: {
        width: 105, // Match the Select width
      },
    },
  }}
        onChange={(e) => {
          if (e.target.value !== '__custom__') {
           onPageSizeChange(Number(e.target.value)??10)
          }
        }}
        displayEmpty
        // renderValue={(selected) => {
        //   if (selected === '__custom__') return `Custom: ${customValue}`;
        //   return selected || 'Select';
        // }}
        sx={{ width: 180 }}
      >
        {pageSizes.map((size) => (
          <MenuItem key={size} value={size}>
            {size}
          </MenuItem>
        ))}
<hr/>
        <MenuItem value="__custom__" disableRipple className="!bg-gray-200">
          <input
            type="number"
            // size="small"
            className="!no-spinne !h-[30px] !w-[65px] !border-none !p-0"
            placeholder="Enter custom size"
            value={inputValues}
            onClick={(e) => e.stopPropagation()} // prevent closing menu
        onChange={(e) => {
          setInputValues(Number(e.target.value))
  // const rawValue = e.target.value;
  // const input = Number(rawValue);

  // Check if it's a valid number and within range
  // if (!isNaN(Number(e.target.value)) && e.target.value >= 1 && e.target.value <= 999) {
    // onPageSizeChange(input);
  // }
}}
//  InputProps={{
//     disableUnderline: true, // removes underline
//     inputProps: {
//       min: 1,
//       max: 999,
//       className: 'no-spinner', // optional for hiding number arrows
//     },
//     sx: {
//       height: '30px',
//       padding: 0,
//     },
//   }}
//   sx={{
//     width: '105px',
//     fontSize: '14px',
//     padding: 0,
//     backgroundColor: 'transparent',
//     border: 'none',
//     boxShadow: 'none', // removes any box-shadow
//     '& input': {
//       padding: '0px !important',
//       border: 'none !important',
//       outline: 'none !important', // remove blue outline
//       boxShadow: 'none !important', // remove inset shadow
//     },
//     '& .MuiInputBase-root': {
//       border: 'none',
//       outline: 'none',
//       boxShadow: 'none',
//     },
//     '&:focus': {
//       outline: 'none',
//       boxShadow: 'none',
//     },
//   }}
    // inputProps={{
    //   min: 1,
    //   max: 999,
    // }}
            
            onBlur={(e) => {
              // Optionally trigger close
              if (e.target.value) {
                // setValue('__custom__');
              }
            }}
            // inputProps={{ min: 1 }}
          />
        </MenuItem>
      </Select>
      </div>

      {/* Pagination Controls */}
      <div className="flex items-center gap-2 ml-4">
        <button
          disabled={page <= 1}
          onClick={() => onPageChange(page - 1)}
          className="disabled:text-gray-300"
          aria-label="Previous page"
        >
          <ChevronLeft size={20} />
        </button>

        <input
          type="number"
          min={1}
          max={totalPages}
          value={inputValue}
          onChange={handleInputChange}
                   onKeyDown={handleKeyDown}
          onBlur={handleInputBlur}
          className="w-12 text-center border border-gray-300 rounded px-1 py-0.5 text-gray-600 focus:outline-none"
          style={{
            MozAppearance: "textfield",
            WebkitAppearance: "none",
          }}/>

        <span className="text-slate-500">of {totalPages}</span>

        <button
          disabled={page >= totalPages}
          onClick={() => onPageChange(page + 1)}
          className="disabled:text-gray-300"
          aria-label="Next page"
        >
          <ChevronRight size={20} />
        </button>
      </div>
    </div>
  );
}
