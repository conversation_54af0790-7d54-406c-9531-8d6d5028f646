"use client";
import { toNormalCase } from "@/utils/generic";
import * as React from "react";
import debounce from "lodash/debounce";
import { useMemo } from "react";

interface Column {
  id: string;
  title: string;
  width?: string;
  hasFilter?: boolean;
  type: string;
  filterType?: string;
  placeholder?: string;
  options?: string[];
}

interface TableColumnHeaderProps {
  column: Column;
  className?: string;
  selectedFilters: string[];
  onFilterChange?: (columnId: string, values: string[]) => void;
  onSort?: (columnId: string) => void;
  sortDirection?: "asc" | "desc" | null;
}

export function TableColumnHeader({
  column,
  onSort,
  onFilterChange,
  sortDirection,
}: TableColumnHeaderProps) {
  const handleSort = () => {
    if (onSort) {
      onSort(column.id);
    }
  };
  const handleFilterChange = useMemo(
    () =>
      debounce((value: string) => {
        onFilterChange?.(column.id, [value]);
      }, 700),
    []
  );

  return (
    <th
      className={`sticky z-10 p-2.5 font-medium text-left bg-teal-50 border-r border-b border-solid !border-b-slate-100 !border-r-slate-100 ${
        column.id === "first-column-id" ? "left-0" : ""
      }`}
      style={{ width: column.width }}
    >
      <div className="flex flex-col gap-2">
        <div
          className="text-sm font-medium text-blue-900 cursor-pointer select-none"
        >
          <span onClick={handleSort}>
                    {toNormalCase(column.title)}
                    {!column.title.includes("Image") && (
                      <span className="ml-1 text-[black]">
                        {sortDirection === "asc" ? "↓" : "↑"}
                      </span>
                    )}
                    </span>
        </div>

        <input
          type="text"
          placeholder={column.placeholder}
          style={{visibility:column.title.includes("Image")?'hidden':'visible'}}
          onChange={(e) => handleFilterChange(e.target.value)}
          className="border border-gray-300 bg-white rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-teal-500"
        />
      </div>
    </th>
  );
}
