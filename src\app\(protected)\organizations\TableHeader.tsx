/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import * as React from "react";
import Pagination from "./PaginationControls";
import Image from "next/image";
import settings from "../../../assests/settings.svg";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import {
  createView,
  deleteView,
  getAllView,
  getView,
  UpdateView,
} from "@/api/header/header";
import { setHeaders } from "@/features/headers/headersSlice";
import { toNormalCase } from "@/utils/generic";
import { MenuItem, Select } from "@mui/material";
import Loader from "@/components/loader/loader";
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd";
import { showToast } from "@/components/toaster/ToastProvider";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import { exportTableData } from "@/api/exportAction/export";
import { clearExportId } from "@/features/export/exportSlice";
export function TableHeader({
  page,
  totalPages,
  pageSize,
  onPageChange,
  handleGetApi,
  onPageSizeChange,
  query,
  title,
  totalItems
}: {
  page: number;
  title: string;
  totalPages: number;
  pageSize: number;
  onPageChange: (newPage: number) => void;
  onPageSizeChange: (newSize: number) => void;
  query?: any;
  handleGetApi?: any;
  setPagination?: any;
  totalItems:number
}) {
  const dispatch = useDispatch();
  const header = useSelector((state: RootState) => state.headers);
  const headerDefault = useSelector((state: RootState) => state.headersDefault);
  const exportId = useSelector((state: RootState) => state.exportId.exportId);
  console.log("exportId", exportId);

  const id = useSelector((state: RootState) => state.user.id);
  const [showColumnSelector, setShowColumnSelector] = React.useState(false);
  const [show, setShow] = React.useState(false);
  const [input, setInput] = React.useState("");
  const [loader, setLoader] = React.useState(false);
  const [visibleTitles, setVisibleTitles] = React.useState<string[]>([]);
  const [isEditing, setIsEditing] = React.useState(false);
  const [editingViewId, setEditingViewId] = React.useState<string | null>(null);
  const [updatedColumns, setUpdatedColumns] = React.useState<
    { title: string; visible: boolean }[]
  >([]);
  const [options, setOptions] = React.useState<
    { label: string; value: string; grid_fields?: any }[]
  >([]);
  const [view, setView] = React.useState("Default");
  const selectorRef = React.useRef<HTMLDivElement>(null);
  const [selectedAction] = React.useState("");
  // const getCurrentVisibleTitles = () =>
  //   updatedColumns.filter((col) => col.visible).map((col) => col.title);
  const handleChangeView = (value: string) => {
    if (value !== "Default" && value !== "add") {
      const payload = {
        id: value,
      };

      setLoader(true);
      getView(payload).then((res) => {
        console.log(
          "res.gridTemplate",
          res.gridTemplate.data.gridTemplate.grid_fields
        );
        dispatch(setHeaders(res.gridTemplate.data.gridTemplate.grid_fields));
        // dispatch(setTitles(res.gridTemplate.data.gridTemplate.grid_fields))
        handlegetData(false);
      });
    } else if (value == "add") {
      setShow(true);
      setShowColumnSelector(true);
    } else {
      handlegetData(true);
    }
    setLoader(false);
  };
  const getViewAll = () => {
    getAllView({
      type: title === "Organizations" ? "organizations" : "subOrganizations",
    })
      .then((res) => {
        setOptions(
          res.gridTemplates.data.data.gridTemplates.map((item: any) => {
            return {
              value: item._id,
              label: item.name,
              grid_fields: item.grid_fields,
            };
          })
        );
      })
      .catch((err) => {
        console.error(err);
      });
  };
  const handlegetData = (isDefault?: boolean) => {
    const payloads = {
      input: { ...query },
    };
    console.log("isDefault", isDefault);

    if (isDefault) {
      handleGetApi();
    } else {
      handleGetApi(payloads);
    }
  };
  function mergeHeaders(
    headerDefault: any[],
    header: any[]
  ): { title: string; visible: boolean }[] {
    const parseTitle = (item: any): string => {
      if (typeof item === "string") return item;
      if (typeof item === "object" && item !== null) {
        return Object.entries(item)
          .filter(([k]) => !isNaN(Number(k)))
          .sort(([a], [b]) => Number(a) - Number(b))
          .map(([, v]) => v)
          .join("");
      }
      return "";
    };

    const headerTitles = new Set(header.map(parseTitle));

    return headerDefault.map((item) => {
      const title = parseTitle(item);
      return {
        title,
        visible: headerTitles.has(title),
      };
    });
  }
  React.useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        selectorRef.current &&
        !selectorRef.current.contains(event.target as Node)
      ) {
        setShowColumnSelector(false);
      }
    }

    if (showColumnSelector) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showColumnSelector, setShowColumnSelector]);
  const handlePatched = () => {
    const merged = mergeHeaders(headerDefault, header);
    console.log("merged", merged);

    setUpdatedColumns(merged);
  };

  React.useEffect(() => {
    handlePatched();
  }, [headerDefault, header]);

  React.useEffect(() => {
    getViewAll();
  }, []);

  const getVisibleColumnTitles = (input: any[]): string[] => {
    return input.reduce((acc: string[], item: any) => {
      if (typeof item === "string") {
        acc.push(item);
      } else if (typeof item === "object" && item?.visible === true) {
        const key = Object.keys(item).find((k) => k !== "visible");
        if (key) acc.push(item[key]);
      }
      return acc;
    }, []);
  };
  const handleSubmit = () => {
    setVisibleTitles(getVisibleColumnTitles(updatedColumns));
    const visibleCount = getVisibleColumnTitles(updatedColumns);

    dispatch(setHeaders(visibleCount));
    handlegetData(false);
    setShow(false);
    setShowColumnSelector(false);
  };
  /**
   * @param {string} id - The id of the column to toggle.
   */ const toggleColumnVisibility = (index: number) => {
    const updated = updatedColumns.map((col, i) =>
      i === index ? { ...col, visible: !col.visible } : col
    );
    setUpdatedColumns(updated);
  };
  const visibleCount = updatedColumns.filter((col) => col.visible).length;
  const totalCount = updatedColumns.length;
  const handleSave = () => {
    const payload = {
      input: {
        name: input,
        grid_fields: visibleTitles,
        user_id: id,
        type: title === "Organizations" ? "organizations" : "subOrganizations",
      },
    };
    const updatepayload = {
      input: {
        id: editingViewId ?? "",
        name: input,
        // grid_fields: visibleTitles,
        user_id: id,
        type: title === "Organizations" ? "organizations" : "subOrganizations",
      },
    };
    setLoader(true);
    if (isEditing && editingViewId) {
      UpdateView(updatepayload)
        .then(async () => {
          setShow(false);
          setShowColumnSelector(false);
          setIsEditing(false);
          setEditingViewId(null);
          setInput("");
          await getViewAll();
          setLoader(false);
        })
        .catch((err) => {
          setLoader(false);
          showToast.error((err as { message: string }).message);
        });
    } else {
      createView(payload)
        .then(async (res) => {
          setShow(false);
          setShowColumnSelector(false);
          setIsEditing(false);
          setEditingViewId(null);
          setInput("");
          await getViewAll();
          setView(res.createGridTemplate.data.gridTemplate._id);
          setLoader(false);
        })
        .catch((err) => {
          setLoader(false);
          showToast.error((err as { message: string }).message);
        });
    }
    setShowColumnSelector(false);
  };
  const handleReset = () => {
    if (view && view !== "Default" && view !== "__add_new_view__") {
      setLoader(true);
      const payload = {
        id: view,
      };

      getView(payload)
        .then((res) => {
          const gridFields = res.gridTemplate.data.gridTemplate.grid_fields;

          // Set updated columns to match saved view
          const resetColumns = headerDefault.map((item: any) => {
            let title = "";
            if (typeof item === "string") {
              title = item;
            } else if (typeof item === "object") {
              title = Object.entries(item)
                .filter(([key]) => !isNaN(Number(key)))
                .sort(([a], [b]) => Number(a) - Number(b))
                .map(([, val]) => val)
                .join("");
            }

            return {
              title,
              visible: gridFields.includes(title),
            };
          });

          setUpdatedColumns(resetColumns);
          setVisibleTitles(gridFields);
          dispatch(setHeaders(gridFields));
          handlegetData(false);
        })
        .catch((err) => {
          console.error("Reset failed:", err);
        })
        .finally(() => {
          setLoader(false);
          setShowColumnSelector(false);
        });
    } else {
      // fallback: just close if view is Default or unsaved
       setUpdatedColumns(
           headerDefault.map((item: any) => {
             let title = "";
             if (typeof item === "string") {
               title = item;
             } else if (typeof item === "object") {
               title = Object.entries(item)
                 .filter(([key]) => !isNaN(Number(key)))
                 .sort(([a], [b]) => Number(a) - Number(b))
                 .map(([, val]) => val)
                 .join("");
             }
             return {
               title,
               visible: true,
             };
           })
         );
         setVisibleTitles(
           headerDefault.map((item: any) => {
             if (typeof item === "string") return item;
             if (typeof item === "object") {
               return Object.entries(item)
                 .filter(([key]) => !isNaN(Number(key)))
                 .sort(([a], [b]) => Number(a) - Number(b))
                 .map(([, val]) => val)
                 .join("");
             }
             return "";
           })
         );
         dispatch(
           setHeaders(
             headerDefault.map((item: any) => {
               if (typeof item === "string") return item;
               if (typeof item === "object") {
                 return Object.entries(item)
                   .filter(([key]) => !isNaN(Number(key)))
                   .sort(([a], [b]) => Number(a) - Number(b))
                   .map(([, val]) => val)
                   .join("");
               }
               return "";
             })
           )
         );
    }
  };
  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const items = Array.from(updatedColumns);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    setUpdatedColumns(items);
  };

  // const arraysEqual = (a: string[], b: string[]) =>
  //   a.length === b.length && a.every((v) => b.includes(v));

  // const isDuplicateView = options.some((opt: any) =>
  //   arraysEqual(getCurrentVisibleTitles(), opt.grid_fields || [])
  // );
  const handleEditView = (viewId: string) => {
    setView(viewId);
    setShow(true);
    setShowColumnSelector(true);
    setIsEditing(true);
    setEditingViewId(viewId);

    const viewToEdit = options.find((opt: any) => opt.value === viewId);
    setInput(viewToEdit ? viewToEdit.label : "");
  };

  const handleChangeActions = async (value: string) => {
    // setSelectedAction(value);
    const payload = {
      input: {
        selectedRow: exportId,
        collection: "organisations",
        fields: [...header].flat().filter(item => !item.toLowerCase().includes("image")),
        filters: query.filters,
        createdBy: id,
        sortOrder: query.sortOrder,
        sortBy: query.sortBy,
        fileType: value.includes("CSV")
          ? "csv"
          : value.includes("Excel")
            ? "xlsx"
            : "json",
      },
    };
     if (value=='Print') {
        payload.input.fields =  [...header,'values'].flat().filter(item => !item.toLowerCase().includes("image"))
      }
    console.log("payload", payload);

    await exportTableData(payload)
      .then((res) => {
        console.log(res);
        showToast.success(res.startExport.message);
        dispatch(clearExportId());
      })
      .catch((err) => {
        console.error(err);
      });
  };
  return (
    <header className="flex gap-5 justify-between items-center pl-4 py-2 bg-white border-b border-solid border-b-slate-100 max-md:flex-col max-md:gap-4 max-sm:px-4 max-sm:py-2.5">
      <div className="flex gap-5 items-center max-md:justify-between max-sm:flex-col max-sm:gap-2.5">
        <Select
          displayEmpty
          value={selectedAction}
          onChange={(e) => handleChangeActions(e.target.value)}
          sx={{
            "& .MuiSelect-select": {
              padding: "10px",
            },
          }}
          style={{ color: "#6B7280" }}
          className="w-[150px] !text-xs !text-gray-500"
        >
          <MenuItem value="" disabled className="!text-gray-500 !text-xs">
            Actions
          </MenuItem>
          <MenuItem value="ExportCSV" className="!text-gray-500 !text-xs">
            Export as CSV
          </MenuItem>
          <MenuItem value="ExportExcel" className="!text-gray-500 !text-xs">
            Export as Excel
          </MenuItem>
          <MenuItem value="Print" className="!text-gray-500 !text-xs">
            Print
          </MenuItem>
          <MenuItem value="Delete" disabled className="!text-gray-500 !text-xs">
            Delete
          </MenuItem>
        </Select>
        {(
            <span className="text-slate-500 ml-1 text-[14px]">
              {totalItems==0?'No':totalItems} Records Found
            </span>
          )}
      </div>
      <div className="flex gap-5 items-center max-md:flex-wrap max-md:gap-2.5 max-md:justify-between max-sm:flex-col max-sm:gap-2.5">
        <Select
          value={view}
          renderValue={(selected) => {
            if (selected === "Default") return "Default";
            const opt = options.find((o) => o.value === selected);
            return opt ? opt.label : selected;
          }}
          onChange={(e) => {
            const selectedValue = e.target.value;
            if (selectedValue === "__add_new_view__") {
              setShow(true);
              setShowColumnSelector(true);
              setIsEditing(false);
              setEditingViewId(null);
              setInput("");
              return;
            }
            setView(selectedValue);
            handleChangeView(selectedValue);
          }}
          sx={{
            "& .MuiSelect-select": {
              padding: "10px",
            },
          }}
          style={{ color: "#6B7280" }}
          className="w-[150px] !text-xs !text-gray-500"
        >
          <MenuItem value={"Default"} className="!text-gray-500 !text-xs">
            Default
          </MenuItem>
          {options.map((opt: { label: string; value: string }) => (
            <MenuItem
              key={opt.value}
              value={opt.value}
              className="!text-gray-500 !text-xs flex justify-between items-center"
              sx={{ justifyContent: "space-between" }}
            >
              <span>{opt.label}</span>
              <span
                className="ml-2 cursor-pointer "
                title="Edit View"
                onClick={() => {
                  // e.stopPropagation();
                  handleEditView(opt.value);
                }}
              >
                <EditIcon sx={{ fontSize: "15px" }} />
              </span>
            </MenuItem>
          ))}

          {/* {!isDuplicateView && ( */}
          <MenuItem
            value="__add_new_view__"
            className="!font-bold !text-gray-700 !text-xs"
          >
            + Add View
          </MenuItem>
          {/* )} */}
        </Select>
        <button
          className="flex gap-2.5 items-center px-4 py-2 bg-violet-50 rounded cursor-pointer"
          onClick={() => {
            setShowColumnSelector((prev) => !prev);
            setShow(false);
            setIsEditing(false);
            setEditingViewId(null);
            setInput("");
          }}
        >
          <Image src={settings} alt="settings" />
          <span className="text-xs font-medium !text-gray-500">Columns</span>
        </button>
        <Pagination
          page={page}
          totalPages={totalPages}
          pageSize={pageSize}
          onPageChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
        />
      </div>

      {showColumnSelector && (
        <div
          ref={selectorRef}
          className="absolute right-[90px] top-[340px] z-50 bg-white rounded shadow-lg p-4 w-[300px] max-h-[500px] "
        >
          {show ? (
            <div className="!mb-4">
              <label className="block text-sm font-medium text-gray-700">
                View Name
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="text"
                  placeholder=""
                  className="px-4 py-2 border border-gray-300 rounded flex-1"
                  value={input}
                  onChange={(e) => {
                    setInput(e.target.value);
                  }}
                />
                {isEditing && (
                  <button
                    type="button"
                    className="ml-2 text-red-500"
                    title="Delete View"
                    onClick={async () => {
                      if (!editingViewId) return;
                      setLoader(true);
                      try {
                        await deleteView({ id: editingViewId });
                        setShow(false);
                        setShowColumnSelector(false);
                        setIsEditing(false);
                        setEditingViewId(null);
                        setInput("");
                        await getViewAll();
                        showToast.success("View deleted successfully");
                      } catch (err: any) {
                        showToast.error(
                          err?.message || "Failed to delete view"
                        );
                      }
                      setLoader(false);
                    }}
                  >
                    <DeleteIcon />
                  </button>
                )}
              </div>
              <div className="flex justify-between mt-4">
                <button
                  className="px-4 py-2 rounded bg-gray-400 text-white"
                  onClick={() => {
                    setShowColumnSelector(false);
                    setShow(false);
                    setInput("");
                    setIsEditing(false);
                    setEditingViewId(null);
                  }}
                >
                  Cancel
                </button>
                <button
                  className="px-4 py-2 rounded bg-teal-500 text-white"
                  onClick={() => handleSave()}
                  disabled={!input || input.trim() === ""}
                >
                  {isEditing ? "Update" : "Create"}
                </button>
              </div>
            </div>
          ) : (
            <>
              <div className="flex justify-between items-center mb-4">
                <p className="text-sm text-gray-500">
                  {visibleCount} out of {totalCount} Selected
                </p>
                <button className="text-gray-700" onClick={handleReset}>
                  Reset
                </button>
              </div>
                            <div className="flex items-center mb-2">
                <input
                  type="checkbox"
                  checked={visibleCount === totalCount}
                  ref={(el) => {
                    if (el) {
                      el.indeterminate =
                        visibleCount > 0 && visibleCount < totalCount;
                    }
                  }}
                  onChange={(e) => {
                    const checked = e.target.checked;
                    setUpdatedColumns(
                      updatedColumns.map((col) => ({
                        ...col,
                        visible: checked,
                      }))
                    );
                  }}
                  className="mr-2"
                  id="select-all-columns"
                />
                <label
                  htmlFor="select-all-columns"
                  className="text-sm text-gray-700 font-medium"
                >
                  Select All
                </label>
              </div>
              {/* --- End Select All Checkbox --- */}
              <hr className="border border-gray-100" />

              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="columns">
                  {(provided: any) => (
                    <div
                      {...provided.droppableProps}
                      ref={provided.innerRef}
                      className="flex flex-col gap-2 my-4 overflow-y-auto max-h-[300px] overflow-y-auto"
                    >
                      {updatedColumns.map((col, i) => (
                        <Draggable
                          key={col.title}
                          draggableId={col.title}
                          index={i}
                        >
                          {(provided: any) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              // Remove dragHandleProps from the container
                              className={`px-3 py-2 rounded text-sm flex items-center justify-between ${
                                col.visible
                                  ? "bg-teal-50 text-gray-500"
                                  : "border border-gray-300 text-gray-600"
                              }`}
                            >
                              <div className="flex items-center">
                                {/* Drag handle icon */}
                                <span
                                  {...provided.dragHandleProps}
                                  className="cursor-grab mr-2 flex items-center"
                                  title="Drag to reorder"
                                >
                                  <svg
                                    width="18"
                                    height="18"
                                    viewBox="0 0 20 20"
                                    fill="none"
                                  >
                                    <rect
                                      x="3"
                                      y="6"
                                      width="14"
                                      height="2"
                                      rx="1"
                                      fill="#6B7280"
                                    />
                                    <rect
                                      x="3"
                                      y="9"
                                      width="14"
                                      height="2"
                                      rx="1"
                                      fill="#6B7280"
                                    />
                                    <rect
                                      x="3"
                                      y="12"
                                      width="14"
                                      height="2"
                                      rx="1"
                                      fill="#6B7280"
                                    />
                                  </svg>
                                </span>
                                {toNormalCase(col.title)}
                              </div>
                              <input
                                type="checkbox"
                                checked={col.visible}
                                onChange={() => toggleColumnVisibility(i)}
                                className="ml-2"
                                onClick={(e) => e.stopPropagation()}
                              />
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>

              <div className="flex justify-between">
                <button
                  className="px-4 py-2 rounded bg-gray-400 text-white"
                  onClick={() => {
                    setShowColumnSelector(false);
                  }}
                >
                  Cancel
                </button>
                <button
                  className="px-4 py-2 rounded bg-teal-500 text-white"
                  onClick={() => handleSubmit()}
                >
                  Save & Close
                </button>
              </div>
            </>
          )}
        </div>
      )}
      {loader && <Loader />}
    </header>
  );
}
