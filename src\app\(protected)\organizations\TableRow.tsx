"use client";

import * as React from "react";
import { ImageView } from "./imageView";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { getSubModulePermission } from "@/utils/generic";
import { Edit, Eye, Trash } from "lucide-react";

interface TableRowProps {
  client: Record<string, string>;
  onToggleSelect?: (id: string) => void;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  handleCheck: (id: string) => void;
  visibleColumns?: string[];
  columnName: string[];
  showSelect?: boolean;
  title: string;
}

export function TableRow({
  client,
  title,
  onEdit,
  onDelete,
  handleCheck,
  columnName,
  showSelect = true,
}: TableRowProps) {
  const exportId = useSelector((state: RootState) => state.exportId.exportId);
  console.log(`  title == "Organizations"
              ? "Main Organization"
              : title == "Sub Organization Users"
                ? "Sub Organization Settings"
                : title == "Main Organization Users"
                  ? "Main Organization Settings"
                  : "Sub Organization"`,  title == "Organizations"
              ? "Main Organization"
              : title == "Sub Organization Users"
                ? "Sub Organization Settings"
                : title == "Organization Users"
                  ? "Main Organization Settings"
                  : "Sub Organization");
  
  const editIcon = (
    <div
      style={{
        backgroundColor: "#5277F7",
        borderRadius: "6px",
        padding: "4px",
        display: "inline-flex",
      }}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={2}
        stroke="#FFFFFF" // white stroke
        className="edit-icon"
        style={{ cursor: "pointer" }}
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M16.862 3.487a2.166 2.166 0 113.06 3.06L7.5 19.97l-4.25.707.708-4.248L16.862 3.487z"
        />
      </svg>
    </div>
  );

  const deleteIcon = (
    <div
      style={{
        backgroundColor: "#FD5475", // red background
        borderRadius: "6px",
        padding: "4px",
        display: "inline-flex",
      }}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={2}
        stroke="#FFFFFF" // white stroke
        className="delete-icon"
        style={{ cursor: "pointer" }}
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M6 7h12M9 7V5a1 1 0 011-1h4a1 1 0 011 1v2m2 0v12a2 2 0 01-2 2H8a2 2 0 01-2-2V7h12z"
        />
      </svg>
    </div>
  );

  return (
    <tr className="table-row hover:bg-gray-50 transition-colors duration-150">
      {showSelect && (
        <td className="sticky left-0 z-10 p-2.5 pl-8 align-middle bg-white border-r border-b border-solid !border-b-slate-100 !border-r-slate-100 w-[84px] max-sm:px-1.5 max-sm:py-2 max-sm:pl-4">
          <input
            type="checkbox"
            checked={exportId.includes(client._id)}
            onChange={() => handleCheck(client._id)}
            className="w-4 h-4 rounded border border-solid cursor-pointer border-zinc-200"
          />
        </td>
      )}

      {columnName?.map((item, i) => (
        // visibleColumns.includes(`${item}`) &&
        <td
          key={i}
          className="text-sm tracking-tight leading-6 text-gray-700 align-middle bg-white border-r border-b border-solid !border-b-slate-100 !border-r-slate-100 max-sm:px-1.5 max-sm:py-2 max-sm:text-xs"
        >
          {item.toLocaleLowerCase().includes("image") ? (
            <div className="flex justify-center items-center">
              <ImageView filename={client[item]} type="grid" />
            </div>
          ) : item === "actions" ? (
            <div className="flex gap-2.5 items-center opacity-0 group-hover:opacity-100 transition-opacity">
              <button
                onClick={() => onEdit?.(client._id)}
                className="p-2 rounded hover:bg-blue-50 transition-colors"
              >
                {editIcon}
              </button>
              <button
                onClick={() => onDelete?.(client._id)}
                aria-label={`Delete ${client.businessName}`}
                className="p-2 rounded hover:bg-red-50 transition-colors"
              >
                {deleteIcon}
              </button>
            </div>
          ) : (
            <div className="p-2 text-sm truncate max-w-[200px]">
              {client[item]}
            </div>
          )}
        </td>
      ))}
      <td className="sticky right-0 z-10 align-middle bg-white border-r border-b border-solid !border-b-slate-100 !border-r-slate-100 w-[84px] max-sm:px-1.5 max-sm:py-2 max-sm:pl-4">
        <div className="flex gap-2.5 items-center justify-center ">
          {(title == "Sub Organization Users"
            ? getSubModulePermission(
                "Sub Organization Settings",
                "Update Users"
              )?.isEnabled
            : title == "Organization Users"
              ? getSubModulePermission(
                  "Main Organization Settings",
                  "Update Users"
                )?.isEnabled
              : true) && (
            <button
              onClick={() => onEdit?.(client._id)}
              className="p-1 text-blue-600 hover:text-blue-800"
            >
              {getSubModulePermission(
                title == "Organizations"
                  ? "Main Organization"
                  : title == "Sub Organization Users"
                    ? "Sub Organization Settings"
                    : title == "Organization Users"
                      ? "Main Organization Settings"
                      : "Sub Organization",
                title == "Sub Organization Users" ||
                  title == "Organization Users"
                  ? "Update Users"
                  : "Update"
              )?.isEnabled ? (
                <Edit className="w-4 h-4" />
              ) : (
                <Eye className="w-4 h-4" />
              )}
            </button>
          )}
          {getSubModulePermission(
            title == "Organizations"
              ? "Main Organization"
              : title == "Sub Organization Users"
                ? "Sub Organization Settings"
                : title == "Organization Users"
                  ? "Main Organization Settings"
                  : "Sub Organization",
            title == "Sub Organization Users" || title == "Organization Users"
              ? "Remove Users"
              : "Delete"
          )?.isEnabled && (
            <button
              onClick={() => onDelete?.(client._id)}
              aria-label={`Delete ${client.businessName}`}
              className="p-1 text-red-600 hover:text-red-800"
            >
              <Trash className="w-4 h-4" />
            </button>
          )}
        </div>
      </td>
    </tr>
  );
}
