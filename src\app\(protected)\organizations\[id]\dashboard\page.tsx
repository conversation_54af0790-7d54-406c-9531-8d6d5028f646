'use client';
import { PermissionGuard } from '@/utils/permissions'
import { useSelector } from 'react-redux'
import { RootState } from '@/store'

export default function EditClientPage() {
  const headerProcess = useSelector((state: RootState) => state.headerProcess);

  return (
    <PermissionGuard
      moduleName="Organizations"
      subModuleName="Main Organization"
      permissionName="View"
      permissions={headerProcess?.role?.permissions || []}
    >
      <div>
        <h1>Dashboard Client</h1>
        {/* Form to edit client */}
      </div>
    </PermissionGuard>
  );
}
  