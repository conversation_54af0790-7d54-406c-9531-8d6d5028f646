// import { useRouter } from 'next/navigation';
'use client';
import React, { useState } from "react";
import CreateClientPage from "../../Form";
import { getTemplates } from "@/api/templates/templates";
import { Button } from "@mui/material";
import { getSubModulePermissionCommon } from "@/utils/generic";
// import { Form as ClientForm } from "@/types/clientForm";
  
  export default function EditClientPage() {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const [form, setForm] = useState({})
      const [templateId, setTemplateId] = useState('')
      const [flattedValues, setFlattedValues] = useState([]);
    React.useEffect(() => {
      getTemplates({ search: "", filters:{key:"organization",type:'Master',isActive:true}}).then((res) => {
        const template = res.templates.data.templates[0];
        console.log('template', template);
        setFlattedValues(res.templates.data.templates[0]?.view_summary?.inGrid);
        if (template && template.fields) {
          const fieldsData = template.fields;
          if (typeof fieldsData === 'string') {
            try {
              const unescaped = fieldsData.replace(/^"|"$/g, '').replace(/\\"/g, '"');
              console.log('unescaped', unescaped);
  
              // Step 2: Parse it
              const parsedJSON = JSON.parse(unescaped)[0];
  
              console.log('parsedJSON', parsedJSON);
              // console.log('fieldsData',JSON.parse(JSON.parse(fieldsData)));
  
              // const parsedFields = JSON.parse(fieldsData);
              setTemplateId(template._id);
              setForm(parsedJSON);
            } catch (error) {
              console.error("Error parsing JSON:", error);
            }
          } else {
            setTemplateId(template._id);
            setForm(fieldsData);
          }
        } else {
          console.warn("Template or fields property is missing.");
        }
      }).catch((err) => {
        console.error(err);
      })
    }, [])

        const handleBack = () => {
      if(typeof window !== 'undefined')
      {window.history.back();}
    }

    const getPermission=()=>{
      const permission =getSubModulePermissionCommon('Organizations','Main Organization', 'Update')?.isEnabled??false
      return permission
    }
    return (
      // <PermissionGuard
      //   moduleName="Organizations"
      //   subModuleName="Main Organization"
      //   permissionName="Update"
      //   permissions={headerProcess?.role?.permissions || []}
      // >
        <div className='px-6'>
<div
                    className="text-sm font-200 text-gray-700  pl-4 m-0 rounded flex items-end !justify-end w-full"
                  >
                   <Button className="font-normal !h-[40px] !mt-0 text-[12px] !w-[75px] !shadow-none" onClick={handleBack}>Back</Button>
                  </div>
          <CreateClientPage formTemplate={form} type="edit"  clientTyoe={""} templateId={templateId} flattedValues={flattedValues}  access={getPermission()}/>
        </div>
      // </PermissionGuard>
    );
  }
  