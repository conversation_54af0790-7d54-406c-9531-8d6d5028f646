'use client';
import { useEffect, useMemo, useState } from 'react';
import Link from 'next/link';
import { usePathname, useParams } from 'next/navigation';
import {
  LayoutDashboard,
  Info,
  List,
  ChevronsLeft,
  ChevronsRight,
  Settings,
  Shield,
  Hospital,
} from 'lucide-react';
import { setSideNav } from '@/features/client/clientSlice';
import { useDispatch } from 'react-redux';
import { Sync } from '@mui/icons-material';
import { getOrganizationSubModules, getSubModulePermission } from '@/utils/generic';


export default function ClientsLayout({ children }: { children: React.ReactNode }) {
  const [collapsed, setCollapsed] = useState(false);
  const pathname = usePathname();
  const params = useParams();
  const id = params?.id;
  const dispatch = useDispatch();
  
  useEffect(()=>{
    dispatch(setSideNav(collapsed));
  },[collapsed])

  console.log("getOrganizationSubModules('Organization')",getOrganizationSubModules('Organizations').filter((item)=>item.moduleName=='Main Organization'));
  
  const isSubOrg = pathname.includes('/subOrganization/dashboard') || pathname.includes('/subOrganization/info') || pathname.includes('/subOrganization/list')
   || pathname.includes('/subOrganization/settings') || pathname.includes('/subOrganization/process-settings') || pathname.includes('/subOrganization/provider') || pathname.includes('/subOrganization/payer-groups');

  const links = useMemo(() => {
    if (isSubOrg) {
      return [
        { href: `/organizations/${id}/subOrganization/dashboard`, text: 'Sub Org Dashboard', icon: <LayoutDashboard size={20} />, isView:true },
        { href: `/organizations/${id}/subOrganization/info`, text: 'Sub Org Info', icon: <Info size={20} />, isView:getSubModulePermission('Sub Organization',"Update")?.isEnabled },
        { href: `/organizations/${id}/subOrganization/list`, text: 'CPT Fee List', icon: <List size={20} />, isView:true },
        { href: `/organizations/${id}/subOrganization/process-settings`, text: 'Process Settings', icon: <Sync/>, isView:getOrganizationSubModules('Organizations').filter((item)=>item.moduleName=='Process Settings')?.[0]?.isEnabled },
        { href: `/organizations/${id}/subOrganization/provider`, text: 'Provider', icon: <Hospital size={20} />, isView:getOrganizationSubModules('Organizations').filter((item)=>item.moduleName=='Provider')?.[0]?.isEnabled },
        { href: `/organizations/${id}/subOrganization/payer-groups`, text: 'Payor Groups', icon: <Shield size={20} />, isView:true },
        { href: `/organizations/${id}/subOrganization/settings`, text: 'Settings', icon: <Settings size={20} />, isView:getOrganizationSubModules('Organizations').filter((item)=>item.moduleName=='Sub Organization Settings')?.[0]?.isEnabled },
      ];
    }

    return [
      { href: `/organizations/${id}/dashboard`, text: 'Organization Dashboard', icon: <LayoutDashboard size={20} />, isView:true },
      { href: `/organizations/${id}/info`, text: 'Organization Info', icon: <Info size={20} />, isView:getSubModulePermission('Main Organization',"Update")?.isEnabled },
      { href: `/organizations/${id}/subOrganization`, text: 'Sub Organization List', icon: <List size={20} />, isView:getOrganizationSubModules('Organizations').filter((item)=>item.moduleName=='Sub Organization')?.[0]?.isEnabled },
      { href: `/organizations/${id}/settings`, text: 'Settings', icon: <Settings size={20} />, isView:getOrganizationSubModules('Organizations').filter((item)=>item.moduleName=='Main Organization Settings')?.[0]?.isEnabled },
    ];
  }, [id, isSubOrg]);

  return (
    <div className="flex" style={{ height: "calc(100vh - 191px)" }}>
      <aside
        className={`bg-[#F8F8F9] transition-all duration-300 ${
          collapsed ? 'w-20' : 'w-64'
        }`}
      >
        {/* Toggle Collapse Button */}
        <button
          onClick={() => setCollapsed(!collapsed)}
          className="flex items-center justify-end w-full py-4 pr-3"
        >
          {collapsed ? <ChevronsRight size={20} /> : <ChevronsLeft size={20} />}
        </button>

        {/* Navigation Links */}
        <nav className="flex flex-col">
          {links.map(({ href, text, icon }) => {
            const isActive = pathname.startsWith(href);
            // if(isView)
            return (
              // getSubModulePermission(text=='Organizations'?"Main Organization":"Sub Organization",'Delete')?.isEnabled
               <Link
                key={href}
                href={href}
                className={`flex items-center px-4 py-3 text-sm font-medium ${
                  isActive
                    ? 'text-blue-600 font-semibold bg-blue-50 border-l-4 active:border-[#1465ab] bg-white'
                    : 'text-gray-700 hover:bg-white hover:border-l-4 hover:border-[#1465ab] active:bg-white active:border-l-4 active:border-[#1465ab]'
                }`}
              >
                <span className="mr-3">{icon}</span>
                {!collapsed && <span>{text}</span>}
              </Link>
            );
          })}
        </nav>
      </aside>

      {/* Main Content */}
      <main className="flex-1 overflow-scroll p-4 bg-gray-100 !w-[50%]">{children}</main>
    </div>
  );
}
