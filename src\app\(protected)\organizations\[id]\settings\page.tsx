/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';
import React, { useEffect, useState } from 'react';
import {
  RadioGroup,
  FormControlLabel,
  Radio,
  FormLabel,
  Button,
} from '@mui/material';
// import { Delete, Edit, Add, AccessTime } from '@mui/icons-material';
import {  getOrgSettings, getOrgUsers, updateOrgSettings } from '@/api/organizations/organizations';
import { setHeaders, setHeadersDefault } from '@/features/headers/headersSlice';
import { convertFormJsonFromClients, getSubModulePermission, transformedClients } from '@/utils/generic';
import { setClientTable } from '@/features/client/clientSlice';
import { TableData } from '@/types/user';
import ClientDataTable from '../../ClientDataTable';
import { useDispatch } from 'react-redux';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import  dayjs, { Dayjs } from 'dayjs';
import { useParams } from 'next/navigation';
import Loader from '@/components/loader/loader';
// import { access } from 'fs';
import { showToast } from '@/components/toaster/ToastProvider';

const OrganizationSettings = () => {
  const [communication, setCommunication] = useState('email');
  const [portalAccess, setPortalAccess] = useState('yes');
  const [orgAccess, setOrgAccess] = useState('yes');
  const [expiryDate, setExpiryDate] = useState<Dayjs | null>(null);
  const params = useParams();
    const id = params.id;
  // const [users] = useState([
  //   { name: 'Albert Einstein', email: '<EMAIL>', date: '06–23–2025', role: 'R1', status: 'Active' },
  //   { name: 'Issac Newton', email: '<EMAIL>', date: '06–23–2025', role: 'R2', status: 'Inactive' },
  // ]);
  const dispatch = useDispatch();
  const [pagination, setPagination] = React.useState(1);
  const [loader, setLoader] = React.useState(false);
  const getOrganisationId = (id: string | string[] | undefined): string | undefined => {
  if (Array.isArray(id)) return id[0];
  return id;
};


  const [query, setQuery] = React.useState({
    search: '',
    // filters:{},
    sortBy: '',
    sortOrder: 'asc',
    page: 1,
    limit: 10,
    organisationId:getOrganisationId(id)
  });

  React.useEffect(() => {
    handleGetApi()
    console.log('each');
  }, [query]);

  useEffect(()=>{
    if(id){
      getOrgSettings({id:getOrganisationId(id) ?? ''}).then((res) => {
        console.log(res, 'getOrgSettings');
        setCommunication(res?.organisationSettingsByOrganisationId?.preferredCommunication === `EMAIL` ? `email` : `ticket`);
        setPortalAccess(res?.organisationSettingsByOrganisationId?.accessPortal === true ? `yes` : `no`);
        setOrgAccess(res?.organisationSettingsByOrganisationId?.accessOrganisation === true ? `yes` : `no` );
        setExpiryDate(dayjs(res?.organisationSettingsByOrganisationId?.expiryTime))
            setLoader(false)
      })
      .catch((err) => {
        setLoader(false)
        console.error(err);
      });
    }
    
  },[])

  


  const handleGetApi =async()=>{
    setLoader(true)
    
      // const result = Object.fromEntries(res.templates.data.templates[0]?.view_summary?.inGrid.map((key: any) => [key, 1]));
       const titles = ["name", "email", "createdAt","isActive"];
              dispatch(setHeaders(titles))
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              dispatch(setHeadersDefault(titles as any))
     await getOrgUsers(query)
        .then((res) => {
          console.log(res?.organisationUsers, 'res.getUsersWithPagination.users');
          const data = transformedClients(res?.organisationUsers?.items);
          const tableData = convertFormJsonFromClients(data);
              dispatch(setClientTable(tableData as TableData));
              setPagination(res?.organisationUsers?.pagination)
              setLoader(false)
         
        })
        
        .catch((err) => {
          setLoader(false)
          console.error(err);
        });
  };

  const handleSubmit = ( ) =>{
    const payload = {
      input: {
           id:getOrganisationId(id) ?? '',
            preferredCommunication: communication === `email` ? `EMAIL` : `TICKET`,
            accessPortal: portalAccess === 'yes' ? true : false,
            accessOrganisation: orgAccess === 'yes' ? true : false,
            expiryTime: expiryDate ? expiryDate.toDate() : new Date(),
      },
    };
    updateOrgSettings(payload).then((res) => {
            showToast.success(res?.code);
            // onClose()
            // setRoleName('')
            // setName('')
            // setEmpId('')
            // setEmail('')
            setLoader(false)
          })
          .catch((err) => {
            showToast.error(err.message);
            console.error(err);
            setLoader(false)
          });
  }

  return (
    <>
      {loader && <Loader/>}
      <div className="bg-white rounded-lg shadow-sm">
      {/* Settings Header */}
      {getSubModulePermission("Main Organization Settings",'Customize Settings')?.isEnabled &&
             <> <h2 className="text-lg font-semibold text-[#2B3674] p-6">Settings</h2>

      {/* Preferred Communication */}
      <div className="mb-6 px-6">
        <FormLabel className="text-[#2B3674] font-bold mb-2 block">Preferred Communication</FormLabel>
        <RadioGroup row value={communication} onChange={(e) => setCommunication(e.target.value)}>
          <FormControlLabel value="email" control={<Radio />} label="Email" />
          <FormControlLabel value="ticket" control={<Radio />} label="Ticket" />
        </RadioGroup>
      </div>
</>}
      {/* Organization Login Portal */}
       {getSubModulePermission("Main Organization Settings",'Customize Settings')?.isEnabled &&
              <>
      <h2 className="bg-[#f5f9ff] font-bold text-[#2B3674] mb-4 p-6">Organization Login Portal</h2>
      <div className="mb-6 rounded-md px-6">
        <FormLabel className="text-[#2B3674] font-bold mb-2 block">Access to Organization Portal</FormLabel>
        <RadioGroup row value={portalAccess} onChange={(e) => setPortalAccess(e.target.value)}>
          <FormControlLabel value="yes" control={<Radio />} label="Yes" />
          <FormControlLabel value="no" control={<Radio />} label="No" />
        </RadioGroup>
      </div></>}
      {/* Organization Users Table */}
      <ClientDataTable title={"Organization Users"} handleGetApi={handleGetApi} setQuery={setQuery} query={query} pagination={pagination} />

      {/* Access Rights */}
      {getSubModulePermission("Main Organization Settings",'Customize Settings')?.isEnabled &&
            <> 
      <h2 className="bg-[#f5f9ff] font-bold text-[#2B3674] mt-3 p-6">Access Rights</h2>
      <div className="rounded-md p-6">
      <div className="flex items-end space-x-6">
        {/* Organization Access */}
        <div>
        <FormLabel className="text-[#2B3674] font-bold mb-2 block">Organization Profile Edit Access</FormLabel>
          <RadioGroup
            row
            value={orgAccess}
            onChange={(e) => setOrgAccess(e.target.value)}
          >
            <FormControlLabel value="yes" control={<Radio />} label="Yes" />
            <FormControlLabel value="no" control={<Radio />} label="No" />
          </RadioGroup>
        </div>

        {/* Expiry Date */}
        <div className="w-64">
        <FormLabel className="text-[#2B3674] font-bold mb-2 block">Expiry Time</FormLabel>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
            disabled={orgAccess === `no`}
              value={expiryDate}
              onChange={(newValue) => setExpiryDate(newValue)}
              slotProps={{ textField: { size: 'small', fullWidth: true } }}
            />
          </LocalizationProvider>
        </div>

        {/* Submit Button */}
        <div className="pt-5">
          <Button
            variant="contained"
            color="primary"
            size="medium"
            sx={{ minWidth: '100px' }}
            onClick={handleSubmit}
          >
            Submit
          </Button>
        </div>
      </div>
      </div>
      </>
}
      </div>
 </>
  );
};

export default OrganizationSettings;
