'use client'

import CreateClientPage from "../../../Form";
import { getTemplates } from "@/api/templates/templates";
import Loader from "@/components/loader/loader";
import { Button } from "@mui/material";
import React, { useState } from "react";
import { getSubModulePermissionCommon } from "@/utils/generic";
  
  export default function EditClientPage() {
    const [form, setForm] = useState({})
    const [templateId, setTemplateId] = useState('')
    const [loader,setLoader] = React.useState(false)
     const [flattedValues, setFlattedValues] = useState([]);
    React.useEffect(() => {
      setLoader(true)
      getTemplates({ search: "", filters:{key:"sub-organization", type:'Master',isActive:true}}).then((res) => {
               const template = res.templates.data.templates[0];
        console.log('template', template);
        setFlattedValues(res.templates.data.templates[0]?.view_summary?.inGrid);
        if (template && template.fields) {
          const fieldsData = template.fields;
          if (typeof fieldsData === 'string') {
            try {           
              const unescaped = fieldsData.replace(/^"|"$/g, '').replace(/\\"/g, '"');
              console.log('unescaped', unescaped);
  
              // Step 2: Parse it
              const parsedJSON = JSON.parse(unescaped)[0];
  
              console.log('parsedJSON', parsedJSON);
              // console.log('fieldsData',JSON.parse(JSON.parse(fieldsData)));
  
              // const parsedFields = JSON.parse(fieldsData);
              setTemplateId(template._id);
              setForm(parsedJSON);
            } catch (error) {
              console.error("Error parsing JSON:", error);
            }
          } else {
            setTemplateId(template._id);
            setForm(fieldsData);
          }
        } else {
          console.warn("Template or fields property is missing.");
        }
        setLoader(false)
      }).catch((err) => {
         setLoader(false)
        console.error(err);
      })
    }, [])
        const handleBack = () => {
      if(typeof window !== 'undefined')
      {window.history.back();}
    }
        const getPermission=()=>{
          const permission =getSubModulePermissionCommon('Organizations','Sub Organization', 'Update')?.isEnabled??false
          return permission
        }

    return (
      <div className='px-6'>
              {loader&&<Loader /> }
               
<div
                    className="text-sm font-200 text-gray-700  pl-4 m-0 rounded flex items-end !justify-end w-full"
                  >
                   <Button className="font-normal !h-[40px] !mt-0 text-[12px] !w-[75px] !shadow-none" onClick={handleBack}>Back</Button>
                  </div>
                <CreateClientPage formTemplate={form} type="edit" clientTyoe={'SUB_CLIENT'} templateId={templateId} flattedValues={flattedValues} access={getPermission()}/>
            </div>
    );
  }
  