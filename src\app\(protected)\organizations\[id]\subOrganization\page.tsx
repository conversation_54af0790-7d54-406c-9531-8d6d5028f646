/* eslint-disable @typescript-eslint/no-explicit-any */
// import { useRouter } from 'next/navigation';
'use client';
import { setClientTable } from "@/features/client/clientSlice";
import React from "react";
import { useDispatch } from "react-redux";
import ClientDataTable from "../../ClientDataTable";
import { TableData } from "@/types/user";
// import { getSubOrganizationUser } from "@/api/organizations/subOrganization";
import { useParams } from "next/navigation";
import { convertFormJsonFromClients, transformedClients } from "@/utils/generic";
import { getOrganizationUser } from "@/api/organizations/organizations";
import Loader from "@/components/loader/loader";
import { getTemplates } from "@/api/templates/templates";
import { setHeaders, setHeadersDefault } from "@/features/headers/headersSlice";

  export default function Page() {
    const { id } = useParams();
  const dispatch = useDispatch();
  const [loader,setLoader] = React.useState(false)
  const [query, setQuery] = React.useState({
              search: '',
              filters: {main_client:id},
              sortBy: '',
              sortOrder: 'asc',
              page: 1,
              limit: 10,
    });
  const [pagination, setPagination] = React.useState(1);

  React.useEffect(() => {
   handleGetApi()
   console.log('each');
  }, [query]);
  
  const handleGetApi =(view?:any)=>{
    getTemplates({ search: "", filters:{key:"sub-organization", type:'Master',isActive:true}}).then((res) => {
      const template = res.templates.data.templates[0];
      console.log('template', template);
       if(view==undefined){
       dispatch(setHeaders(res.templates.data.templates[0]?.view_summary?.inGrid))
            dispatch(setHeadersDefault(res.templates.data.templates[0]?.view_summary?.default))
       }
            const result = Object.fromEntries(res.templates.data.templates[0]?.view_summary?.inGrid.map((key: any) => [key, 1]));
      const payload = {
        input: { ...query, ['selectedFields']: result ,orgType:"SUB_CLIENT"},
      };  
      setLoader(true)
      getOrganizationUser(payload)
        .then((res) => {
          console.log(res.getUsersWithPagination.users, 'res.getUsersWithPagination.users');

          const data = transformedClients(res.getUsersWithPagination.users);
          const tableData = convertFormJsonFromClients(data);
          console.log(tableData);
          console.log('data', data);

          dispatch(setClientTable(tableData as TableData));
          setPagination(res.getUsersWithPagination.pagination)
          setLoader(false)
        })
        .catch((err) => {
          console.error(err);
          setLoader(false)
        });
    })
  };


  return (
    // <PermissionGuard
    //   moduleName="Organizations"
    //   subModuleName="Sub Organization"
    //   permissionName="View"
    //   permissions={headerProcess?.role?.permissions || []}
    // >
    <>
      {loader && <Loader/>}
      <ClientDataTable
        title={"Sub Organizations"}
        handleGetApi={handleGetApi}
        pagination={pagination}
        setQuery={setQuery}
        query={query}
      /></>
    // </PermissionGuard>
  )
}

  