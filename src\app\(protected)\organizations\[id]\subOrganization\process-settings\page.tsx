/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import React, { useEffect, useState } from 'react';
import {
  Table, TableBody, TableCell, TableContainer,
  TableHead, TableRow, Paper, IconButton, TextField,
  Typography, Box, Tabs, Tab, Button,
  Icon,
  Switch,
  Autocomplete,
  CircularProgress
} from '@mui/material';
import { Delete, Save } from '@mui/icons-material';
import Image from 'next/image';
import addIcon from '../../../../../../assests/AddIcon.png';
import { deleteCreate, getAllProcessSettings, getOrgProcess, operationCreate, updateProcessSettings } from '@/api/process/process';
import { RootState } from '@/store';
import { useDispatch, useSelector } from 'react-redux';
import {
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { getSystemUsers } from '@/api/user-management/systemUsers/systemUsers';
import Loader from '@/components/loader/loader';
import { showToast } from '@/components/toaster/ToastProvider';
import DeleteModal from '@/components/deleteModel';
import { useParams } from 'next/navigation';
import { getByOrganization } from '@/api/organizations/organizations';
import { setOrgList } from '@/features/client/clientSlice';
import { selectHeaders } from '@/api/header/header';
import Cookies from "js-cookie";
import { PermissionGuard, PermissionWrapper } from '@/utils/permissions';
import { getSubModulePermission } from '@/utils/generic';
import DeleteModals from '@/app/[components]/deleteModel';
type TableData = { [roleName: string]: string[] };
type EditState = { [index: number]: boolean };

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function CustomTabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
      className='!p-0'
    >
      {value === index && <Box >{children}</Box>}
    </div>
  );
}
function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

export default function Page() {
  const [value, setValue] = useState(0);
  const [process, setProcess] = useState<any[]>([]);
  // const processDetail = useSelector((state: RootState) => state.process);
  const [processDetail, setProcessDetail]=useState({
    organizationID:"",
    subOrganizationId:"",
    processId:""
  })
  console.log('processDetail',processDetail);
  const dispatch =useDispatch()
   const user = useSelector((state: RootState) => state.user);
   const headerProcess: any = useSelector((state: RootState) => state.headerProcess);
  const [openDelete, setOpenDelete] = React.useState(false);
  const [turnOff, setTurnOff] = React.useState(false);
  const [deleteData, setDeleteData] = useState<any>([]);
  const [deleteTitle, setDeleteTitle] = useState('');
  const [deleteIndex, setDeleteIndex] = useState<any>(null);
  const [operationData, setOperationData] = useState<TableData>({});
  const [loader, setLoader] = useState(true)
  const [isAdded, setIsAdded] = useState(false)
  const [auditData, setAuditData] = useState<TableData>({});
  const [managementData, setManagementData] = useState<TableData>({});
  const [allUsers, setAllUsers] = useState([])
const [ids, setId]=useState('')
  const [operationEdit, setOperationEdit] = useState<EditState>({});
  const [auditEdit, setAuditEdit] = useState<EditState>({});
  const [managementEdit, setManagementEdit] = useState<EditState>({});
 const [inputValue, setInputValue] = useState('');
  const [userOptions, setUserOptions] = useState([]);
const [loading, setLoading] = useState(false);
  const [operationValue, setOprationValue] = useState<any>({})
  const [orgId, setOrgId]=useState('')
  const [actions, setAction]=useState('')
  const [query, setQuery] = React.useState({
    search: '',
    page:1,
    limit:100
  });
  console.log('operationData', operationData);
  const {id} =useParams()
  console.log('id---',id);
  
  useEffect(() => {
    fetchData();
  }, []);
  const handleTurnOff=()=>{
  setTurnOff(false)
  }

  const submitTurnOff =()=>{
    setLoader(true)
    updateProcessSettings({id:ids,isActive:false}).then(async () => {
      await fetchData()
       setTurnOff(false)
    }).catch((err) => {
       setLoader(false)
      console.error(err);
    })
  }
    const submitTurnOn =()=>{
       setLoader(true)
    updateProcessSettings({id:ids,isActive:true}).then(async () => {
      await fetchData()
       setTurnOff(false)
    }).catch((err) => {
       setLoader(false)
      console.error(err);
    })
  }

//   useEffect(() => {
//     console.log('inputValue',inputValue);
    
//   const delayDebounceFn = setTimeout(() => {
//     getSystemUser({search:inputValue});
//   }, 1000); // debounce 500ms

//   return () => clearTimeout(delayDebounceFn);
// }, [inputValue]);

  const fetchData = async () => {
    try {
        // await getProcess();
      await getByOrganization({ clientId: id })
            .then(async(ress) =>{
            
              // setProcessDetail({...processDetail,['organizationID']:ress.getByClient.data.client.main_client,['subOrganizationId']:ress.getByClient.data.client._id}) 
      setOrgId(ress.getByClient.data.client.orgId)
            const payloads = {
      organisationId: ress.getByClient.data.client.main_client,
        subOrganisationId: ress.getByClient.data.client._id
    }
  await  getOrgProcess(payloads)
      .then(async(response) => {
        
        setProcessDetail({...processDetail,['processId']:response.organisationProcessMappings?.[0]?.processId,['organizationID']:ress.getByClient.data.client.main_client,['subOrganizationId']:ress.getByClient.data.client._id})
        setProcess(response.organisationProcessMappings)
        // Cookies.set("orgId", ress.getByClient.data.client.main_client, { expires: 1 });
        // Cookies.set("subOrgId", ress.getByClient.data.client._id, { expires: 1 });
        // Cookies.set("processId", response.organisationProcessMappings?.[0]?.processId, { expires: 1 });
  console.log(response.organisationProcessMappings,'response.organisationProcessMappings');
      const payload = {
        processId: response.organisationProcessMappings?.[0]?.processId,
        organisationId: ress.getByClient.data.client.main_client,
        subOrganisationId: ress.getByClient.data.client._id
      };

      const res = await getAllProcessSettings(payload);
      const assignments = res.processAssignmentsByCategory?.users || [];
      setAllUsers(assignments)
      if (assignments.length > 0 && assignments[0].roles) {
        // ✅ new API format
        assignments.forEach((category: { roles: any[]; category: string; }) => {
          const tableData: TableData = {};
          category.roles.forEach(role => {
            tableData[role.roleName] = role.users;
          });
          console.log('tableData', tableData);

          switch (category.category.toLowerCase()) {
            case 'operation':
              setOperationData(tableData);
              break;
            case 'audit':
              setAuditData(tableData);
              break;
            case 'management':
              setManagementData(tableData);
              break;
          }
        });
      }
      else if (assignments.length > 0 && assignments[0].hierarchies) {
        // ✅ fallback/hierarchies format
        assignments.forEach((category: { hierarchies: any[]; category: string; }) => {
          const tableData = transformHierarchiesToTableData(category.hierarchies);
          switch (category.category.toLowerCase()) {
            case 'operations':
              setOperationData(tableData);
              break;
            case 'audit':
              setAuditData(tableData);
              break;
            case 'management':
              setManagementData(tableData);
              break;
          }
        });
       
      }
       setLoader(false)
      }
            ).catch((err)=>{
              console.error(err);
              
            })
             })
      .catch(console.error);
    } catch (error) {
      console.error(error);
    }
  };

  const getSystemUser=(query:{search:string})=>{
        getSystemUsers(query).then((res) => {
      console.log(res.systemUsers.items);
      setLoading(true)
      setUserOptions(res.systemUsers.items.map((item: { id: any; name: any; employeeId: any; }) => {
        return (
          {
            id: item.id,
            name: `${item.name}`,
            employeeId: item.employeeId
          }
        )
      })

      )
       setLoading(false)
    }).catch((err) => {
      console.error(err);
 setLoading(false)
    })
  }
  useEffect(() => {
getSystemUser(query)
  }, [query])

  const transformHierarchiesToTableData = (hierarchiesArray: any[]): TableData => {
    const tableData: TableData = {};

    for (const hierarchy of hierarchiesArray) {
      Object.keys(hierarchy).forEach(role => {
        if (!tableData[role]) {
          tableData[role] = [];
        }
        tableData[role].push(`${hierarchy[role]?.name} (${hierarchy[role]?.employeeId})`);
      });
    }

    return tableData;
  };
  const handleChange = (status: boolean, id: string) => {
    setId(id);
    const payload = {
      id: id,
      isActive: true
    }
    if(status==!true){
      setAction('ON')
      setTurnOff(true)
//  updateProcessSettings(payload).then(async () => {
//       await fetchData()
//     }).catch((err) => {
//       console.error(err);

//     })
    }
    else{
      setAction('OFF')
     setTurnOff(true)
    }
   
  }
  const getMaxRows = (data: TableData) =>
    Math.max(0, ...Object.values(data).map(arr => arr.length));

  // const handleAddRow = (
  //   data: TableData,
  //   setData: React.Dispatch<React.SetStateAction<TableData>>,
  //   setEdit: React.Dispatch<React.SetStateAction<EditState>>
  // ) => {
  //   setIsAdded(true)
  //   setData(prev => {
  //     const updated = { ...prev };
  //     Object.keys(updated).forEach(role => {
  //       updated[role] = [...updated[role], ''];
  //     });
  //     return updated;
  //   });
  //   setEdit(prev => ({ ...prev, [getMaxRows(data)]: true }));
  // };

  const handleAddRow = (
  data: TableData,
  setData: React.Dispatch<React.SetStateAction<TableData>>,
  setEdit: React.Dispatch<React.SetStateAction<EditState>>,
  title: string
) => {
  setIsAdded(true);

  setData(prev => {
    let updated = { ...prev };

    // 🟠 if it's empty, initialize with roles
    if (Object.keys(updated).length === 0) {
      if (title.includes('Operation')) {
        updated = { manager: [''], supervisor: [''], agent: [''] };
      } else if (title.includes('Audit')) {
        updated = { qcManager: [''], qcSupervisor: [''], qcAgent: [''] };
      } else if (title.includes('Management')) {
        updated = { admin: [''], subAdmin: [''] };
      }
    } else {
      Object.keys(updated).forEach(role => {
        updated[role] = [...updated[role], ''];
      });
    }

    return updated;
  });

  setEdit(prev => ({ ...prev, [getMaxRows(data)]: true }));
};

  const handleDeleteRow = (
    index: number,
    data: TableData,
    setData: React.Dispatch<React.SetStateAction<TableData>>,
    setEdit: React.Dispatch<React.SetStateAction<EditState>>
  ) => {
    setData(prev => {
      const updated: TableData = {};
      Object.entries(prev).forEach(([role, users]) => {
        updated[role] = users.filter((_, i) => i !== index);
      });
      return updated;
    });
    setEdit(prev => {
      const { [index]: _, ...rest } = prev;
      return rest;
    });
    setIsAdded(false)
  };

  const handleDeleteApi = (title: any, data: any, index: number) => {
    setDeleteTitle(title);
    setDeleteData(data);
    setDeleteIndex(index);
    setOpenDelete(true);

  }
  const getHeaders=async()=>{
      await selectHeaders({name
: 
"organisation",userId
: 
user.id})
          .then((res) => {
            console.log("Initial organizations loaded:", res.selectHeader);
            dispatch(setOrgList(res.selectHeader))
          })
          .catch((err) =>
            console.log("error loading initial organization options:", err)
          );

  }
  console.log('deleteData',deleteData);
  
  const submitDelete = () => {
    setLoader(true)
    console.log('data', deleteData);

    // eslint-disable-next-line prefer-const
    let payload: any = {
      input: {
        organisationId: processDetail.organizationID,
        subOrganisationId: processDetail.subOrganizationId,
        processId: processDetail.processId,
      }
    }

    if (deleteTitle.includes('Operation')) {
      
      const value = deleteData?.filter((item:{category:string})=>item.category=='operations')[0]?.hierarchies[deleteIndex];
      payload.input.managerId = value?.manager.id;
      payload.input.supervisorId = value?.supervisor.id;
      payload.input.agentId = value?.agent.id;
      deleteCreate(payload, deleteTitle).then(() => {
        showToast.success('DELETED')
        setOpenDelete(false);
        fetchData()
        getHeaders()
      })
    }
    else if (deleteTitle.includes('Audit')) {
      const value = deleteData?.filter((item:{category:string})=>item.category=='audit')[0]?.hierarchies[deleteIndex];
      payload.input.qcManagerId = value.qcManager.id;
      payload.input.qcSupervisorId = value.qcSupervisor.id;
      payload.input.qcAgentId = value.qcAgent.id;
      deleteCreate(payload, deleteTitle).then(() => {
        fetchData()
        showToast.success('DELETED')
        setOpenDelete(false);
        getHeaders()
      })
    }
    else if (deleteTitle.includes('Management')) {
      const value = deleteData?.filter((item:{category:string})=>item.category=="management")[0]?.hierarchies[deleteIndex];
      payload.input.adminId = value.admin.id;
      payload.input.subAdminId = value.subAdmin.id;
      deleteCreate(payload, deleteTitle).then(() => {
        fetchData()
        showToast.success('DELETED')
        setOpenDelete(false);
        getHeaders()
      })
    }
  }
  const handleSaveRow = async (
    index: number,
    data: TableData,
    title: string,
    setEdit: React.Dispatch<React.SetStateAction<EditState>>
  ) => {


    // 1️⃣ Extract IDs for this row
    const rowPayload: { [role: string]: string } = {};
    Object.keys(data).forEach(role => {
      rowPayload[role] = data[role][index];
    });
    setLoader(true)
    // 2️⃣ Call the API
    // eslint-disable-next-line prefer-const
    let payload:any = {
      input: {
        organisationId: processDetail.organizationID,
        subOrganisationId: processDetail.subOrganizationId,
        processId: processDetail.processId,
        orgId:orgId
      }
    }
    console.log('operationValue', operationValue, payload);

    if (title.includes('Operation')) {
      payload.input.managerId = operationValue.manager||'';
      payload.input.supervisorId = operationValue.supervisor||"";
      payload.input.agentId = operationValue.agent||"";
      await operationCreate(payload, title).then((res) => {
        console.log(res);
        fetchData()
        setEdit(prev => ({ ...prev, [index]: false }));
        showToast.success('CREATED')
        setIsAdded(false)
        getHeaders()
      }).catch((err) => {
        setLoader(false)
        console.log(err);
        showToast.error(err.message)
      })
    }
    else if (title.includes('Audit')) {
      payload.input.qcManagerId = operationValue.qcManager;
      payload.input.qcSupervisorId = operationValue.qcSupervisor;
      payload.input.qcAgentId = operationValue.qcAgent;
      await operationCreate(payload, title).then((res) => {
        console.log(res);
        fetchData()
        setEdit(prev => ({ ...prev, [index]: false }));
        showToast.success('CREATED')
        setIsAdded(false)
        getHeaders()
      }).catch((err) => {
        setLoader(false)
        console.log(err);
        showToast.error(err.message)
      })
    }
    else if (title.includes('Management')) {
      payload.input.adminId = operationValue.admin;
      payload.input.subAdminId = operationValue.subAdmin;
      await operationCreate(payload, title).then((res) => {
        console.log(res);
        fetchData()
        setEdit(prev => ({ ...prev, [index]: false }));
        showToast.success('CREATED')
        setIsAdded(false)
        getHeaders()
      }).catch((err) => {
        setLoader(false)
        console.log(err);
        showToast.error(err.message)
      })
    }
      

    // 3️⃣ Mark as saved

  };
  const handleCellChange = (
    role: string,
    index: number,
    value: string,
    setData: React.Dispatch<React.SetStateAction<TableData>>
  ) => {
    setData(prev => {
      const updated = { ...prev };
      updated[role] = [...updated[role]];
      updated[role][index] = value;
      return updated;
    });
  };

  const renderTable = (
  title: string,
  data: TableData,
  setData: React.Dispatch<React.SetStateAction<TableData>>,
  editState: EditState,
  setEditState: React.Dispatch<React.SetStateAction<EditState>>
) => {
  let roleNames = Object.keys(data);
  const maxRows = getMaxRows(data);

  if (roleNames.length === 0) {
    // Static header if no data
    if (title.includes('Operation')) roleNames = ['manager', 'supervisor', 'agent'];
    else if (title.includes('Audit')) roleNames = ['qcManager', 'qcSupervisor', 'qcAgent'];
    else if (title.includes('Management')) roleNames = ['admin', 'subAdmin'];
  }
console.log(getSubModulePermission("Process Settings",'Remove user')?.isEnabled);

  return (
    <div className="mb-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2 bg-white pb-4 rounded-lg">
        <Typography variant="h6" sx={{ color: "#2B3674", fontWeight: 600, fontSize: '16px' }}>
          {title}
        </Typography>
      { getSubModulePermission("Process Settings",'Assign user')?.isEnabled && <button
          // disabled={isAdded}
          type="button"
          className="text-sm font-200 text-gray-700 bg-none px-4 py-2 rounded flex"
          onClick={() => handleAddRow(data, setData, setEditState, title)}
        >
          <Icon><Image src={addIcon} alt="add" width={20} height={20} /></Icon>
          <span>Add</span>
        </button>}
      </div>

      <TableContainer component={Paper}>
        <Table size="small">
          <TableHead sx={{ backgroundColor: "#f0fdff" }}>
            <TableRow>
              {roleNames.map(role => (
                <TableCell key={role} sx={{ color: "#1E40AF", fontWeight: 600 }}>
                  {role.charAt(0).toUpperCase() + role.slice(1)}
                </TableCell>
              ))}
              <TableCell sx={{ color: "#1E40AF", fontWeight: 600 }}>Action</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {maxRows > 0 ? (
              [...Array(maxRows)].map((_, rowIdx) => (
                <TableRow key={rowIdx}>
                  {roleNames.map(role => (
                    <TableCell key={role}>
                      {editState[rowIdx] ? (
                     <Autocomplete
  // size="small"
  fullWidth
  className='h-[40px]'
  options={userOptions}
  loading={loading}
   sx={{'& .MuiAutocomplete-root':{
        height:'40px'
      }}}
  getOptionLabel={(option:{name:string,employeeId:string}) => `${option.name} (${option.employeeId})`}
  value={userOptions.find((u:{name:string}) => u.name === data[role]?.[rowIdx]) || null}
  onChange={(_, newValue:any) => {
    setOprationValue({
      ...operationValue,
      [role]: newValue ? newValue.id : null,
    });
    handleCellChange(role, rowIdx, newValue ? newValue.name : '', setData);
  }}
  renderInput={(params) => (
    <TextField
      {...params}
      variant="outlined"
      className='h-[40px]'
      placeholder='Select User'
      sx={{'& .MuiAutocomplete-root':{
        height:'40px'
      }}}
      InputProps={{
        ...params.InputProps,
        endAdornment: (
          <>
            {loading ? <CircularProgress color="inherit" size={20} /> : null}
            {params.InputProps.endAdornment}
          </>
        ),
      }}
    />
  )}
  isOptionEqualToValue={(option:any, value:any) => option?.id === value?.id}
/>
                      ) : (
                        data[role]?.[rowIdx] || ''
                      )}
                    </TableCell>
                  ))}
                  <TableCell>
                    {editState[rowIdx] && (
                      <IconButton color="primary" disabled={loader} onClick={() => handleSaveRow(rowIdx, data, title, setEditState)}>
                        <Save fontSize="small" />
                      </IconButton>
                    )}
                    <IconButton color="error" disabled={loader || !getSubModulePermission("Process Settings",'Remove user')?.isEnabled} onClick={() => editState[rowIdx] ? handleDeleteRow(rowIdx, data, setData, setEditState) : handleDeleteApi(title, allUsers, rowIdx)}>
                      <Delete fontSize="small" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
              
                <TableCell colSpan={roleNames.length+1} align="center">
                  No data found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </div>
  );
};

  return (
    // <PermissionGuard
    //   moduleName="Organizations"
    //   subModuleName="Process Settings"
    //   permissionName="Enable/Disable process"
    //   permissions={headerProcess?.role?.permissions || []}
    // >
      <div className="py-4 space-y-8 bg-[white] !rounded-[16px] h-[100%]">
        {loader && <Loader />}
      <Box sx={{ width: '100%' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={value} onChange={(_, v) => setValue(v)}>
            {process.map((item) => (
              <Tab
                key={item.processId}
                // disabled={!item.isActive}
                label={item.processName}
                {...a11yProps(0)}
                className='!normal-case'
                sx={{ color: item.isActive?"#2B3674":"#706e6e91", fontWeight: 600, fontSize: '16px' }}
              />
            ))}
          </Tabs>
        </Box>
        {process.length !== 0 && process.map((item,i)=>(
<CustomTabPanel value={value} index={i} key={i}>

                <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }} className='!mb-0 px-4'>
                  {/* <Typography className='!mt-2'>{process[0].processName}</Typography> */}
                   <Typography variant="h6" sx={{ color: "#2B3674", fontWeight: 600, fontSize: '16px' }} className='!mt-3'>
            {item.processName}
          </Typography>
                 {getSubModulePermission("Process Settings",'Enable/Disable process')?.isEnabled && <Switch
                    checked={item.isActive}
                    onClick={(e) => e.stopPropagation()}
                    onChange={() => handleChange(item.isActive, item._id)}
                  />}
                </Box>
             
            {item.isActive &&
            <>            <Accordion defaultExpanded  className='!mt-3'>
             <AccordionSummary expandIcon={<ExpandMoreIcon />} className='!mb-0 !bg-[#7592F90D] !min-h-[40px]' sx={{'& .MuiAccordionSummary-content':{
                marginY:"0px !important"
              }}}>
                 <Typography variant="h6" sx={{ color: "#2B3674", fontWeight: 600, fontSize: '16px' }} className='!my-3'>
            {"Role Assignment"}
          </Typography>
              </AccordionSummary>

             
              <AccordionDetails>
                {i==0 &&  
                <>{renderTable("Roles Assignment – Operation", operationData, setOperationData, operationEdit, setOperationEdit)}
                {renderTable("Roles Assignment – Audit", auditData, setAuditData, auditEdit, setAuditEdit)}
                {renderTable("Roles Assignment – Management", managementData, setManagementData, managementEdit, setManagementEdit)}</>}
              </AccordionDetails>
              
            </Accordion>
             <Accordion defaultExpanded  className='!mt-3'>
             <AccordionSummary expandIcon={<ExpandMoreIcon />} className='!mb-0 !bg-[#7592F90D] !min-h-[40px]' sx={{'& .MuiAccordionSummary-content':{
                marginY:"0px !important"
              }}}>
                 <Typography variant="h6" sx={{ color: "#2B3674", fontWeight: 600, fontSize: '16px' }} className='!my-3'>
            {"SLA"}
          </Typography>
              </AccordionSummary>
                            <AccordionDetails></AccordionDetails>
               </Accordion></>
}
        </CustomTabPanel>
        ))}
        
      </Box>
      {openDelete && (
        <DeleteModal
          isOpen={openDelete}
          onClose={() => setOpenDelete(false)}

          onDelete={submitDelete}

        />
      )}
       {turnOff && (
              <DeleteModals
                isOpen={turnOff}
                onClose={handleTurnOff}
                onDelete={actions=='OFF'?submitTurnOff:submitTurnOn}
                message={actions=='OFF'?'Disabling this process will block access for organization-assigned users':'The template for process is enabled, you may now start the process'}
                title={actions=='OFF'?'Turn Off':'Turn On'}
                action={actions=='OFF'?'Turn Off':'Turn On'}
              />
            )}
      </div>
    // </PermissionGuard>
  );
}
