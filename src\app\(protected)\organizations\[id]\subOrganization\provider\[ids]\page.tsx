'use client';
import React, { useState } from 'react'
import CreateClientPage from "../../../../../masters/Form";
import { getTemplates } from '@/api/templates/templates';
import { getSubModulePermissionCommon } from '@/utils/generic';
// import { form } from "../../../organizations/create/form"
// import { Form as ClientForm } from "@/types/cl
const Page = () => {
   const [form, setForm] = useState({});
   const [templateId, setTemplateId] = useState('')  
    const [flattedValues, setFlattedValues] = useState([]);
   
      React.useEffect(() => {
        getTemplates({ search: "", filters:{key:"provider", type:'Master',isActive:true}}).then((res)=>{
      const template = res.templates.data.templates[0];
       setFlattedValues(res.templates.data.templates[0]?.view_summary?.inGrid);
      console.log('template',JSON.parse(template.fields));
      if (template && template.fields) {
        const fieldsData = template.fields;
        if (typeof fieldsData === 'string') {
            try {
              const unescaped = fieldsData.replace(/^"|"$/g, '').replace(/\\"/g, '"');
  
  // Step 2: Parse it
  const parsedJSON = JSON.parse(unescaped)[0];
  
  console.log('parsedJSON',parsedJSON);
              // console.log('fieldsData',JSON.parse(JSON.parse(fieldsData)));
              
              // const parsedFields = JSON.parse(fieldsData);
              setTemplateId(template._id);
              setForm(parsedJSON);
               } catch (error) {
              console.error("Error parsing JSON:", error);
            }
            } else {
            setTemplateId(template._id);
            setForm(fieldsData);
          }
        } else {
          console.warn("Template or fields property is missing.");
        }
        }).catch((err)=>{
          console.error(err);
        })
      },[])
   const getPermission=()=>{
            const permission =getSubModulePermissionCommon('Organizations','Provider', 'Update')?.isEnabled??false
            return permission
          }
  return (
     <div>
        <CreateClientPage formTemplate={form} type="edit"  clientTyoe={""} templateId={templateId} flattedValues={flattedValues} access={getPermission()}/>
      </div>
  )
}

export default Page
