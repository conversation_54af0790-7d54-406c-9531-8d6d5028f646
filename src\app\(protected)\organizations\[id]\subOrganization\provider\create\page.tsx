'use client'
import React, { useState } from 'react'
import CreateClientPage from '../../../../../masters/Form'
import { Button } from '@mui/material'
import { getTemplates } from '@/api/templates/templates'
import Loader from '@/components/loader/loader'
const Page = () => {
  const [form, setForm] =useState({})
  const [templateId, setTemplateId] = useState('')
    const [flattedValues, setFlattedValues] = useState([]);
const [loader, setLoader] = React.useState(false);
React.useEffect(() => {
setLoader(true)
  getTemplates({ search: "", filters:{key:"provider", type:'Master',isActive:true}})
    .then((res) => {
      const template = res.templates.data.templates[0];
       setFlattedValues(res.templates.data.templates[0]?.view_summary?.inGrid);
      console.log('template',JSON.parse(template.fields));
      if (template && template.fields) {
        const fieldsData = template.fields;
        if (typeof fieldsData === 'string') {
          try {
            const unescaped = fieldsData.replace(/^"|"$/g, '').replace(/\\"/g, '"');

// Step 2: Parse it
const parsedJSON = JSON.parse(unescaped)[0];

console.log('parsedJSON',parsedJSON);
            // console.log('fieldsData',JSON.parse(JSON.parse(fieldsData)));
            
            // const parsedFields = JSON.parse(fieldsData);
            setTemplateId(template._id);
            setForm(parsedJSON);
          } catch (error) {
            console.error("Error parsing JSON:", error);
          }
        } else {
          setTemplateId(template._id);
          setForm(fieldsData);
        }
        setLoader(false)
      } else {
        console.warn("Template or fields property is missing.");
      }
    })
    .catch((err) => {
      setLoader(false)
      console.error("Error fetching templates:", err);
    });
}, []);
console.log(form,'form');

    const handleBack = () => {
      if(typeof window !== 'undefined')
      {window.history.back();}
    }
  return (
    loader? <Loader />:
    <div className='px-6'>
<div
                    className="text-sm font-200 text-gray-700  pl-4 m-0 rounded flex items-end !justify-end w-full"
                  >
                   <Button className="font-normal !h-[40px] !mt-0 text-[12px] !w-[75px] !shadow-none" onClick={handleBack}>Back</Button>
                  </div>
    <CreateClientPage formTemplate={form} type="create" clientTyoe={'MAIN_CLIENT'} templateId={templateId} flattedValues={flattedValues} access={true}/>
    </div>

  )
}

export default Page
