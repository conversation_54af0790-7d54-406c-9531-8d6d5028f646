/* eslint-disable @typescript-eslint/no-explicit-any */
// import { useRouter } from 'next/navigation';
'use client';
import { setClientTable } from "@/features/client/clientSlice";
import React from "react";
import { useDispatch, useSelector } from "react-redux";
// import ClientDataTable from "../../ClientDataTable";
import { TableData } from "@/types/user";
import { convertFormJsonFromClients, transformedClients } from "@/utils/generic";
import ClientDataTable from "@/app/[components]/tableGrid/ClientDataTable";
import { getAllProvider } from "@/api/masters/provider/provider";
import Loader from "@/components/loader/loader";
import { getTemplates } from "@/api/templates/templates";
import { setHeaders, setHeadersDefault } from "@/features/headers/headersSlice";
import { useParams } from "next/navigation";
import { RootState } from "@/store";

  export default function Page() {
  const headerProcess:any =  useSelector((state: RootState) => state.headerProcess);
  const dispatch = useDispatch();
  const [loader, setLoader] = React.useState(false);
  const {id} =useParams()
  const [query, setQuery] = React.useState({
   search: '',
    filters: {subOrganisationId:id},
    sortBy: '',
    sortOrder: 'asc',
    page: 1,
    limit: 10,
    });
  const [pagination, setPagination] = React.useState(1);
const [roles,setRoles]= React.useState<any>([])

React.useEffect(() => {
  console.log('headerProcess', headerProcess);

  const role=headerProcess?.role?.permissions?.filter((item:{moduleName:string}) =>
      item.moduleName.includes('Organization')
    )[0].subModules?.filter((data:{moduleName:string})=>data.moduleName=="Provider")[0].permissions
    setRoles(role);
  }, [headerProcess]);

  React.useEffect(() => {
    handleGetApi()
    console.log('each');
  }, [query]);

const handleGetApi=(view?:any)=>{
  setLoader(true)
    // const payload=query
      getTemplates({ search: "", filters:{key:"provider", type:'Master',isActive:true}}).then((res) => {
          const template = res.templates.data.templates[0];
          console.log('template', template);
           if(view==undefined){
          dispatch(setHeaders(res.templates.data.templates[0]?.view_summary?.inGrid))
          dispatch(setHeadersDefault(res.templates.data.templates[0]?.view_summary?.default))
           }
          const result = Object.fromEntries(res.templates.data.templates[0]?.view_summary?.inGrid.map((key: any) => [key, 1]));
          const payload = {
            input: { ...query, ['selectedFields']: result },
          };  
    getAllProvider(view==undefined?payload:view).then((res)=>{
    console.log('each second',res);
    setPagination(res.providers.pagination)
      const data = transformedClients(res.providers.providers);
                const tableData = convertFormJsonFromClients(data);
    dispatch(setClientTable(tableData as TableData));
    setLoader(false)
    }).catch((err)=>{
      setLoader(false)
      console.error(err);
    })
          })   .catch((err) => {
          setLoader(false)
          console.error(err);
        });
  }

  return(
    // <PermissionGuard
    //   moduleName="Organizations"
    //   subModuleName="Provider"
    //   permissionName="View"
    //   permissions={headerProcess?.role?.permissions || []}
    // >
    <>
      {loader && <Loader />}
      <ClientDataTable
        title={"Provider"}
        handleGetApi={handleGetApi}
        pagination={pagination}
        query={query}
        setQuery={setQuery}
        role={roles}
      />
      </>
    // </PermissionGuard>
  )
}

  