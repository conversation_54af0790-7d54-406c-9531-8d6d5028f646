/* eslint-disable prefer-const */
/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';
import { Drawer, TextField, Typography, IconButton, InputLabel, MenuItem } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useEffect, useState } from "react";
import { showToast } from "@/components/toaster/ToastProvider";
import Loader from "@/components/loader/loader";
import { useParams, usePathname } from "next/navigation";
import {getOrgList } from "@/api/user-management/organization-roles/orgRoles";
import { createOrgUsers, getOrgUserById, updateOrgUsers } from "@/api/organizations/organizations";

interface Role {
  _id: string;
  name: string;
  type: string;
  // add more if needed (optional)
}

const CreateDrawer = ({
  open,
  onClose,
  _id,
  mainId,
  orgId
}: {
  open: boolean;
  onClose: () => void;
  onSave: () => void;
  _id?: string;mainId:string
  orgId?:string
}) => {
  
 const { id } = useParams();
 const normalisedId = Array.isArray(id) ? id[0] : id;
  const [name,setName] = useState('')
  const [empId,setEmpId] = useState('')
  const [roleName,setRoleName] = useState('')
  const [email,setEmail] = useState('')
  const [loader,setLoader] = useState(false)
  const [orgType, setOrgType] = useState('');
  const [rolesList, setRolesList] = useState<Role[]>([]);
  const pathName = usePathname()
  const isOrgRoles = pathName.includes('user-management/organization-roles/');
console.log('orgId,',mainId);

  const handleChange = (label: string, value: string) => {
    switch (label) {
      case 'roleId':
        setRoleName(value);
        break;
      case 'name':
        setName(value);
        break;
      case 'empId':
        setEmpId(value);
        break;
      case 'email':
        setEmail(value);
        break;
      case 'roleId':
        setOrgType(value);
        break;

    }
  };

  useEffect(()=>{
    setLoader(true)
    setOrgType(pathName.includes('/subOrganization/settings/') ? "SUB_ORGANISATION" : "MAIN_ORGANISATION")
    console.log(pathName.includes('/subOrganization/settings/') ? "SUB_ORGANISATION" : "MAIN_ORGANISATION")
     getOrgList().then((res) => {
      const filteredRoles = (res?.organisationRoles?.items || []).filter((role: any) => 
        role.type === (pathName.includes('/subOrganization/settings/') ? "SUB_ORGANISATION" : "MAIN_ORGANISATION")
      );
        setRolesList(filteredRoles)
        console.log('filteredRoles',filteredRoles,res?.organisationRoles?.items,orgType)
        setLoader(false)
      }).catch((err) => {
        setLoader(false)
        console.error(err);
      });
  },[])

  useEffect(()=>{
    console.log('idddddd',_id)
    
    setLoader(true)
    if(_id && _id !== ''){
      getOrgUserById({id:_id})
              .then((res) => {
                setLoader(false)
                console.log(res, 'res.getUsersWithPagination.users');
                setName(res?.organisationUser?.name)
                setRoleName(res?.organisationUser?.roleId)
                setEmail(res?.organisationUser?.email)
                setEmpId(res?.organisationUser?.employeeId)
              })
              
              .catch((err) => {
                setLoader(false)
                console.error(err);
              });
    }else{
      
      setName('')
                setRoleName('')
                setEmail('')
                setEmpId('')
                setLoader(false)
    }
  },[_id,open])

  const handleSave = () => {
    setLoader(true)
    if(_id && _id !== ''){
      let payload:any = {
        input: {
          id:_id,
          name: name,
          email: email,
          employeeId: empId,
          organisationId: normalisedId ?? '', 
          roleId: roleName,
          type: pathName.includes('/subOrganization/settings/') ? "SUB_ORGANISATION" : "MAIN_ORGANISATION",
        },
      };
      if (pathName.includes('/subOrganization/settings/') && id) {
  payload.input = { ...payload.input, subOrganisationId: normalisedId, organisationId:mainId };

}
    
      updateOrgUsers(payload).then((res) => {
        showToast.success(res?.code);
        setRoleName('')
        setName('')
        setEmpId('')
        setEmail('')
        onClose()
        setLoader(false)
      })
      .catch((err) => {
        showToast.error(err.message);
        console.error(err);
        setLoader(false)
      });
    }else{
      let payload:any = {
  input: {
    name,
    email,
    employeeId: empId,
    organisationId: normalisedId ?? '',
    roleId: roleName,
    type: pathName.includes('/subOrganization/settings/') ? "SUB_ORGANISATION" : "MAIN_ORGANISATION",
    orgId,
  }
};

if (pathName.includes('/subOrganization/settings/') && id) {
  payload.input = { ...payload.input, subOrganisationId: normalisedId, organisationId:mainId };

}
      
    
      createOrgUsers(payload).then((res) => {
        showToast.success(res?.code);
        setRoleName('')
        setName('')
        setEmpId('')
        setEmail('')
        onClose()
        setLoader(false)
      })
      .catch((err) => {
        showToast.error(err.message);
        console.error(err);
        setLoader(false)
      });
    }
    
  }

  
  // console.log('columns', columns);

  return (
    <>
    {loader&&
      <Loader/>}
    <Drawer anchor="right" open={open} onClose={onClose}>
        <div className="w-[400px] p-6 flex flex-col h-full">
          <div className="flex justify-between items-center mb-4">
            <section className="w-full">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2 bg-blue-50 px-2 py-1 pt-[10px]">
                <Typography variant="h6" className="font-semibold text-gray-900">
                  {pathName.includes('/subOrganization/settings/') ? "Create Sub Organization User" : "Create Organization User"}
                </Typography>
                <IconButton onClick={onClose}>
                  <CloseIcon />
                </IconButton>
              </div>
            </section>
          </div>

          <div className="flex-1 overflow-auto">
             <div className="flex flex-col mt-3">
                <InputLabel shrink className="!text-[22px] font-semibold text-gray-800">
                  Name
                </InputLabel>
                <TextField fullWidth variant="outlined" value={name} onChange={(e) => handleChange('name', e.target.value)} />
              </div>
            {!isOrgRoles && (
              <>
                {/* Name */}
                

                {/* Email */}
                <div className="flex flex-col mt-3">
                <InputLabel shrink className="!text-[22px] font-semibold text-gray-800">
                  Email
                </InputLabel>
                <TextField fullWidth variant="outlined" value={email} onChange={(e) => handleChange('email', e.target.value)} />
                  
                </div>

                {/* Employee ID */}
                <div className="flex flex-col mt-3">
                <InputLabel shrink className="!text-[22px] font-semibold text-gray-800">
                  Employee Id
                </InputLabel>
                <TextField fullWidth variant="outlined" value={empId} onChange={(e) => handleChange('empId', e.target.value)} />

                </div>

                {/* Role Name */}
                <div className="flex flex-col mt-3">
                <InputLabel shrink className="!text-[22px] font-semibold text-gray-800">
                  Role
                </InputLabel>
                <TextField
                  select
                  fullWidth
  value={roleName}
  onChange={(e) => handleChange('roleId', e.target.value)}
>
  
{rolesList.map((role) => (
          <MenuItem key={role._id} value={role._id}>
            {role.name}
          </MenuItem>
        ))}
</TextField>
                </div>
              </>
            )}


          </div>

          <div className="flex justify-end mt-6">
            <button className="bg-[#1969AE] hover:bg-[#155b96] text-white font-bold py-2 px-6 rounded w-[25%]" onClick={handleSave}>
              Save
            </button>
          </div>
        </div>
      </Drawer>
    </>
  );
};

export default CreateDrawer;
