export const form = {
    "id": "step-0fa5112f-36c1-4d83-a067-995ffd289c24",
    "name": "Step 3",
    "sections": [
      {
        "id": "b8bbe54a-4638-4b3d-a18b-0e64dd9f9789",
        "name": "Basic Information",
        "fields": [
        {
          "id": "field-06a8a032-3c66-4dcd-9f7e-e87c84b1b789",
          "label": "Client Name",
          "field_type": "text",
          "logic_rules": [],
          "placeholder": "Enter a Client Name",
        },
        {
          "id": "field-863cc7cd-7171-4275-8a58-ffd8438cb43e",
          "label": "Legal Business Name",
          "field_type": "text",
          "logic_rules": [],
          "placeholder": "Enter a Business Name",
          "required": true,
          "filter": true,
        },
        {
          "id": "field-7fe73dd8-eabf-4bb5-8a92-caa63a15b92a",
          "label": "Client Onboard Date",
          "field_type": "date",
          "logic_rules": [],
          "placeholder": "Select Client Onboard Date",
        },
        {
          "id": "field-59d92dbd-99b0-499e-8463-b39a1f32d4d2",
          "label": "Client ID",
          "field_type": "text",
          "logic_rules": [],
          "placeholder": "Enter Client ID",
          "required": true,
          "filter": true,
        },
        {
          "id": "field-2af433c0-41be-4d7c-8662-ef264ff1a90a",
          "label": "Client Email",
          "field_type": "email",
          "logic_rules": [],
          "placeholder": "Enter a Client Email",
          "required": true,
          "filter": true,
        },
        {
          "id": "field-57149142-5677-485c-b794-69dabc2375de",
          "label": "Telephone",
          "field_type": "phone",
          "logic_rules": [],
          "placeholder": "Enter Telephone Number",
          "required": true,
          "filter": true,
        },
        {
          "id": "field-ced8c2f2-b94c-4de1-af61-8a7206a4c00b",
          "label": "Fax",
          "field_type": "phone",
          "logic_rules": [],
          "placeholder": "Enter Fax",
          "required": true,
          "filter": true,
        },
        {
          "id": "field-4fe7348d-8a51-4fe7-ab6e-f37d896f10ca",
          "label": "Address ",
          "field_type": "text",
          "logic_rules": [],
          "placeholder": "Enter the Address ",
          "required": true,
          "filter": true,
        },
        {
          "id": "field-fb4b9e70-5cc5-4999-b828-052ad81daa00",
          "label": "Address 2",
          "field_type": "text",
          "logic_rules": [],
          "placeholder": "Enter Address 2",
        },
        {
          "id": "field-36ee7d5e-f74c-4310-a070-d325efe822b2",
          "label": "City",
          "field_type": "select",
          "logic_rules": [],
          "placeholder": "Select City",
          "required": true,
          "filter": false,
          "options": [
            { "id": "option-1748606601855", "value": "New York" },
            { "id": "option-1748606613815", "value": "Chicago" },
            { "id": "option-1748606643591", "value": "Los Angels" },
            { "id": "option-1748606661287", "value": "San jose" }
          ]
        },
        {
          "id": "field-b99e9376-9536-4020-9ad3-131d612786d3",
          "label": "State/Province",
          "field_type": "text",
          "logic_rules": [],
          "placeholder": "Enter State/Province",
          "required": true
        },
        {
          "id": "field-8a3fddf8-d41c-49a2-9c1b-004e8f0fdef8",
          "label": "Zip/Postal Code",
          "field_type": "number",
          "logic_rules": [],
          "placeholder": "Enter Zip/Postal Code",
          "required": true,
          "filter": true
        },
        {
          "id": "field-a2afb268-77e5-4300-bf54-c9a32a47cc22",
          "label": "County",
          "field_type": "select",
          "logic_rules": [],
          "placeholder": "Select County",
          "required": true,
          "filter": true,
          "options": [
            { "id": "option-1748606705623", "value": "Dallas County" },
            { "id": "option-1748606715247", "value": "Dillingham Census Area" },
            { "id": "option-1748607084031", "value": "Los Angeles County" },
            { "id": "option-1748607107239", "value": "Martin County" }
          ]
        },
        {
          "id": "field-57624709-e58a-41f2-82ea-f917bca38e6f",
          "label": "Country",
          "field_type": "text",
          "logic_rules": [],
          "placeholder": "Enter Country"
        }
      ]
      },
      {
        "id": "b3cfdc9f-326e-45c0-9993-73cd73841b5f",
        "name": "Point of contact",
        "fields": [
          {
            "id": "field-6c85729c-bd75-4cac-acac-452ddb9d51b4",
            "label": "grid field",
            "field_type": "grid",
            "logic_rules": [],
            "columns": [
              {
                "name": "Name",
                "fieldType": "text",
                "id": "col_1748606087347"
              },
              {
                "name": "Email ID",
                "fieldType": "email",
                "id": "col_1748606102818"
              },
              {
                "name": "Mobile Number",
                "fieldType": "number",
                "id": "col_1748606121674"
              },
              {
                "name": "Is Primary",
                "fieldType": "select",
                "id": "col_1748606138339"
              },
              {
                "name": "Action",
                "fieldType": "text",
                "id": "col_1748606150507"
              }
            ],
            "rows": []
          }
        ]
      }
    ]
  }