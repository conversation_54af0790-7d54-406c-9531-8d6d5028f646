import { TableConfig, Column } from "@/types/user";

interface FormField {
  id: string;
  label: string;
  field_type: string;
  logic_rules: unknown[];
  placeholder: string;
  required?: boolean;
  filter?: boolean;
  options?: { id: string; value: string }[];
}

// Function to transform form fields into table configuration
export function generateTableConfig(formFields: FormField[]): TableConfig {
  const columns = formFields
    .filter(field => field.filter !== false)
    .map(field => ({
      id: field.id.replace('field-', ''),
      title: field.label,
      width: getColumnWidth(field.field_type),
      hasFilter: field.filter || false,
      type: getColumnType(field.field_type),
      visible: true,
      sortable: true,
      filterType: getFilterType(field.field_type),
      placeholder: `Search ${field.label.toLowerCase()}...`,
      ...(field.options && { options: field.options.map(opt => opt.value) })
    })) as Column[];

  // Add default columns
  const defaultColumns: Column[] = [
    {
      id: "profileImage",
      title: "Client Image",
      width: "w-[127px]",
      hasFilter: false,
      type: "image",
      visible: true,
      sortable: false,
      filterType: "text",
      placeholder: "Search image..."
    },
    {
      id: "actions",
      title: "Actions",
      width: "w-[91px]",
      hasFilter: false,
      type: "actions",
      visible: true,
      sortable: false,
      filterType: "text",
      placeholder: "Search actions..."
    }
  ];

  return {
    columns: [...defaultColumns.slice(0, 1), ...columns, ...defaultColumns.slice(1)],
    settings: {
      defaultSortColumn: "businessName",
      defaultSortDirection: "asc",
      rowsPerPage: 10,
      selectable: true,
      filterable: true,
      responsive: true
    }
  };
}

function getColumnWidth(fieldType: string): string {
  switch (fieldType) {
    case 'email':
      return 'w-[380px]';
    case 'phone':
      return 'w-[212px]';
    case 'text':
      return 'w-[218px]';
    case 'number':
      return 'w-[147px]';
    case 'date':
      return 'w-[147px]';
    case 'select':
      return 'w-[147px]';
    default:
      return 'w-[200px]';
  }
}

function getColumnType(fieldType: string): string {
  switch (fieldType) {
    case 'date':
      return 'date';
    case 'number':
      return 'number';
    case 'email':
      return 'email';
    case 'phone':
      return 'phone';
    case 'select':
      return 'select';
    default:
      return 'text';
  }
}

function getFilterType(fieldType: string): string {
  switch (fieldType) {
    case 'select':
      return 'select';
    case 'date':
      return 'date';
    default:
      return 'text';
  }
}

// Sample data generator
export const generateSampleData = () => [
  {
    id: "1",
    profileImage: "https://placehold.co/30x30/4a5568/4a5568",
    clientName: "John Doe",
    businessName: "UnitedHealthcare",
    clientId: "CLT-US-21....",
    email: "<EMAIL>",
    telephone: "+****************",
    fax: "+****************",
    address: "456 North Wabash Ave",
    address2: "Suite 500",
    city: "New York",
    state: "NY",
    zipCode: "10178",
    county: "Dallas County",
    country: "United States",
    onboardDate: "2024-03-20",
    isSelected: false
  },
  {
    id: "2",
    profileImage: "https://placehold.co/30x30/8b5a3c/8b5a3c",
    clientName: "Jane Smith",
    businessName: "Central Reach",
    clientId: "CLT-US-21....",
    email: "<EMAIL>",
    telephone: "+****************",
    fax: "+****************",
    address: "456 North Wabash Ave",
    address2: "Floor 12",
    city: "Chicago",
    state: "IL",
    zipCode: "10178",
    county: "Los Angeles County",
    country: "United States",
    onboardDate: "2024-03-19",
    isSelected: false
  }
]; 