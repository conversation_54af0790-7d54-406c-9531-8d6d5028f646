'use client'

import { getImagedUrl } from "@/api/file/file";
import Image from "next/image";
import React from "react";

export const ImageView = ({ filename ,type}:{filename:string,type:string}) => {
  const [viewUrl, setViewUrl] = React.useState<string | null>(null);
  const [error, setError] = React.useState<string | null>(null);
console.log('filename',filename);

  React.useEffect(() => {
    const fetchImageUrl = async () => {
      try {
        const res = await getImagedUrl({ filename });
        console.log(res.generateViewUrl.data.viewUrl);

        // Optional fetch check if you want:
        const response = await fetch(res.generateViewUrl.data.viewUrl, {
          method: "GET",
          headers: {
            "Content-Type": "image/png",
          },
        });
        console.log(response);

        setViewUrl(res.generateViewUrl.data.viewUrl);
      } catch (err) {
        console.error(err);
        setError('Failed to load image');
      }
    };

    fetchImageUrl();
  }, [filename]);

  if (error) return ;
  if (!viewUrl) return ;
console.log('filename',filename);

  return (
    !viewUrl ?<div className="pb-[15px]">Loading...</div>:
    error?<div className="text-red-500">error</div>
    :type=='form'?<a href={viewUrl} target="_blank" className='truncate text-blue-500 underline '> {filename}</a> : <Image
    style={{height:'30px', width:'30px'}}
      alt="profile"
      src={viewUrl}
      width={50}
      // className="border border-gray-300"
      height={30}
    />
);
};