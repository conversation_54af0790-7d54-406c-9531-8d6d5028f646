/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import * as React from "react";
import ClientDataTable from "./ClientDataTable";
import { useDispatch, useSelector } from "react-redux";
import { setClientTable } from "@/features/client/clientSlice";
import { TableData } from "@/types/user"; // Update with your actual types
// import { form } from "./create/form";
import { getOrganizationUser } from "@/api/organizations/organizations";
import {
  convertFormJsonFromClients,
  transformedClients,
} from "@/utils/generic";
import { getTemplates } from "@/api/templates/templates";
import { setHeaders, setHeadersDefault } from "@/features/headers/headersSlice";
import Loader from "@/components/loader/loader";
import { PermissionGuard } from "@/utils/permissions";
import { RootState } from "@/store";

export default function Page() {
  const dispatch = useDispatch();
  const headerProcess: any = useSelector((state: RootState) => state.headerProcess);
  const [pagination, setPagination] = React.useState(1);
  const [loader, setLoader] = React.useState(false);
  const [query, setQuery] = React.useState({
    search: "",
    filters: { type: "main_client" },
    sortBy: "",
    sortOrder: "asc",
    page: 1,
    limit: 10,
    type: "Provider",
  });

  React.useEffect(() => {
    handleGetApi();
    console.log("each");
  }, [query]);

  const handleGetApi = (view?: any) => {
    setLoader(true);
    getTemplates({ search: "", filters:{key:"organization", type:'Master',isActive:true}}).then((res) => {
      if (view == undefined) {
        dispatch(
          setHeaders(res.templates.data.templates[0]?.view_summary?.inGrid)
        );
        dispatch(
          setHeadersDefault(
            res.templates.data.templates[0]?.view_summary?.default
          )
        );
      }
      console.log(res?.templates,'templates')
        
      
      const result = Object.fromEntries(
        res.templates.data.templates[0]?.view_summary?.inGrid.map(
          (key: any) => [key, 1]
        )
      );
      const payload = {
        input: { ...query, ["selectedFields"]: result },
      };
      //  dispatch(setClientTable({} as TableData));
      getOrganizationUser(payload)
        .then((res) => {
          console.log(
            res.getUsersWithPagination.users,
            "res.getUsersWithPagination.users"
          );
          const data = transformedClients(res.getUsersWithPagination.users);
          const tableData = convertFormJsonFromClients(data);
          dispatch(setClientTable(tableData as TableData));
          setPagination(res.getUsersWithPagination.pagination);
          setLoader(false);
        })

        .catch((err) => {
          setLoader(false);
          dispatch(setClientTable({} as TableData));
          console.error(err);
        });
    });
  };
  return (
    <PermissionGuard
      moduleName="Organizations"
      subModuleName="Main Organization"
      permissionName="View"
      permissions={headerProcess?.role?.permissions || []}
    >
      {loader && <Loader />}
      <div className="p-4">
        {/* {!error && */}
      <ClientDataTable
        title={"Organizations"}
        handleGetApi={handleGetApi}
        setQuery={setQuery}
        query={query}
        pagination={pagination}
        // headerProcess={getOrganizationSubModules('Organizations')}
      /></div>
    </PermissionGuard>
  );
}
