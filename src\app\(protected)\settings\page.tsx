/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import * as React from "react";
import { Autocomplete, Box, Card, CardContent, CircularProgress, List, ListItem, ListItemText, TextField, Tooltip, Typography } from '@mui/material';
import { format, parseISO } from "date-fns";
import { formatRelativeTime, generateAuditTitle } from "@/utils/generic";
import { getAudit } from "@/api/audit/audit";
import { getSystemUsers } from "@/api/user-management/systemUsers/systemUsers";
import Loader from "@/components/loader/loader";

function Page() {
  const inital ={
  page:1,
  limit:10,
  filters:{from:"",to:"",userEmail:""}
}
  const containerRef = React.useRef<HTMLDivElement>(null);
   const [active, setActive] = React.useState("audit-trial");
   const [loading, setLoading] = React.useState(false);
 const [data, setData] =React.useState<any>([])
 const [hasMore, setHasMore] = React.useState(true);
 const [loader, setLoader] = React.useState(true);
const [query, setQuery]=React.useState(inital)
const [selectedUser, setSelectedUser] = React.useState<null | { id: string; name: string }>(null); // ← initial value should be nul
  const [userOptions, setUserOptions] = React.useState([]);
const [searchQuery] = React.useState({
    search: '',
    page:1,
    limit:100
  });

  const getSystemUser=(query:{search:string})=>{
        getSystemUsers(query).then((res) => {
      console.log(res.systemUsers.items);
      setLoading(true)
      setUserOptions(res.systemUsers.items.map((item: { id: string; name: string; email: string; }) => {
        return (
          {
            id: item.email,
            name: `${item.name}`,
          }
        )
      })

      )
       setLoading(false)
    }).catch((err) => {
      console.error(err);
 setLoading(false)
    })
  }
  React.useEffect(() => {
getSystemUser(searchQuery)
  }, [searchQuery])

const fetchLogs = React.useCallback(
  async (isFilterChange = false) => {
    if (!hasMore && !isFilterChange) return;

    // Set loading states
    if (isFilterChange) {
      setLoader(true);      // Show full loader during filter change
    } else {
      setLoading(true);     // Show bottom loader during scroll
    }

    try {
      const res = await getAudit({ input: query });
      const newLogs = res.auditLogs.data || [];

      setData((prev: any) => {
        const previousData = isFilterChange ? [] : prev?.auditLogs?.data || [];

        const existingIds = new Set(previousData.map((log: any) => log.id));
        const uniqueLogs = newLogs.filter((log: any) => !existingIds.has(log.id));

        return {
          auditLogs: {
            ...res.auditLogs,
            data: [...previousData, ...uniqueLogs],
          },
        };
      });

      setHasMore(
        !(newLogs.length === 0 || res.auditLogs.page >= res.auditLogs.totalPages)
      );
    } catch (err) {
      console.error(err);
    } finally {
      // Clear the correct loader
      if (isFilterChange) {
        setLoader(false);
      } else {
        setLoading(false);
      }
    }
  },
  [query, hasMore]
);

// 🔁 Trigger on query change (page or filters)
React.useEffect(() => {
  fetchLogs(query.page === 1); // true if it's a filter change (reset page)
}, [query]);

// 🖱️ Infinite scroll
React.useEffect(() => {
  const container = containerRef.current;
  if (!container) return;

  const handleScroll = () => {
    if (!loading && hasMore && container.scrollTop + container.clientHeight >= container.scrollHeight - 20) {
      setQuery((prev) => ({ ...prev, page: prev.page + 1 }));
    }
  };

  container.addEventListener("scroll", handleScroll);
  return () => container.removeEventListener("scroll", handleScroll);
}, [loading, hasMore]);

// 🧠 Group by date
const groupedLogs: any = React.useMemo(() => {
  const result: any = {};
  data?.auditLogs?.data?.forEach((log: { timestamp: string }) => {
    const date = format(parseISO(log?.timestamp), "dd/MM/yyyy");
    if (!result[date]) result[date] = [];
    result[date].push(log);
  });
  return result;
}, [data]);

console.log('groupedLogs',groupedLogs);

// 📅 Filter handler
const onFilterChange = (type: string, value: string) => {
  setQuery((prev) => ({
    ...prev,
    page: 1, // reset to first page
    filters: {
      ...prev.filters,
      [type]: value,
    },
  }));
  setHasMore(true); // reset hasMore for new filter
};
  return (
        <section className="flex gap-8 p-5 max-sm:flex-col overflow-hidden" style={{ height: "calc(100vh - 191px)" }}  >
      {loader && <div className="flex justify-center"><Loader/> </div>}
     <aside className="rounded border border-solid bg-stone-50 border-slate-100 w-[359px] max-sm:w-full h-full"    style={{ maxHeight: "calc(100vh - 64px)" }} >
      <nav>
    
         <button
          onClick={() => setActive("audit-trial")}
          className={`px-11 py-2.5 text-sm font-medium tracking-tight text-gray-700 w-full text-left border-l-4 ${
            active === "audit-trial" ? "border-blue-600 bg-white" : "border-transparent"
          }`}
        >
          Audit Logs
        </button>
      </nav>
    </aside>
      <section ref={containerRef} className="flex-1 overflow-y-auto pr-4" >
    <div className="p-4 space-y-4">
      {/* Date filter dropdown */}
      <div className="mb-4">
        <div className="flex justify-between items-center w-[75%]">
        <h1 className="mb-5 text-base font-bold text-sky-700">
            Audit Logs
          </h1>
          <div className="flex justify-between items-flex-start">
          <h1 className="mb-5 text-base font-bold text-gray-700 flex items-center">
           From:&nbsp; <span>
           <input
            type="date"
            onChange={(e) => onFilterChange('from', e.target.value)}
            className="border border-gray-300 bg-white rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-teal-500 w-full"
          />
          </span>&nbsp;&nbsp;
          </h1>
          <h1 className="mb-5 text-base font-bold text-gray-700 flex items-center">
           To:&nbsp;<span>
           <input
            type="date"
              max={new Date().toISOString().split("T")[0]}
      min={query.filters?.from} // prevent earlier than 'from'
            onChange={(e) => onFilterChange('to', e.target.value)}
            className="border border-gray-300 bg-white rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-teal-500 w-full"
          />
          </span>
          </h1>
                    &nbsp;&nbsp; 
                  
<Autocomplete
  size="small"
  options={userOptions}
  getOptionLabel={(option) => option.id} // shows email
  value={selectedUser}
  onChange={(event, newValue) => {
    if (newValue) {
      setSelectedUser(newValue);
      onFilterChange("userEmail", newValue.id);
    } else {
      setSelectedUser(null);
      onFilterChange("userEmail", "");
    }
  }}
  isOptionEqualToValue={(option, value) => option.id === value?.id}
  sx={{ width: 250, height:40 }}
  renderOption={(props, option) => (
    <li {...props}>
      <Tooltip title={option.id} placement="right">
        <Typography
          sx={{
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            maxWidth: '200px'
          }}
        >
          {option.id}
        </Typography>
      </Tooltip>
    </li>
  )}
  renderInput={(params) => (
    <TextField
      {...params}
      placeholder="Select User"
      variant="outlined"
      // size="small"
    />
  )}
/>
                       <div className="flex ">   <button
                   className={`px-4 py-2 mt-0 mb-5 ml-1 rounded bg-teal-500 text-white w-[125px]`}
                   onClick={() => setQuery(inital)}
                  //  disabled={visibleCount==0}
                 >
                   Reset Filter
                 </button></div>
          </div>
          </div>
    <Box >
{(Object.entries(groupedLogs)?.length!==0 && !loader )?Object.entries(groupedLogs)?.map(([date, entries]) => (
  <Box key={date} sx={{ mb: 4 }} >
    {(entries as any[])?.length!==0  &&
    <>
    <Typography className="text-[16px] text-gray-700 !font-[600]" gutterBottom>
     {date === format(new Date(), "dd/MM/yyyy") ? "Today" : date}
    </Typography>

          {(entries as any[])?.map((log:{additionalData:string,message:any,id:string,userEmail:string,action:string,entityType:string,timestamp:string}) => {
            let messages = log.message || [];

            if (
              (!messages || messages.length === 0) &&
              log.additionalData
            ) {
              try {
                const parsed = JSON.parse(log.additionalData);
                if (parsed.messages?.length) {
                  messages = parsed.messages;
                }
              } catch (err) {
                console.error("Failed to parse additionalData:", err);
              }
            }

            return (
              <Card key={`${log.id}-${log.timestamp}`} sx={{ mb: 2}} elevation={0} className="!rounded-[14px] border border-gray-300 w-[75%]">
                <CardContent className="!p-4 !rounded-[20px]">
                  <List dense className="!p-0">
                 <div className="flex justify-between">
                  <p className="text-[16px] text-gray-800 !font-semibold">
            {generateAuditTitle(log?.userEmail, log?.action, log?.entityType)} </p>
            <span className="text-[16px] text-gray-800 !font-semibold">{formatRelativeTime(log.timestamp)}</span> 
          </div>  
                   {/* {JSON.stringify(log?.userEmail)} */}
                    {messages.map((msg:string, index:number) => (
                      <ListItem key={index} disableGutters sx={{ py: 0.5 }}>
                       ● &nbsp; <ListItemText primary={msg} primaryTypographyProps={{ fontSize: 16 }} />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </Card>
            );
          })}
          </>}
        </Box>
      )):!loader && <div className="absolute top-[50%] left-[50%] text-[18px] font-[600] text-gray-700">No Audit found</div>}

    </Box>
    {loading && <div className="flex justify-center"><CircularProgress/> </div>}
    </div>
    </div>
      </section>
    </section>
  );
}

export default Page;
