/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import * as React from "react";
import ClientDataTable from "../../../../components/providerCredential/ClientDataTable";
import { useDispatch } from "react-redux";
import { setClientTable } from "@/features/client/clientSlice";
import { TableData } from "@/types/user";
import {
  convertFormJsonFromClients,
  transformedClients,
} from "@/utils/generic";
import { getTemplates } from "@/api/templates/templates";
import { setHeaders, setHeadersDefault } from "@/features/headers/headersSlice";
import Loader from "@/components/loader/loader";
import { getProviderTickets } from "@/api/ProviderCredentials/provider";
import { showToast } from "@/components/toaster/ToastProvider";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
} from "@mui/material";
// import { showToast } from "@/components/toaster/ToastProvider";

export default function AllTicketsList() {
  const dispatch = useDispatch();
  const [pagination, setPagination] = React.useState(1);
  const [loader, setLoader] = React.useState(true);
  const [fieldTypes, setFieldTypes] = React.useState<Record<string, string>>(
    {}
  );
  const [showTemplateNotAssignedPopup, setShowTemplateNotAssignedPopup] =
    React.useState(false);
  const [hasData, setHasData] = React.useState(true);
  const [query, setQuery] = React.useState({
    search: "",
    filters: {},
    sortBy: "",
    sortOrder: "asc",
    page: 1,
    limit: 10,
  });

  React.useEffect(() => {
    handleGetApi();
    console.log("each");
  }, [query]);

  const handleGetApi = (view?: any) => {
    setLoader(true);
    getTemplates({
      search: "",
      // filters: { key: "provider-credential-tickets", isActive: true },
      filters: { type: "provider_credentials", isActive: true },
    }).then((res) => {
      // Check if templates array is empty
      if (
        !res.templates?.data?.templates ||
        res.templates.data.templates.length === 0
      ) {
        setLoader(false);
        setShowTemplateNotAssignedPopup(true);
        return;
      }
      if (view == undefined) {
        // Extract field types from template
        const template = res.templates.data.templates[0];
        const extractedFieldTypes: Record<string, string> = {};

        // Parse template fields to extract field types
        if (res.templates.data.templates.length !== 0 && template?.fields) {
          let parsedFields = [];
          try {
            if (typeof template.fields === "string" && template.fields.trim()) {
              parsedFields = JSON.parse(template.fields);
            } else if (Array.isArray(template.fields)) {
              parsedFields = template.fields;
            }
          } catch (err) {
            console.error("Error parsing template fields:", err);
          }

          // Extract field types from all fields in all steps and sections
          parsedFields.forEach((step: any) => {
            if (step.sections) {
              step.sections.forEach((section: any) => {
                if (section.fields) {
                  section.fields.forEach((field: any) => {
                    if (field.label && field.field_type) {
                      // Convert label to camelCase to match column IDs
                      const fieldKey = field.label
                        .toLowerCase()
                        .replace(/\s+/g, "_")
                        .replace(/[^a-z0-9_]/g, "");
                      extractedFieldTypes[fieldKey] = field.field_type;
                      extractedFieldTypes[field.label] = field.field_type;
                    }
                  });
                }
              });
            }
          });
        } else {
          setLoader(false);
          showToast.error("Template not found, Please contact your admin");
        }

        // Add default field types for system fields
        extractedFieldTypes["status"] = "select";
        extractedFieldTypes["priority"] = "select";
        extractedFieldTypes["created_at"] = "date";
        extractedFieldTypes["updated_at"] = "date";
        extractedFieldTypes["received_date"] = "date";
        extractedFieldTypes["worked_date"] = "date";

        setFieldTypes(extractedFieldTypes);

        // Add default headers for tickets and replace Full Name with Provider Name
        const templateHeaders =
          res.templates.data.templates[0]?.view_summary?.default || [];
        const updatedTemplateHeaders = templateHeaders.map((header: string) =>
          header.toLowerCase() === "full name" ||
          header.toLowerCase() === "fullname"
            ? "Provider Name"
            : header
        );

        const defaultHeaders = [
          "Priority",
          // "Ticket ID",
          "Type",
          "allocated_type",
          "userAgentName",
          "received_date",
          "Worked Date",
          "status",
          ...updatedTemplateHeaders,
        ];

        // Set initial visible headers to include the new default headers
        const templateInGridHeaders =
          res.templates.data.templates[0]?.view_summary?.inGrid || [];
        const updatedInGridHeaders = templateInGridHeaders.map(
          (header: string) =>
            header.toLowerCase() === "full name" ||
            header.toLowerCase() === "fullname"
              ? "Provider Name"
              : header
        );

        const initialHeaders = [
          "Priority",
          // "Ticket ID",
          "Type",
          "allocated_type",
          "userAgentName",
          "received_date",
          "Worked Date",
          "status",
          "ticketId",
          ...updatedInGridHeaders,
        ];

        dispatch(setHeaders(initialHeaders));
        dispatch(setHeadersDefault(defaultHeaders as any));
      }
      const result = Object.fromEntries(
        res.templates.data.templates[0]?.view_summary?.inGrid.map(
          (key: any) => [key, 1]
        )
      );
      result.priority = 1;
      result.type = 1;
      result.allocation_type = 1;
      result.user_agent = 1;
      result.received_date = 1;
      result.worked_date = 1;
      result.status = 1;
      result.ticketId = 1;
      result.assign = 1;

      const payload = {
        ...query,
        selectedFields: result,
      };
      getProviderTickets(payload)
        .then((res) => {
          const data = transformedClients(res.providerTickets.provider);
          const tableData = convertFormJsonFromClients(data);

          // Check if there's any data
          setHasData(data && data.length > 0);

          dispatch(setClientTable(tableData as TableData));
          setPagination(res.providerTickets.pagination);
          setLoader(false);
        })
        .catch((err) => {
          setLoader(false);
          setHasData(false);
          console.error(err);
        });
    });
  };

  return (
    <>
      {loader && <Loader />}

      {/* Template Not Assigned Popup */}
      <Dialog
        open={showTemplateNotAssignedPopup}
        onClose={() => setShowTemplateNotAssignedPopup(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Typography variant="h6" component="div" sx={{ color: "#d32f2f" }}>
            Template Not Assigned
          </Typography>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" sx={{ mb: 2 }}>
            Template is not assigned to you so please contact your admin.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setShowTemplateNotAssignedPopup(false)}
            variant="contained"
            color="primary"
          >
            OK
          </Button>
        </DialogActions>
      </Dialog>

      {!showTemplateNotAssignedPopup && (
        <ClientDataTable
          title={"Provider Credentials"}
          handleGetApi={handleGetApi}
          setQuery={setQuery}
          query={query}
          pagination={pagination}
          fieldTypes={fieldTypes}
          hasData={hasData}
        />
      )}
    </>
  );
}
