/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import * as React from "react";
// import ClientDataTable from "../../../components/providerCredential/clientData.json";
import ClientDataTable from "../../../../components/providerCredential/ClientDataTable";
import { useDispatch } from "react-redux";
import { setClientTable } from "@/features/client/clientSlice";
import { TableData } from "@/types/user"; // Update with your actual types
// import { form } from "./create/form";
import { getOrganizationUser } from "@/api/organizations/organizations";
import {
  convertFormJsonFromClients,
  transformedClients,
} from "@/utils/generic";
import { getTemplates } from "@/api/templates/templates";
import { setHeaders, setHeadersDefault } from "@/features/headers/headersSlice";
import Loader from "@/components/loader/loader";

export default function CompletedList() {
  const dispatch = useDispatch();
  const [pagination, setPagination] = React.useState(1);
  const [loader, setLoader] = React.useState(false);
  const [query, setQuery] = React.useState({
    search: "",
    filters: { type: "main_client" },
    sortBy: "",
    sortOrder: "asc",
    page: 1,
    limit: 10,
  });

  React.useEffect(() => {
    handleGetApi();
    console.log("each");
  }, [query]);

  const handleGetApi = (view?: any) => {
    setLoader(true);
    getTemplates({ search: "", filters:{key:"organization", type:'Master',isActive:true}}).then((res) => {
      if (view == undefined) {
        dispatch(
          setHeaders(res.templates.data.templates[0]?.view_summary?.inGrid)
        );
        dispatch(
          setHeadersDefault(
            res.templates.data.templates[0]?.view_summary?.default
          )
        );
      }
      const result = Object.fromEntries(
        res.templates.data.templates[0]?.view_summary?.inGrid.map(
          (key: any) => [key, 1]
        )
      );
      const payload = {
        input: { ...query, ["selectedFields"]: result },
      };
      getOrganizationUser(payload)
        .then((res) => {
          const data = transformedClients(res.getUsersWithPagination.users);
          const tableData = convertFormJsonFromClients(data);
          dispatch(setClientTable(tableData as TableData));
          setPagination(res.getUsersWithPagination.pagination);
          setLoader(false);
        })

        .catch((err) => {
          setLoader(false);
          console.error(err);
        });
    });
  };
  return (
    <>
      {loader && <Loader />}
      <ClientDataTable
        title={"Completed List"}
        handleGetApi={handleGetApi}
        setQuery={setQuery}
        query={query}
        pagination={pagination}
      />
    </>
  );
}
