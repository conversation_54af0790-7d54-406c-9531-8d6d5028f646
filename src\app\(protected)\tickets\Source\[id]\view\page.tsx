"use client";
import { useParams } from "next/navigation";
import React from "react";
import TicketView from "../../ticketView";

export default function SourcePage() {
  const params = useParams();
  const id = Array.isArray(params?.id) ? params?.id[0] : params?.id;

  // const [pagination, setPagination] = React.useState();
  // const [loader, setLoader] = React.useState(false);
  //   const [providerEmailTicket,setproviderEmailTicket] = useState()
  // You can use this helper if needed
  // const getOrganisationId = (id: string | string[] | undefined): string | undefined => {
  //   if (Array.isArray(id)) return id[0];
  //   return id;
  // };



  return <>{id && <TicketView id={id} />}</>;
}
