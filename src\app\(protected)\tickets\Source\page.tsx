/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import * as React from "react";
// import ClientDataTable from "../../../components/providerCredential/clientData.json";
import ClientDataTable from "../../../../components/providerCredential/ClientDataTable";
import { useDispatch } from "react-redux";
import {  setClientTable } from "@/features/client/clientSlice";
// import { TableData } from "@/types/user"; // Update with your actual types
// import { form } from "./create/form";
// import { getOrganizationUser } from "@/api/organizations/organizations";
import {
  convertFormJsonFromClients,
  // transformedClients,
} from "@/utils/generic";
// import { getTemplates } from "@/api/templates/templates";
import { setHeaders, setHeadersDefault } from "@/features/headers/headersSlice";
import Loader from "@/components/loader/loader";
import { getSourceList } from "@/api/tickets/source";



export default function SourcePage() {
  const dispatch = useDispatch();
  const [pagination, setPagination] = React.useState(1);
  const [loader, setLoader] = React.useState(false);
  const [query, setQuery] = React.useState({
    search: "",
    // filters: '',
    sortBy: "",
    sortOrder: "asc",
    page: 1,
    limit: 10,
  });

  React.useEffect(()=>{
    // dispatch(clearClientTable());
    dispatch(setHeaders(['']))
    dispatch(setHeadersDefault([''] as any))
  },[])
  

  React.useEffect(() => {
    handleGetApi();
    console.log("each");
  }, [query]);

  const handleGetApi = (view?: any) => {
    setLoader(true);
    // getTemplates({ search: "Organizations" }).then((res) => {
    //   if (view == undefined) {
    //     dispatch(
    //       setHeaders(res.templates.data.templates[0]?.view_summary?.inGrid)
    //     );
    //     dispatch(
    //       setHeadersDefault(
    //         res.templates.data.templates[0]?.view_summary?.default
    //       )
    //     );
    //   }
    //   const result = Object.fromEntries(
    //     res.templates.data.templates[0]?.view_summary?.inGrid.map(
    //       (key: any) => [key, 1]
    //     )
    //   );
    //   const payload = {
    //     input: { ...query, ["selectedFields"]: result },
    //   };
    //   getOrganizationUser(payload)
    //     .then((res) => {
    //       const data = transformedClients(res.getUsersWithPagination.users);
    //       const tableData = convertFormJsonFromClients(data);
    //       dispatch(setClientTable(tableData as TableData));
    //       setPagination(res.getUsersWithPagination.pagination);
    //       setLoader(false);
    //     })

    //     .catch((err) => {
    //       setLoader(false);
    //       console.error(err);
    //     });
    // });
    console.log(view)
    

     

              //     const data = transformedClients(ticketTableData);
              // const tableData = convertFormJsonFromClients(data);
              //     dispatch(setClientTable(tableData as TableData));

                const payload = {
                    input:query
                  }
                 getSourceList(payload)
                .then((res) => {
                  console.log(res, 'res.getUsersWithPagination.users');

                  const defaultHeaders = [
                            "priority", "type", "subject","createdAt","updatedAt","status"
                          ];
                  
                          // Set initial visible headers to include the new default headers
                         
                  
                          const initialHeaders = [
                            "priority", "type", "subject","createdAt","updatedAt","status"
                          ];
                  
                          dispatch(setHeaders(initialHeaders));
                          dispatch(setHeadersDefault(defaultHeaders as any));

                          const transformedClients = res?.providerEmailTickets?.providerEmailTickets.map((item:any) => ({
                            _id: item._id,
                            values: JSON.stringify(item)
                          }));
                          
                          // Step 2: Convert to table-ready format
                          const tableData = convertFormJsonFromClients(transformedClients);
                            // const tableData = convertFormJsonFromClients(data);
                            console.log('table data11111',tableData,transformedClients,res?.providerEmailTickets?.providerEmailTickets)
                            dispatch(setClientTable(tableData as any));
                      // dispatch(setClientTable(tableData as TableData));
                      setPagination(res?.providerEmailTickets?.pagination)
                      setLoader(false)
                 
                })
                
                .catch((err) => {
                  setLoader(false)
                  console.error(err);
                });
              }
  return (
    <>
      {loader && <Loader />}
      <ClientDataTable
        title={"Source List"}
        handleGetApi={handleGetApi}
        setQuery={setQuery}
        query={query}
        pagination={pagination}
      />
    </>
  );
}
