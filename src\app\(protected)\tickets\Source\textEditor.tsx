import React from 'react';
// import ClassicEditor from '@ckeditor/ckeditor5-build-classic';
import { CKEditor, useCKEditorCloud } from '@ckeditor/ckeditor5-react';

const CustomCKEditor = ({
  value,
  onChange,
}: {
  value: string;
  onChange: (v: string) => void;
}) => {

  const cloud = useCKEditorCloud( {
    version: '46.0.0',
    premium: true
} );

if ( cloud.status === 'error' ) {
    return <div>Error!</div>;
}

if ( cloud.status === 'loading' ) {
    return <div>Loading...</div>;
}

const {
    ClassicEditor,
    Essentials,
    Paragraph,
    Bold,
    Italic,
    List
} = cloud.CKEditor;

// const { FormatPainter } = cloud.CKEditorPremiumFeatures;

  return (
    <div className="ckeditor-wrapper">
      <CKEditor
        editor={ClassicEditor}
        data={value}
        onChange={(event, editor) => {
          const data = editor.getData();
          onChange(data);
        }}
        

        config={ {
          licenseKey: 'eyJhbGciOiJFUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.ViIYa_ny9YKigYc9Na78z0dm0WitmWCS_ryGeHVOXpC0L7f2LEvXEMn_-3wdDelD97cO-pzA2x_Lsa7yBCl7SA',
          plugins: [ Essentials, Paragraph, Bold, Italic,List ],
          toolbar: [
            'heading',
            '|',
            'fontFamily',
            'fontSize',
            'bold',
            'italic',
            'underline',
            'strikethrough',
            'subscript',
            'superscript',
            '|',
            'alignment',
            'bulletedList',
            'numberedList',
            'blockQuote',
            'insertTable',
            'undo',
            'redo',
            'removeFormat',
          ],
      } }
      />
    </div>
  );
};

export default CustomCKEditor;
