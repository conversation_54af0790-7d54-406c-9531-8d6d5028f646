"use client";

import React, { useEffect, useState } from "react";
// import { FaPaperclip } from "react-icons/fa";
// import AttachFileIcon from '@mui/icons-material/AttachFile';
import { format } from "date-fns";
import CustomTinyMCEEditor from "./textEditor";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import {
  createProviderTicket,
  getSourceById,
  replyEmailThread,
  UpdateProviderTicket,
} from "@/api/tickets/source";
import { useRouter } from "next/navigation";
import Loader from "@/components/loader/loader";
import uploadIcon from "../../../../assests/Upload.svg";
import { showToast } from "@/components/toaster/ToastProvider";
import Image, { StaticImageData } from "next/image";
import { RootState } from "@/store";
import { useSelector } from "react-redux";
import pdfIcon from "../../../../assests/pdfSVG.jpg";
import imageIcon from "../../../../assests/jpegIcon.png";
import docIcon from "../../../../assests/msWordIcon.png";
import txtIcon from "../../../../assests/textIcon.png";
import excelIcon from "../../../../assests/excelIcon.png";
import zipIcon from "../../../../assests/zipIcon.png";
import defaultIcon from "../../../../assests/fileIcon.png";
import { getImagedUrl, getPreSignedUrl } from "@/api/file/file";
import {
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  MenuItem,
  TextField,
} from "@mui/material";
import { v4 as uuidv4 } from "uuid";

export interface ProviderEmailTicket {
  _id: string;
  subject: string;
  description: string;
  type: "email" | "ticket" | null;
  priority: string;
  createdAt: string;
  updatedAt: string;
  values: {
    ticketType: string;
    priority: string;
    subject: string;
    description: string;
    enrollementType: string;
    jobRequest: string;
    providerNPI: string;
    payorName: string;
    state: string;
  };
  isAllocated: boolean;
  conversationId: string;
  status: string | null;
  messages: {
    from: string;
    to: string;
    body: string;
    date: string;
    reason?: string;
    message?: string | null;
    attachments?: {
      fileName: string;
      url: string;
      uploadedAt: string;
      type: string;
      __typename: string;
    }[];
    messageId?: string;
  }[];
}

const TicketView = ({ id }: { id: string }) => {
  const fileInputRef = React.useRef<HTMLInputElement | null>(null);
  // const params = useParams();
  const fromEmail = useSelector((state: RootState) => state.user.email);
  // const id = Array.isArray(params?.id) ? params?.id[0] : params?.id;
  const [loader, setLoader] = React.useState(false);
  const [priority, setPriority] = useState("low");
  const router = useRouter();
  const [closeDialogOpen, setCloseDialogOpen] = useState(false);
  const [closeReason, setCloseReason] = useState("");
  const handleFileClick = () => {
    fileInputRef.current?.click();
  };
  const [data, setData] = useState<ProviderEmailTicket | null>(null);
  // const [loading, setLoading] = useState(true);

  useEffect(() => {
    getSource();
  }, []);

  const getSource = () => {
    if (!id) return;
    setLoader(true);
    getSourceById({ id })
      .then((res) => {
        setData(res?.providerEmailTicket);
        setPriority(res?.providerEmailTicket?.priority ?? "Low");
        setLoader(false);
      })

      .catch((err) => {
        setLoader(false);
        console.error(err);
      });
  };

  // const toCamelCase = (str: string): string => {
  //   return str
  //     .replace(/[^a-zA-Z0-9 ]/g, '') // remove non-alphanumeric except spaces
  //     .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) =>
  //       index === 0 ? word.toLowerCase() : word.toUpperCase()
  //     )
  //     .replace(/\s+/g, '');
  // };

  // Example usage inside your reducer
  // const flattenedValues: Record<string, string> = data?.values
  //   ? data.values.reduce((acc, item) => {
  //       const camelKey = toCamelCase(item.field);
  //       acc[camelKey] = item.value;
  //       return acc;
  //     }, {} as Record<string, string>)
  //   : {};

  const getIconForType = (type: string, fileName: string): StaticImageData => {
    const ext = fileName?.split(".").pop()?.toLowerCase() || "";

    if (type.includes("pdf") || ext === "pdf") return pdfIcon;
    if (
      type.includes("image") ||
      ["png", "jpg", "jpeg", "gif", "webp"].includes(ext)
    )
      return imageIcon;
    if (
      type.includes("msword") ||
      type.includes("officedocument.wordprocessingml") ||
      ["doc", "docx"].includes(ext)
    )
      return docIcon;
    if (["txt", "text"].includes(ext) || type.includes("text/plain"))
      return txtIcon;
    if (
      type.includes("excel") ||
      type.includes("spreadsheetml") ||
      ["xls", "xlsx", "csv"].includes(ext)
    )
      return excelIcon;
    if (["zip", "rar", "7z", "tar", "gz"].includes(ext)) return zipIcon;

    return defaultIcon;
  };

  const handleCloseTicket = (reason?: string) => {
    setLoader(true);
    const to =
      (data?.type ?? "").toLowerCase() === "email"
        ? data?.messages[0]?.from || ""
        : data?.messages[0]?.to || "";
    const payload = {
      input: {
        id: id ?? "",
        status: "Closed",
        pushNotification: "client",
        messages: [
          {
            from: fromEmail,
            to: to,
            date: new Date(),
            reason: reason ?? "",
          },
        ],
      },
    };
    UpdateProviderTicket(payload)
      .then((res) => {
        // setLoader(false)
        if (res) {
          setLoader(false);
          console.log(res);
          showToast.success("Ticket Closed Successfully");
          router.push(`/tickets/Source/`);
          // const payLoad = {
          //   input:{
          //     source_ticket_id:id,
          //     flattenedValues: data?.values,
          //     type: data?.type?.toUpperCase() ?? "TICKET",
          //     templateId: '64f8c3f0e93d4b0012f9a123',
          //     status: 'NEW'
          //     // ticketId: string;
          //   }
          // }
          // createProviderTicket(payLoad)
          // .then((data) => {
          //   setLoader(false)
          //   console.log(data)
          //   showToast.success('Allocated Successfully')
          //   router.push(`/tickets/Source/`)
          // })

          // .catch((err) => {
          //   setLoader(false)
          //   console.error(err);
          //   showToast.error(err?.error?.message)
          // });
        }
      })

      .catch((err) => {
        setLoader(false);
        console.error(err);
        showToast.error(err?.error?.message);
      });
  };

  const updateTicketpriority = (value?: string) => {
    const payload = {
      input: {
        id: id ?? "",
        priority: value ? value : priority ? priority : "Low",
      },
    };

    UpdateProviderTicket(payload)
      .then((res) => {
        // setLoader(false)
        if (res) {
          setLoader(false);
          console.log(res);
          showToast.success("Priority Updated");
        }
      })

      .catch((err) => {
        setLoader(false);
        console.error(err);
        showToast.error(err?.error?.message);
      });
  };

  const handleOpenTicket = (reason?: string) => {
    setLoader(true);
    const to =
      (data?.type ?? "").toLowerCase() === "email"
        ? data?.messages[0]?.from || ""
        : data?.messages[0]?.to || "";
    const payload = {
      input: {
        id: id ?? "",
        status: "Reopen",
        pushNotification: "client",
        messages: [
          {
            from: fromEmail,
            to: to,
            date: new Date(),
            reason: reason ?? "",
          },
        ],
      },
    };
    UpdateProviderTicket(payload)
      .then((res) => {
        // setLoader(false)
        if (res) {
          setLoader(false);
          console.log(res);
          showToast.success("Ticket Re Opened Successfully");
          router.push(`/tickets/Source/`);
          // const payLoad = {
          //   input:{
          //     source_ticket_id:id,
          //     flattenedValues: data?.values,
          //     type: data?.type?.toUpperCase() ?? "TICKET",
          //     templateId: '64f8c3f0e93d4b0012f9a123',
          //     status: 'NEW'
          //     // ticketId: string;
          //   }
          // }
          // createProviderTicket(payLoad)
          // .then((data) => {
          //   setLoader(false)
          //   console.log(data)
          //   showToast.success('Allocated Successfully')
          //   router.push(`/tickets/Source/`)
          // })

          // .catch((err) => {
          //   setLoader(false)
          //   console.error(err);
          //   showToast.error(err?.error?.message)
          // });
        }
      })

      .catch((err) => {
        setLoader(false);
        console.error(err);
        showToast.error(err?.error?.message);
      });
  };

  const handleSubmit = () => {
    setLoader(true);

    const payload = {
      input: {
        id: id ?? "",
        status: "Open",
        pushNotification: "client",
      },
    };
    UpdateProviderTicket(payload)
      .then((res) => {
        // setLoader(false)
        if (res) {
          const payLoad = {
            input: {
              source_ticket_id: id,
              flattenedValues: data?.values,
              type: data?.type?.toUpperCase() ?? "TICKET",
              templateId: "64f8c3f0e93d4b0012f9a123",
              status: "NEW",
              // ticketId: string;
            },
          };
          createProviderTicket(payLoad)
            .then((data) => {
              setLoader(false);
              console.log(data);
              showToast.success("Allocated Successfully");
              router.push(`/tickets/Source/`);
            })

            .catch((err) => {
              setLoader(false);
              console.error(err);
              showToast.error(err?.error?.message);
            });
        }
      })

      .catch((err) => {
        setLoader(false);
        console.error(err);
        showToast.error(err?.error?.message);
      });
  };

  const replytoEmailThread = () => {
    console.log(data?.messages);
    const to =
      (data?.type ?? "").toLowerCase() === "email"
        ? data?.messages[0]?.from || ""
        : data?.messages[0]?.to || "";

    const payLoad = {
      input: {
        conversationId: data?.conversationId ? data?.conversationId : "",
        from: fromEmail,
        to: to,
        body: editor,
        attachments: attachments,
        messageId: data?.messages[data.messages.length - 1]?.messageId ?? "",
        pushNotification: "client",
      },
    };

    replyEmailThread(payLoad)
      .then((data1) => {
        setLoader(false);
        setEditor("");
        setAttachments([]);
        getSource();
        showToast.success(data1?.code || `${data?.type} Sent Successfully`);
        // router.push(`/tickets/Source/`)
      })

      .catch((err) => {
        setLoader(false);
        console.error(err);
        showToast.error(err?.error?.message);
      });
  };

  // const [description, setDescription] = useState("");
  const [uploaded, setupload] = useState(false);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [attachments, setAttachments] = useState<any[]>([]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      uploadFiles(files);
    }
  };

  const fetchImageUrl = async (filename: string) => {
    try {
      const res = await getImagedUrl({ filename });
      console.log(res.generateViewUrl.data.viewUrl);

      // Optional fetch check if you want:
      const response = await fetch(res.generateViewUrl.data.viewUrl, {
        method: "GET",
        headers: {
          "Content-Type": "image/png",
        },
      });
      console.log(response);
      window.open(res.generateViewUrl.data.viewUrl, "_blank");
      // setViewUrl(res.generateViewUrl.data.viewUrl);
    } catch (err) {
      console.error(err);
      // setError('Failed to load image');
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files) {
      uploadFiles(files);
    }
  };

  const uploadFiles = async (files: FileList) => {
    setupload(true);

    try {
      const uploadedAttachments = await Promise.all(
        Array.from(files).map(async (file) => {
          const uuid = uuidv4(); // generate a new UUID
          const fileExtension = file.name.split(".").pop(); // get the extension
          const path = `provider/ticket/${uuid}.${fileExtension}`;
          const payload = {
            input: {
              filename: path,
              contentType: file.type || "application/octet-stream",
            },
          };

          const uploadPath = await getPreSignedUrl(payload);
          const uploadUrl = uploadPath.generateUploadUrl.data.uploadUrl;

          const uploadResponse = await fetch(uploadUrl, {
            method: "PUT",
            body: file,
            headers: {
              "Content-Type": file.type || "*",
            },
          });

          if (!uploadResponse.ok) {
            throw new Error(`Upload failed for ${file.name}`);
          }

          return {
            fileName: file.name,
            url: uploadUrl.split("?")[0],
            uploadedAt: new Date().toISOString(),
            type: file.type?.split("/")[1] || "unknown",
          };
        })
      );

      setAttachments((prev) => [...prev, ...uploadedAttachments]);
    } catch (err) {
      console.error("One or more uploads failed:", err);
    } finally {
      setupload(false);
    }
  };

  const removeFile = (index: number) => {
    const updated = [...attachments];
    URL.revokeObjectURL(updated[index].url); // Clean memory
    updated.splice(index, 1);
    setAttachments(updated);
  };

  // const { type, subject, messages } = data;
  const [editor, setEditor] = useState(``);
  console.log(`data1111111111`, data);

  // const renderMessage = (msg: Message, index: number) => (
  //   <div key={index} className="mb-6">
  //     <div className="text-sm font-semibold">{msg.from}</div>
  //     <div className="text-gray-600 text-sm mb-1">{format(new Date(msg.date), "PPpp")}</div>
  //     <div className="bg-gray-50 p-3 rounded shadow-sm text-sm whitespace-pre-line">
  //       {msg.body}
  //     </div>

  //     {msg.attachments && msg.attachments?.length > 0 && (
  //       <div className="mt-2 flex flex-wrap gap-2">
  //         {msg.attachments.map((att, idx) => (
  //           <a
  //             key={idx}
  //             href={att}
  //             target="_blank"
  //             rel="noopener noreferrer"
  //             className="flex items-center text-blue-600 text-sm hover:underline"
  //           >
  //             <AttachFileIcon className="mr-1" /> {att.split("/").pop()}
  //           </a>
  //         ))}
  //       </div>
  //     )}
  //   </div>
  // );

  const renderEmailView = () => (
    <div className="flex flex-col lg:flex-row gap-6">
      {/* Left Panel: Email Details */}
      <div className="lg:w-1/2 bg-white rounded shadow p-5 ">
        <h2 className="font-bold text-[16px] text-[#2B3674] mb-2">
          Email Details
        </h2>
        <p className="font-[500] text-[18px] font-sans text-[#414A5B] mb-4">
          {data?.subject}
        </p>

        <div className="max-h-[calc(90vh-345px)] overflow-y-auto">
          {data?.messages.map((msg, idx) => (
            <div key={idx} className="border-b border-[#DDDDDD] p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <AccountCircleIcon fontSize="large" color="disabled" />
                  <p className="flex flex-col text-sm text-gray-700 ml-2">
                    <span className="font-medium text-[16px]">{msg.from}</span>
                    <span className="font-medium text-[12px]">
                      to {msg.to ? msg.to : `<EMAIL>`}
                      <ArrowDropDownIcon />
                    </span>
                  </p>
                </div>

                <p className="text-xs text-gray-500">
                  {format(new Date(msg.date), "eee, MMM d, h:mm a")}
                </p>
              </div>
              <div className="font-[500] text-[14px] text-[#414A5B] mb-2 whitespace-pre-line">
                {/<[a-z][\s\S]*>/i.test(msg.body) ? (
                  <div dangerouslySetInnerHTML={{ __html: msg.body }} />
                ) : (
                  msg.body
                )}
              </div>

              {msg.attachments &&
                Array.isArray(msg.attachments) &&
                msg.attachments.length > 0 && (
                  <div className="flex items-center gap-2 text-sm mt-2">
                    <div className="text-blue-600 flex flex-col">
                      {msg.attachments.map((attachment, aIdx) => {
                        const safeUrl =
                          typeof attachment?.url === "string"
                            ? attachment.url
                            : "#";
                        const fileName =
                          attachment?.fileName ||
                          attachment?.url?.split("/").pop() ||
                          "Unknown File";
                        return (
                          <div
                            onClick={() => fetchImageUrl(safeUrl)}
                            className="flex items-center gap-2 text-sm mt-2 border rounded border-[#DDDDDD] p-4 cursor-pointer"
                            key={aIdx}
                          >
                            <Image
                              src={getIconForType(
                                attachment.type,
                                attachment.fileName
                              )}
                              alt=""
                              width={20}
                              height={20}
                            />
                            <p key={aIdx} className="text-black">
                              {fileName}
                            </p>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
            </div>
          ))}
        </div>
      </div>

      {/* Right Panel: Reply */}
      <div className="lg:w-1/2 bg-white rounded shadow p-5 flex flex-col">
        {/* Header with title and buttons */}
        <div className="flex justify-between items-center mb-2">
          <h2 className="font-bold text-[16px] text-[#2B3674]">Reply</h2>
          <div className="flex gap-2">
            {data && data?.status !== `Open` && data?.status !== `closed` && (
              <button
                onClick={() => setCloseDialogOpen(true)}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-3 rounded text-sm"
              >
                Close
              </button>
            )}
            {data && data?.status === `Closed` && (
              <button
                onClick={() => setCloseDialogOpen(true)}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-3 rounded text-sm"
              >
                Re Open
              </button>
            )}
            {data && data?.status !== `Open` && data?.status !== `closed` && (
              <button
                onClick={handleSubmit}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded text-sm"
              >
                Ready for Allocation
              </button>
            )}
          </div>
        </div>

        <div className="mb-4 min-h-[10rem] p-2">
          <CustomTinyMCEEditor value={editor} onChange={setEditor} />
        </div>

        {/* Attachment section */}
        <input
          type="file"
          className="hidden"
          ref={fileInputRef}
          onChange={handleFileChange}
        />
        <div
          className="flex flex-col items-center border border-dashed p-6 text-center text-sm text-gray-500 mb-4 rounded cursor-pointer hover:border-blue-400"
          onClick={handleFileClick}
          onDragOver={(e) => e.preventDefault()}
          onDrop={(e) => {
            e.preventDefault();
            handleDrop(e);
          }}
        >
          <Image src={uploadIcon} alt="Logo" />
          <p className="mb-1">
            Upload a{" "}
            <span className="text-blue-600 underline cursor-pointer">file</span>{" "}
            or drag and drop
          </p>
          <p className="text-xs text-gray-400">PNG, JPG, PDF up to 10MB</p>
        </div>

        <div className="space-y-3">
          {uploaded && (
            <div className="text-center">
              <CircularProgress />
            </div>
          )}
          {attachments.map((file, index) => (
            <div
              key={index}
              onClick={() => fetchImageUrl(file.url)}
              className="flex items-center gap-4 border border-[#DDD] rounded px-4 py-2 cursor-pointer"
            >
              <Image
                src={getIconForType(file.type, file.fileName)}
                alt="file"
                width={24}
                height={24}
              />
              <div className="flex-1 overflow-hidden text-ellipsis whitespace-nowrap">
                <p className="text-black-600">{file.fileName}</p>
              </div>
              <button
                onClick={() => removeFile(index)}
                className="text-red-600 text-sm hover:underline"
              >
                Remove
              </button>
            </div>
          ))}
        </div>

        {/* Send Button */}
        <div className="text-right mt-3">
          <button
            disabled={!editor || editor === ""}
            onClick={replytoEmailThread}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white px-6 py-2 rounded"
          >
            Send
          </button>
        </div>
      </div>
    </div>
  );

  const renderTicketView = () => (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 p-6">
      {/* Left - Ticket Details */}
      <div className="bg-white rounded shadow-md">
        <h2 className="text-[16px] font-bold text-[#2B3674] p-4">
          Ticket Details
        </h2>

        {/* Basic Fields */}
        <div className="">
          <h3 className="font-dm font-[600] text-sm text-[14px] bg-[#FAFAFA] p-4">
            Basic Fields
          </h3>
          {data && (
            <div className="p-4 grid grid-cols-2 md:grid-cols-5 gap-y-6 gap-x-6 text-sm font-dm text-[#2B3674]">
              <div className="flex flex-col">
                <span className="font-dm font-[600] text-sm leading-[21px] tracking-normal align-middle text-[#222222] text-[14px]">
                  Ticket Type
                </span>
                <span className="font-dm font-[400] text-sm leading-[24px] tracking-normal text-[#414A5B] text-[14px]">
                  {data.type || "Provider Credentials"}
                </span>
              </div>

              <div className="flex flex-col">
                <span className="font-dm font-[600] text-sm leading-[21px] tracking-normal align-middle text-[#222222] text-[14px]">
                  Priority
                </span>
                <span className="font-dm font-[400] text-sm leading-[24px] tracking-normal text-[#414A5B] text-[14px]">
                  {data.priority}
                </span>
              </div>

              <div className="flex flex-col">
                <span className="font-dm font-[600] text-sm leading-[21px] tracking-normal align-middle text-[#222222] text-[14px]">
                  Status
                </span>
                <span className="font-dm font-[400] text-sm leading-[24px] tracking-normal text-[#414A5B] text-[14px]">
                  {data.status}
                </span>
              </div>

              <div className="flex flex-col">
                <span className="font-dm font-[600] text-sm leading-[21px] tracking-normal align-middle text-[#222222] text-[14px]">
                  Subject
                </span>
                <span className="font-dm font-[400] text-sm leading-[24px] tracking-normal text-[#414A5B] text-[14px]">
                  {data.subject}
                </span>
              </div>

              <div className="col-span-2 flex flex-col mt-1">
                <span className="font-dm font-[600] text-sm leading-[21px] tracking-normal align-middle text-[#222222] text-[14px]">
                  Description
                </span>
                <p className="font-dm font-[400] text-sm leading-[24px] tracking-normal text-[#414A5B] text-[14px]">
                  {data?.description}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Process Fields */}
        <div className="border-b border-[#DDDDDD]">
          <h3 className="font-dm font-[600] text-sm text-[14px] bg-[#FAFAFA] p-4">
            Process Fields
          </h3>

          <div className="p-4 grid grid-cols-2 md:grid-cols-5 gap-y-6 gap-x-6 text-sm mt-3">
            {/* Each Field */}
            <div className="flex flex-col">
              <span className="font-dm font-[600] text-[14px] leading-[21px] text-[#222222]">
                Enrollement Type
              </span>
              <span className="font-dm font-[400] text-[14px] leading-[24px] text-[#414A5B]">
                {data?.values?.enrollementType}
              </span>
            </div>

            <div className="flex flex-col">
              <span className="font-dm font-[600] text-[14px] leading-[21px] text-[#222222]">
                Job Request
              </span>
              <span className="font-dm font-[400] text-[14px] leading-[24px] text-[#414A5B]">
                {data?.values?.jobRequest}
              </span>
            </div>

            <div className="flex flex-col">
              <span className="font-dm font-[600] text-[14px] leading-[21px] text-[#222222]">
                Provider NPI
              </span>
              <span className="font-dm font-[400] text-[14px] leading-[24px] text-[#414A5B]">
                {data?.values?.providerNPI}
              </span>
            </div>

            <div className="flex flex-col">
              <span className="font-dm font-[600] text-[14px] leading-[21px] text-[#222222]">
                Payor Name
              </span>
              <span className="font-dm font-[400] text-[14px] leading-[24px] text-[#414A5B]">
                {data?.values?.payorName}
              </span>
            </div>

            <div className="flex flex-col">
              <span className="font-dm font-[600] text-[14px] leading-[21px] text-[#222222]">
                State
              </span>
              <span className="font-dm font-[400] text-[14px] leading-[24px] text-[#414A5B]">
                {data?.values?.state}
              </span>
            </div>

            <div className="flex flex-col">
              <span className="font-dm font-[600] text-[14px] leading-[21px] text-[#222222]">
                Attachment
              </span>
              <span className="font-dm font-[400] text-[14px] leading-[24px] text-[#414A5B]"></span>
            </div>
          </div>
        </div>

        {/* Communication Log */}
        <div>
          <h3 className="bg-[#7592F90D] px-4 py-2 text-[16px] font-bold text-[#2B3674]">
            Communication Log
          </h3>
          <div className="max-h-64 min-h-30 overflow-y-auto">
            {data &&
              data.messages.length > 0 &&
              data.messages.map((msg, idx) => (
                <div key={idx} className="border-b border-[#DDDDDD] p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <AccountCircleIcon fontSize="large" color="disabled" />
                      <p className="flex flex-col text-sm text-gray-700 ml-2">
                        <span className="font-medium text-[16px]">
                          {msg.from}
                        </span>
                        <span className="font-medium text-[12px]">
                          to {msg.to}
                          <ArrowDropDownIcon />
                        </span>
                      </p>
                    </div>

                    <p className="text-xs text-gray-500">
                      {format(new Date(msg.date), "eee, MMM d, h:mm a")}
                    </p>
                  </div>
                  <div className="font-[500] text-[14px] text-[#414A5B] mb-2 whitespace-pre-line">
                    {(() => {
                      const content = msg.reason || msg.body;
                      return /<[a-z][\s\S]*>/i.test(content) ? (
                        <div dangerouslySetInnerHTML={{ __html: content }} />
                      ) : (
                        content
                      );
                    })()}
                  </div>

                  {msg.attachments &&
                    Array.isArray(msg.attachments) &&
                    msg.attachments.length > 0 && (
                      <div className="flex items-center gap-2 text-sm mt-2">
                        <div className="text-blue-600 flex flex-col">
                          {msg.attachments.map((attachment, aIdx) => {
                            const safeUrl =
                              typeof attachment?.url === "string"
                                ? attachment.url
                                : "#";
                            const fileName =
                              attachment?.fileName ||
                              attachment?.url?.split("/").pop() ||
                              "Unknown File";
                            return (
                              <div
                                onClick={() => fetchImageUrl(safeUrl)}
                                className="flex items-center gap-2 text-sm mt-2 border rounded border-[#DDDDDD] p-4 cursor-pointer"
                                key={aIdx}
                              >
                                <Image
                                  src={getIconForType(
                                    attachment.type,
                                    attachment.fileName
                                  )}
                                  alt=""
                                  width={20}
                                  height={20}
                                />
                                <p key={aIdx} className="text-black">
                                  {fileName}
                                </p>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    )}
                </div>
              ))}
          </div>
        </div>
      </div>

      {/* Right - Reply */}
      <div className="bg-white rounded shadow-md flex flex-col p-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-[16px] font-bold text-[#2B3674]">Reply</h2>
          <div className="flex gap-2">
            {data && data?.status !== `Open` && data?.status !== `closed` && (
              <button
                onClick={() => setCloseDialogOpen(true)}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-3 rounded text-sm"
              >
                Close
              </button>
            )}
            {data && data?.status === `Closed` && (
              <button
                onClick={() => setCloseDialogOpen(true)}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-3 rounded text-sm"
              >
                Re Open
              </button>
            )}
            {data && data?.status !== `Open` && data?.status !== `closed` && (
              <button
                onClick={handleSubmit}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded text-sm"
              >
                Ready for Allocation
              </button>
            )}
          </div>
        </div>

        <label className="text-sm font-[600] mb-1">Description</label>
        <textarea
          placeholder="Type here something...."
          className="border border-[#DDDDDD] rounded px-3 py-2 min-h-[120px] text-sm mb-4"
          value={editor}
          onChange={(e) => setEditor(e.target.value)}
        />

        <label className="text-sm font-[600] mb-1">Attachment</label>
        <input
          type="file"
          className="hidden"
          ref={fileInputRef}
          onChange={handleFileChange}
        />
        <div
          className="flex flex-col items-center border border-dashed p-6 text-center text-sm text-gray-500 mb-4 rounded cursor-pointer hover:border-blue-400"
          onClick={handleFileClick}
          onDragOver={(e) => e.preventDefault()}
          onDrop={(e) => {
            e.preventDefault();
            handleDrop(e);
          }}
        >
          <Image src={uploadIcon} alt="Logo" />
          <p className="mb-1">
            Upload a{" "}
            <span className="text-blue-600 underline cursor-pointer">file</span>{" "}
            or drag and drop
          </p>
          <p className="text-xs text-gray-400">PNG, JPG, PDF up to 10MB</p>
        </div>

        <div className="space-y-3">
          {uploaded && (
            <div className="">
              <CircularProgress />
            </div>
          )}
          {attachments.map((file, index) => (
            <div
              key={index}
              className="flex items-center gap-4 border border-[#DDD] rounded px-4 py-2 mx-3 cursor-pointer"
              onClick={() => fetchImageUrl(file.url)}
            >
              <Image
                src={getIconForType(file.type, file.fileName)}
                alt="file"
                width={24}
                height={24}
              />
              <div className="flex-1 overflow-hidden text-ellipsis whitespace-nowrap">
                <p className="text-black-600">{file.fileName}</p>
              </div>
              <button
                onClick={() => removeFile(index)}
                className="text-red-600 text-sm hover:underline"
              >
                Remove
              </button>
            </div>
          ))}
        </div>

        <label className="text-sm font-[600] mb-1">Priority</label>
        <TextField
          select
          value={priority}
          onChange={(e) => {
            setPriority(e.target.value);
            updateTicketpriority(e.target.value);
          }}
          size="small"
          fullWidth
          variant="outlined"
          sx={{
            mb: 3,
            width: "200px",
            "& .MuiOutlinedInput-root": {
              padding: "8px 12px",
              fontSize: "0.875rem",
            },
          }}
        >
          <MenuItem value="low">Low</MenuItem>
          <MenuItem value="Medium">Medium</MenuItem>
          <MenuItem value="high">High</MenuItem>
        </TextField>

        <div className="text-right mt-auto">
          <button
            disabled={!editor || editor === ""}
            onClick={replytoEmailThread}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white px-6 py-2 rounded"
          >
            Send
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <>
      {loader && <Loader />}
      <div className="mx-auto p-6">
        {data?.type?.toLowerCase() === "email"
          ? renderEmailView()
          : renderTicketView()}
      </div>

      <Dialog
        open={closeDialogOpen}
        onClose={() => setCloseDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle className="font-bold text-lg text-white bg-teal-500">
          {data &&
            data?.status !== `Closed` &&
            data?.status !== `Open` &&
            `Reason for Closing Ticket`}
          {data && data?.status === `Closed` && `Reason for Re Opening Ticket`}
        </DialogTitle>
        <DialogContent>
          <TextField
            multiline
            rows={4}
            fullWidth
            placeholder="Enter reason for closing the ticket"
            value={closeReason}
            className="!mt-6"
            onChange={(e) => setCloseReason(e.target.value)}
            margin="dense"
          />
        </DialogContent>
        <DialogActions>
          <button
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white px-6 py-2 rounded"
            onClick={() => setCloseDialogOpen(false)}
            color="secondary"
          >
            Cancel
          </button>
          <button
            onClick={() => {
              if (
                data &&
                data?.status !== `Closed` &&
                data?.status !== `Open`
              ) {
                handleCloseTicket(closeReason);
              }
              if (data && data?.status === `Closed`) {
                handleOpenTicket(closeReason);
              }
              // <-- You can pass reason to your submit function
              setCloseDialogOpen(false);
              setCloseReason("");
            }}
            color="primary"
            className="bg-teal-500 hover:bg-teal-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white px-6 py-2 rounded"
            disabled={!closeReason.trim()}
          >
            Confirm & Close
          </button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default TicketView;
