import React from "react";
import type { StepType } from "@/components/questionsbuilder/types";
import Button from "../../../[components]/Button1";

interface Props {
  steps: StepType[];
  selectedStep: string;
  handlePreviousStep: () => void;
  handleException: () => void;
  handleIncomplete: () => void;
  handleNextStep: () => void;
  handleMoveToQC: () => void;
}

const ProviderFormActions: React.FC<Props> = ({
  steps,
  selectedStep,
  handlePreviousStep,
  handleException,
  handleIncomplete,
  handleNextStep,
  handleMoveToQC,
}) => {
  const currentStepIndex = steps.findIndex((step) => step.id === selectedStep);
  const isFirstStep = currentStepIndex === 0;
  const isLastStep = currentStepIndex === steps.length - 1;

  return (
    <div className="bg-white p-4 rounded-sm shadow-sm border-gray-200 border">
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-600">
          Step {currentStepIndex + 1} of {steps.length}
        </div>
        <div className="flex space-x-3">
          {!isFirstStep && (
            <Button
              text="Previous"
              onClick={handlePreviousStep}
              className="px-6 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded"
            >
              Previous
            </Button>
          )}
          <Button
            text="Exception"
            onClick={handleException}
            className="px-6 py-2 bg-[#FF9E6E] hover:bg-[#FF9E6E] text-white rounded"
          >
            Exception
          </Button>
          <Button
            text="Incomplete"
            onClick={handleIncomplete}
            className="px-6 py-2 bg-[#EA6B6B] hover:bg-[#EA6B6B] text-white rounded"
          >
            Incomplete
          </Button>
          {!isLastStep && (
            <Button
              text="Next"
              onClick={handleNextStep}
              className="px-6 py-2 bg-[#1567A7] hover:bg-[#1567A7] text-white rounded"
            >
              Next
            </Button>
          )}
          {isLastStep && (
            <Button
              text="Move to QC"
              onClick={handleMoveToQC}
              className="px-6 py-2 bg-teal-500 hover:bg-teal-500 text-white rounded"
            >
              Move to QC
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};
export default ProviderFormActions;
