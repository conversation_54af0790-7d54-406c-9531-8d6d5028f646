import React from "react";
import { Card, Divider, Typography } from "@mui/material";
import { Field<PERSON>enderer } from "../../../../components/questionsbuilder/FieldRenderforProcess";
import type { StepType, FieldType } from "@/components/questionsbuilder/types";
import type { FieldOption } from "@/components/questionsbuilder/tablefields";

interface Props {
  steps: StepType[];
  selectedStep: string;
  allFields: FieldType[];
  fieldValues: Record<string, unknown>;
  fieldVisibilityMap: Record<string, boolean>;
  mapFieldForRenderer: (field: FieldType) => FieldOption;
  handleFieldValueChange: (
    id: string,
    value: string | number | boolean | string[] | null | undefined
  ) => void;
  validationErrors: Record<string, string>;
  selectedStateId: string | null;
  setSelectedStateId: (id: string | null) => void;
}

const ProviderFormSections: React.FC<Props> = ({
  steps,
  selectedStep,
  allFields,
  fieldValues,
  fieldVisibilityMap,
  mapFieldFor<PERSON><PERSON><PERSON>,
  handleFieldValueChange,
  validationErrors,
  selectedStateId,
  setSelectedStateId,
}) => {
  return (
    <div className="space-y-6">
      <Card className="border-2 border-gray-100 backdrop-blur-sm bg-white/90 shadow-xl hover:shadow-4xl transition-shadow duration-300 box-size !rounded-[4px]">
        {Array.isArray(steps) &&
          steps
            .find((step) => step.id === selectedStep)
            ?.sections?.map((section) => {
              const sectionNameLower = section.name?.toLowerCase() || "";
              const providerKeywords = [
                "provider",
                "provider information",
                "provider details",
              ];
              const isProviderSection = providerKeywords.some((keyword) =>
                sectionNameLower.includes(keyword)
              );

              let enrollmentTypeField = null;
              let enrollmentTypeValue = null;
              const enrollmentTypeKeywords = [
                "enrollment",
                "enrolment",
                "enrollmenttype",
                "enrolmenttype",
                "type",
              ];
              enrollmentTypeField = allFields.find((field) =>
                enrollmentTypeKeywords.some(
                  (keyword) =>
                    field.label?.toLowerCase().includes(keyword) ||
                    field.id?.toLowerCase().includes(keyword)
                )
              );
              enrollmentTypeValue = enrollmentTypeField
                ? fieldValues[enrollmentTypeField.id]
                : null;

              if (!enrollmentTypeField) {
                for (const field of allFields) {
                  if (
                    field.field_type === "global_select" &&
                    field.label?.toLowerCase().includes("type")
                  ) {
                    enrollmentTypeField = field;
                    enrollmentTypeValue = fieldValues[field.id];
                    break;
                  }
                }
              }
              if (!enrollmentTypeField) {
                for (const field of allFields) {
                  if (field.label?.toLowerCase().includes("type")) {
                    enrollmentTypeField = field;
                    enrollmentTypeValue = fieldValues[field.id];
                    break;
                  }
                }
              }
              const isIndividualEnrollment =
                enrollmentTypeValue &&
                (enrollmentTypeValue.toString().toLowerCase() === "group" ||
                  enrollmentTypeValue
                    .toString()
                    .toLowerCase()
                    .includes("Group"));

              if (isProviderSection && isIndividualEnrollment) {
                return null;
              }

              return (
                <div key={section.id}>
                  <div className="flex justify-between items-center p-3 bg-[#7592F9]/[0.05]  ">
                    <Typography sx={{ fontWeight: "bold", color: "#2B3674" }}>
                      {section.name}
                    </Typography>
                  </div>
                  <Divider className="mb-4" />
                  <div className="grid grid-cols-3 gap-4 p-4">
                    {section.fields
                      .filter(
                        (field: FieldType) => fieldVisibilityMap[field.id]
                      )
                      .map((field) => (
                        <div
                          key={field.id}
                          className={`col-span-1 ${field.field_type === "grid" ? "col-span-3" : ""}`}
                        >
                          <div className="mt-1">
                            <div className="w-full">
                              <FieldRenderer
                                field={mapFieldForRenderer(field)}
                                setSelectedStateId={setSelectedStateId}
                                selectedStateId={selectedStateId}
                                allFields={allFields.map(mapFieldForRenderer)}
                                fieldValues={fieldValues}
                                value={
                                  fieldValues[field.id] as
                                    | string
                                    | number
                                    | boolean
                                    | string[]
                                    | null
                                    | undefined
                                }
                                onChange={(value) =>
                                  handleFieldValueChange(field.id, value)
                                }
                              />
                              {validationErrors[field.id] && (
                                <Typography
                                  variant="caption"
                                  color="error"
                                  className="mt-1 block text-red-500"
                                  sx={{
                                    fontSize: "0.75rem",
                                    marginTop: "4px",
                                  }}
                                >
                                  {validationErrors[field.id]}
                                </Typography>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              );
            })}
      </Card>
    </div>
  );
};
export default ProviderFormSections;
