import React from "react";
import { Box } from "@mui/material";
import type { StepType } from "@/components/questionsbuilder/types";

interface Props {
  steps: StepType[];
  selectedStep: string;
  setSelectedStep: (id: string) => void;
}
const ProviderFormStepper: React.FC<Props> = ({
  steps,
  selectedStep,
  setSelectedStep,
}) => {
  return (
    <div className="flex space-x-1 mb-0 mt-3">
      {Array.isArray(steps) &&
        steps.map((step, index) => {
          const currentStepIndex = steps.findIndex(
            (s) => s.id === selectedStep
          );
          const isActive = index <= currentStepIndex;
          const isLast = index === steps.length - 1;

          let roundedClass = "";
          if (isActive && (isLast || index === currentStepIndex))
            roundedClass = "rounded-r-full";

          return (
            <Box
              key={step.id}
              onClick={() => setSelectedStep(step.id)}
              className={`flex-1 flex items-center justify-center mr-0.5 py-1 px-5 cursor-pointer transition-all duration-200 ${
                isActive
                  ? `bg-gradient-to-r from-[#A357DF] to-[#0C4DB2] text-white ${roundedClass} m-0`
                  : "bg-[#EDF4FA] text-[#9DA7C1]"
              }`}
            >
              <div className="flex items-center space-x-2">
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center text-xs font-semibold ${
                    isActive
                      ? "bg-white text-[#0C4DB2] border-2 border-[#00B8B0]"
                      : "bg-white border-2 border-[#C2C4D0] text-[#A3AED0]"
                  }`}
                >
                  {index + 1}
                </div>
                <span
                  className={`text-sm ${
                    isActive ? "text-white font-medium" : "text-[#9DA7C1]"
                  }`}
                >
                  {step.name}
                </span>
              </div>
            </Box>
          );
        })}
    </div>
  );
};
export default ProviderFormStepper;
