import Dynamicform from "./providerForm";
// import TicketHeader from "./ticketHeader";
import TicketSidebar from "./ticketSidebar";

export default function TicketDetailsPage() {
  // const ticket = {
  //   id: "90000369",
  //   provider: "<PERSON>",
  //   specialty: "Oncology",
  //   process: "Provider Credentialing",
  //   follower: "Kyal",
  //   status: "Inprogress",
  //   receivedDate: "16-12-2022",
  //   lastUpdated: "24-12-2022",
  //   requestedBy: "Dr. Aurora",
  // };

  type TicketHistoryStatus =
    | "Current"
    | "Exception"
    | "Moved to QC"
    | "Completed";

  const history: {
    user: string;
    date: string;
    status: TicketHistoryStatus;
    totalTime: string;
    exceptionType?: string;
    followUpDate?: string;
    comments?: string;
    actionCode?: string;
    statusCode?: string;
  }[] = [
    {
      user: "Christina",
      date: "03-29-2025",
      status: "Current",
      totalTime: "02 : 52 : 12",
    },
    {
      user: "<PERSON>",
      date: "03-22-2025",
      status: "Exception",
      exceptionType: "Incorrect Data",
      followUpDate: "06-22-2024",
      comments: "Missing NPI Information in email...",
      totalTime: "00 : 25 : 10",
    },
    {
      user: "Steve Austin",
      date: "03-22-2025",
      status: "Exception",
      exceptionType: "Incorrect Data",
      followUpDate: "06-22-2024",
      comments: "Missing NPI Information in email...",
      totalTime: "01 : 01 : 11",
    },
    {
      user: "Jhon Felix",
      date: "03-22-2025",
      status: "Moved to QC",
      actionCode: "Client assistance",
      statusCode: "Missing NPI Information",
      followUpDate: "06-22-2024",
      comments: "Missing NPI Information in email...",
      totalTime: "01 : 23 : 24",
    },
    {
      user: "Ron Killings",
      date: "03-22-2025",
      status: "Completed",
      actionCode: "Client assistance",
      statusCode: "Missing NPI Information",
      followUpDate: "06-22-2024",
      comments:
        "Missing NPI Information in email is the main field as the part o lorem ipsum is simple and dummy text...",
      totalTime: "03 : 20 : 14",
    },
  ];

  return (
    <div className="flex" style={{ height: "71vh" }}>
      <TicketSidebar history={history} />
      <div className="flex-1 flex flex-col bg-[#F7F9FB] ml-3">
        {/* <TicketHeader ticket={ticket} /> */}
        <div className="flex-1 overflow-y-auto">
          <Dynamicform />
        </div>
        {/* Uncomment this if needed */}
        {/* <TicketFooterActions onException={() => {}} onIncomplete={() => {}} onNext={() => {}} /> */}
      </div>
    </div>
  );
}
