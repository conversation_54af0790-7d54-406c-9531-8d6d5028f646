/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import React, { useState, useEffect, useCallback, useRef } from "react";
import { Card } from "../../../[components]/card";
import {
  Box,
  CircularProgress,
  Divider,
  Typography,
  Alert,
  Snackbar,
} from "@mui/material";
import Button from "@/app/[components]/Button1";
import "./style.css";
import type { FieldType, StepType } from "@/components/questionsbuilder/types";
import { getTemplate } from "@/api/dynamicTemplate/template";
import { showToast } from "@/components/toaster/ToastProvider";
import { FieldRenderer } from "../../../../components/questionsbuilder/FieldRenderforProcess";
import type { FieldOption } from "@/components/questionsbuilder/tablefields";
import { useParams, useRouter } from "next/navigation";
import { getAllPayers, getPayer } from "@/api/masters/payer/payer";
// import { getProvider } from "@/api/masters/provider/provider";
import {
  getNpiInformations,
  getProviderNpi,
  getTicketsById,
  updateProviderNpi,
} from "@/api/ProviderCredentials/provider";

export interface GridColumn {
  id: string;
  name: string;
  fieldType: string;
}

// Extend FieldType for local use, adding identifierType and identifierKey properties
interface LocalFieldType extends FieldType {
  columns?: GridColumn[];
  rows?: Record<string, unknown>[];
  htmlContent?: string;
  identifierType?: string;
  identifierKey?: "provider" | "group" | "payer";
  prefillFieldIds?: string[];
}

export default function ProviderForm() {
  const [steps, setSteps] = useState<StepType[]>([]);
  const [selectedStep, setSelectedStep] = useState<string>("step-1");
  const [fieldValues, setFieldValues] = useState<Record<string, unknown>>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedStateId, setSelectedStateId] = useState<string | null>(null);
  const [prefillLoading, setPrefillLoading] = useState<Record<string, boolean>>(
    {}
  );
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});
  const [showSuccess, setShowSuccess] = useState<boolean>(false);
  const [, setTicketData] = useState<any>(null);
  const [ticketLoading, setTicketLoading] = useState<boolean>(false);

  // Interface for enhanced dropdown options
  interface DropdownOption {
    id: string;
    value: string;
    entityName?: string;
    npi?: string;
    originalData?: any;
  }

  // State for API-loaded dropdown options
  const [dropdownOptions, setDropdownOptions] = useState<
    Record<string, DropdownOption[]>
  >({
    groupNPI: [],
    providerNPI: [],
    state: [],
    address: [],
    payerName: [],
  });
  const [, setLoadingOptions] = useState<Record<string, boolean>>({
    groupNPI: false,
    providerNPI: false,
    state: false,
    address: false,
    payerName: false,
  });

  // State to store dynamic options from Group NPI API
  const [dynamicFieldOptions, setDynamicFieldOptions] = useState<{
    states: DropdownOption[];
    addresses: DropdownOption[];
  }>({
    states: [],
    addresses: [],
  });

  // Debouncing and API call management
  const debounceTimeouts = useRef<Record<string, NodeJS.Timeout>>({});
  const lastApiCallValues = useRef<Record<string, string>>({});

  const params = useParams();
  const router = useRouter();
  const id = params.id;

  const fetchTemplate = useCallback(async () => {
    if (!id) return;
    setLoading(true);
    try {
      const res = await getTemplate({
        filters: { type: "provider_credentials", isActive: true },
        sortBy: "",
        sortOrder: "",
        search: "",
      });
      const template = res?.templates?.data?.templates[0];
      if (!template) {
        console.error("Invalid response format:", res);
        showToast.error("Failed to load template data");
        return;
      }
      const rawFields = template?.fields;
      let parsedFields = [];
      try {
        if (typeof rawFields === "string" && rawFields.trim()) {
          parsedFields = JSON.parse(rawFields);
        } else if (Array.isArray(rawFields)) {
          parsedFields = rawFields;
        } else {
          parsedFields = [];
        }
      } catch (err) {
        console.error("Error parsing fields:", err);
      }

      setSteps(parsedFields);
      if (parsedFields.length > 0) {
        setSelectedStep(parsedFields[0].id);
      }
    } catch (error) {
      console.error("Error loading template:", error);
      showToast.error("Failed to load template");
    } finally {
      setLoading(false);
    }
  }, [id]);

  // Helper function to create mapping from field labels to field IDs
  const createFieldLabelToIdMapping = (steps: StepType[]) => {
    const mapping: Record<string, string> = {};

    steps.forEach((step) => {
      step.sections.forEach((section) => {
        section.fields.forEach((field) => {
          if (field.label) {
            const label = field.label.toLowerCase();
            mapping[label] = field.id;

            // Remove spaces and special characters for flexible matching
            const normalizedLabel = label.replace(/[^a-z0-9]/g, "");
            mapping[normalizedLabel] = field.id;

            // Camel case version (like "enrollementType")
            const camelCase = label
              .replace(/\s+(.)/g, (_, char) => char.toUpperCase())
              .replace(/[^a-zA-Z0-9]/g, "");
            mapping[camelCase] = field.id;

            // Common variations based on your ticket data
            if (label.includes("enrollement")) {
              mapping["enrollementtype"] = field.id;
              mapping["enrollementType"] = field.id;
            }
            if (label.includes("job")) {
              mapping["jobrequest"] = field.id;
              mapping["jobRequest"] = field.id;
            }
            if (label.includes("provider") && label.includes("npi")) {
              mapping["providernpi"] = field.id;
              mapping["providerNPI"] = field.id;
            }
            // Specific state field mappings
            if (label.includes("provider") && label.includes("state")) {
              mapping["providerstate"] = field.id;
              mapping["providerState"] = field.id;
            } else if (label.includes("group") && label.includes("state")) {
              mapping["groupstate"] = field.id;
              mapping["groupState"] = field.id;
            } else if (label.includes("payor") && label.includes("state")) {
              mapping["payorstate"] = field.id;
              mapping["payorState"] = field.id;
            } else if (label.includes("payer") && label.includes("state")) {
              mapping["payerstate"] = field.id;
              mapping["payerState"] = field.id;
            } else if (
              label.includes("state") &&
              !label.includes("provider") &&
              !label.includes("group") &&
              !label.includes("payor") &&
              !label.includes("payer")
            ) {
              mapping["state"] = field.id;
            }

            // Payer/Payor name mappings
            if (label.includes("payor") && label.includes("name")) {
              mapping["payorname"] = field.id;
              mapping["payorName"] = field.id;
            } else if (label.includes("payer") && label.includes("name")) {
              mapping["payername"] = field.id;
              mapping["payerName"] = field.id;
            }

            // Address mappings
            if (label.includes("provider") && label.includes("address")) {
              mapping["provideraddress"] = field.id;
              mapping["providerAddress"] = field.id;
            } else if (label.includes("group") && label.includes("address")) {
              mapping["groupaddress"] = field.id;
              mapping["groupAddress"] = field.id;
              mapping["address"] = field.id; // Also map generic "address" for group
            } else if (
              label.includes("address") &&
              !label.includes("provider") &&
              !label.includes("group")
            ) {
              mapping["address"] = field.id;
            }
            if (label.includes("priority")) {
              mapping["priority"] = field.id;
            }
            if (label.includes("subject")) {
              mapping["subject"] = field.id;
            }
            if (label.includes("description")) {
              mapping["description"] = field.id;
            }
            if (label.includes("ticket") && label.includes("type")) {
              mapping["tickettype"] = field.id;
              mapping["ticketType"] = field.id;
            }
          }
        });
      });
    });

    return mapping;
  };

  // Helper function to map ticket values to field IDs
  const mapTicketValuesToFieldIds = (
    ticketValues: any,
    labelToIdMapping: Record<string, string>
  ) => {
    const mappedValues: Record<string, any> = {};

    // Helper function to map a single field with section context
    const mapSingleField = (key: string, value: any, sectionName?: string) => {
      // Try to find matching field ID
      const normalizedKey = key.toLowerCase();
      const sectionPrefix = sectionName
        ? sectionName.toLowerCase().replace(/information/g, "")
        : "";

      // Create context-aware keys for section-specific fields
      const contextKeys = [
        normalizedKey, // exact key
        key, // original camelCase
        normalizedKey.replace(/[^a-z0-9]/g, ""), // clean key
      ];

      // Add section-specific keys for common fields
      if (sectionPrefix) {
        if (normalizedKey === "state") {
          contextKeys.push(`${sectionPrefix}state`, `${sectionPrefix}State`);
        }
        if (normalizedKey === "address") {
          contextKeys.push(
            `${sectionPrefix}address`,
            `${sectionPrefix}Address`
          );
        }
        if (normalizedKey.includes("name") && sectionPrefix.includes("payor")) {
          contextKeys.push("payorname", "payorName", "payername", "payerName");
        }
        // Special handling for entityName in groupInformation
        if (normalizedKey === "entityname" && sectionPrefix.includes("group")) {
          contextKeys.push(
            "entityname",
            "entityName",
            "groupname",
            "groupName"
          );
        }
      }

      // Try all context keys
      for (const contextKey of contextKeys) {
        if (labelToIdMapping[contextKey]) {
          mappedValues[labelToIdMapping[contextKey]] = value;
          return true;
        }
      }

      // If no mapping found, try partial matching
      for (const [mappingKey, fieldId] of Object.entries(labelToIdMapping)) {
        if (
          mappingKey.includes(normalizedKey) ||
          normalizedKey.includes(mappingKey)
        ) {
          mappedValues[fieldId] = value;
          return true;
        }
      }

      return false;
    };

    // Check if ticketValues is an array (new section-wise format)
    if (Array.isArray(ticketValues)) {
      ticketValues.forEach((sectionObj, sectionIndex) => {
        console.log(` Processing section ${sectionIndex}:`, sectionObj);

        Object.entries(sectionObj).forEach(([sectionName, sectionData]) => {
          console.log(` Section: ${sectionName}`, sectionData);

          if (typeof sectionData === "object" && sectionData !== null) {
            Object.entries(sectionData).forEach(([fieldKey, fieldValue]) => {
              mapSingleField(fieldKey, fieldValue, sectionName);
            });
          }
        });
      });
    } else if (typeof ticketValues === "object" && ticketValues !== null) {
      // Handle flat object format (legacy support)
      Object.entries(ticketValues).forEach(([key, value]) => {
        mapSingleField(key, value);
      });
    } else {
    }

    return mappedValues;
  };

  // Function to fetch ticket data and populate form fields
  const fetchTicketData = useCallback(async () => {
    if (!id) return;

    setTicketLoading(true);
    try {
      const response = await getTicketsById(id as string);

      // Try different response paths based on your API structure
      const ticket =
        response?.data?.providerTicket ||
        response?.providerEmailTicket ||
        response?.providerTicket;
      if (ticket) {
        setTicketData(ticket);

        if (ticket.values) {
          let parsedValues = {};

          try {
            if (typeof ticket.values === "string") {
              parsedValues = JSON.parse(ticket.values);
            } else if (typeof ticket.values === "object") {
              parsedValues = ticket.values;
            }
            const labelToIdMapping = createFieldLabelToIdMapping(steps);
            const mappedValues = mapTicketValuesToFieldIds(
              parsedValues,
              labelToIdMapping
            );
            setFieldValues((prevValues) => ({
              ...prevValues,
              ...mappedValues,
            }));
            fieldValuesRef.current = {
              ...fieldValuesRef.current,
              ...mappedValues,
            };
          } catch (parseError) {
            console.error("Error parsing ticket values:", parseError);
          }
        } else {
        }
      }
    } catch (error) {
      console.error("Error loading ticket data:", error);
      showToast.error("Failed to load ticket data");
    } finally {
      setTicketLoading(false);
    }
  }, [id, steps]);

  useEffect(() => {
    if (!id) return;
    fetchTemplate();
  }, [id, fetchTemplate]);

  useEffect(() => {
    if (!id || steps.length === 0) return;
    fetchTicketData();
  }, [id, steps.length, fetchTicketData]);
  const evaluateVisibilityCondition = (
    condition: string | undefined,
    allFields: FieldType[]
  ) => {
    if (!condition) return true;

    try {
      const fieldPattern = /\{([^}]+)\}/g;
      let evaluatedCondition = condition;

      let match = fieldPattern.exec(condition);
      while (match !== null) {
        const fieldId = match[1];
        const fieldValue = fieldValues[fieldId];
        const field = allFields.find((f) => f.id === fieldId);

        if (field) {
          if (field.field_type === "select") {
            evaluatedCondition = evaluatedCondition.replace(
              match[0],
              typeof fieldValue === "string"
                ? `'${fieldValue}'`
                : String(fieldValue)
            );
          } else if (
            field.field_type === "multiselect" ||
            field.field_type === "checkboxes"
          ) {
            const arrayValue = Array.isArray(fieldValue)
              ? fieldValue
              : typeof fieldValue === "string"
                ? fieldValue.split(",").filter(Boolean)
                : [];

            evaluatedCondition = evaluatedCondition.replace(
              match[0],
              JSON.stringify(arrayValue)
            );
          } else {
            evaluatedCondition = evaluatedCondition.replace(
              match[0],
              typeof fieldValue === "string"
                ? `'${fieldValue}'`
                : String(fieldValue)
            );
          }
        }
        match = fieldPattern.exec(condition);
      }

      evaluatedCondition = evaluatedCondition.replace(
        /\bcontains\b/g,
        "includes"
      );
      evaluatedCondition = evaluatedCondition
        .replace(/\band\b/gi, "&&")
        .replace(/\bor\b/gi, "||");

      evaluatedCondition = evaluatedCondition.replace(
        /(\[.*?\]) includes '([^']*)'/g,
        '$1.includes("$2")'
      );

      evaluatedCondition = evaluatedCondition
        .replace(/=/g, "===")
        .replace(/!===/g, "!==");

      return new Function(`return ${evaluatedCondition}`)();
    } catch (error) {
      console.error("Error evaluating visibility condition:", error, condition);
      return true;
    }
  };
  const getFieldVisibilityMap = (
    allFields: FieldType[]
  ): Record<string, boolean> => {
    const visibility: Record<string, boolean> = {};

    if (!Array.isArray(allFields)) {
      console.error("allFields is not an array:", allFields);
      return visibility;
    }

    allFields.forEach((field) => {
      const isVisible = evaluateVisibilityCondition(field.visibleIf, allFields);
      visibility[field.id] = isVisible;

      if (!isVisible && fieldValues[field.id] !== undefined) {
        setFieldValues((prev) => {
          const newValues = { ...prev };
          delete newValues[field.id];
          return newValues;
        });
      }
    });

    return visibility;
  };

  const allFields = Array.isArray(steps)
    ? steps?.flatMap(
        (s) => s.sections?.flatMap((sec) => sec?.fields || []) || []
      )
    : [];

  const fieldVisibilityMap = getFieldVisibilityMap(allFields || []);

  // Define fetchPrefillData first
  const fetchPrefillData = useCallback(
    async (
      identifierType: string,
      identifierId: string,
      fieldsToFill: FieldType[]
    ) => {
      if (!identifierId || !identifierType || !fieldsToFill.length) {
        return;
      }

      setPrefillLoading((prev) => ({ ...prev, [identifierType]: true }));

      try {
        let response;
        let data;
        // Call appropriate API based on identifier type
        switch (identifierType.toLowerCase()) {
          // case "provider":
          //   response = await getProvider({ id: identifierId });
          //   data = response?.provider?.data?.provider;
          //   console.log(`Provider API response:`, response);
          //   break;

          // case "group":
          //   console.log(
          //     `Calling getByOrganization with clientId: ${identifierId}`
          //   );
          //   response = await getByOrganization({ clientId: identifierId });
          //   // The API returns data in getByClient.data format
          //   data = response?.getByClient?.data;
          //   console.log(`Group API response:`, response);
          //   break;

          case "payer":
            response = await getPayer({ id: identifierId });
            data = response?.payer?.data?.payer;
            break;

          default:
            return;
        }

        if (!data) {
          console.error(
            "No data returned from API for",
            identifierType,
            identifierId
          );
          showToast.error(
            `No data found for ${identifierType} ID: ${identifierId}`
          );
          return;
        }

        // Parse the values from the API response
        let values: Record<string, any> = {};

        if (data.values) {
          if (typeof data.values === "string") {
            try {
              values = JSON.parse(data.values) as Record<string, any>;
            } catch (err) {
              console.error("Error parsing values string:", err);
              values = {};
            }
          } else if (typeof data.values === "object") {
            values = data.values as Record<string, any>;
          }
        } else {
          // If no 'values' property, use the data object directly
          values = data;
        }

        // Update field values based on globals_name mapping
        const newFieldValues = { ...fieldValues };
        let updatedCount = 0;

        fieldsToFill.forEach((field) => {
          if (field.globals_name) {
            const path = field.globals_name.split(".");

            // Navigate through the object path
            let currentValue: any = values;
            for (const key of path) {
              if (
                currentValue &&
                typeof currentValue === "object" &&
                key in currentValue
              ) {
                currentValue = currentValue[key];
              } else {
                currentValue = undefined;
                break;
              }
            }

            if (
              (currentValue === undefined ||
                currentValue === null ||
                currentValue === "") &&
              identifierType
            ) {
              const fieldKey = (
                field.globals_name ||
                field.label ||
                field.id ||
                ""
              ).toLowerCase();
              const fieldId = field.id.toLowerCase();
              const apiData = data;

              if (
                fieldKey.includes("name") ||
                fieldKey.includes("organization")
              ) {
                if (identifierType === "provider") {
                  currentValue =
                    apiData.name ||
                    apiData.providerName ||
                    apiData.firstName ||
                    (apiData.firstName && apiData.lastName
                      ? apiData.firstName + " " + apiData.lastName
                      : null);
                } else if (identifierType === "payer") {
                  currentValue =
                    apiData.name ||
                    apiData.payerName ||
                    apiData.organizationName;
                }
              } else if (
                fieldKey.includes("address") ||
                fieldKey.includes("street")
              ) {
                currentValue =
                  apiData.address || apiData.street || apiData.address1;
              } else if (fieldKey.includes("city")) {
                currentValue = apiData.city;
              } else if (fieldKey.includes("state")) {
                currentValue = apiData.state;
              } else if (fieldKey.includes("zip")) {
                currentValue =
                  apiData.zip || apiData.zipCode || apiData.postalCode;
              } else if (fieldKey.includes("phone")) {
                currentValue =
                  apiData.phone || apiData.phoneNumber || apiData.contactPhone;
              } else if (fieldKey.includes("email")) {
                currentValue =
                  apiData.email || apiData.emailAddress || apiData.contactEmail;
              } else if (fieldKey.includes("tax") || fieldKey.includes("ein")) {
                currentValue =
                  apiData.taxId || apiData.ein || apiData.federalTaxId;
              } else if (fieldKey.includes("npi")) {
                if (identifierType === "group") {
                  currentValue = apiData.npi || apiData.groupNpi;
                } else if (identifierType === "provider") {
                  currentValue = apiData.npi || apiData.providerNpi;
                }
              } else if (fieldKey.includes("specialty")) {
                currentValue = apiData.specialty || apiData.specialtyCode;
              } else if (fieldKey.includes("license")) {
                currentValue = apiData.license || apiData.licenseNumber;
              }

              // If no value found yet, try direct field ID mapping as last resort
              if (!currentValue && apiData) {
                // Try exact field ID match
                if (apiData[field.id]) {
                  currentValue = apiData[field.id];
                }
                // Try camelCase variations
                else if (apiData[fieldId]) {
                  currentValue = apiData[fieldId];
                }
                // Try common variations for firstName
                else if (
                  fieldId === "firstname" &&
                  (apiData.firstName || apiData.first_name)
                ) {
                  currentValue = apiData.firstName || apiData.first_name;
                }
              }

              if (currentValue) {
              }
            }

            if (
              currentValue !== undefined &&
              currentValue !== null &&
              currentValue !== ""
            ) {
              newFieldValues[field.id] = currentValue;

              // Also update the ref for immediate access
              fieldValuesRef.current = {
                ...fieldValuesRef.current,
                [field.id]: currentValue,
              };

              updatedCount++;
            }
          }
        });

        setFieldValues(newFieldValues);

        const patchedFieldValues = { ...newFieldValues };
        let additionalUpdates = 0;

        fieldsToFill.forEach((field) => {
          let patchValue = null;

          if (field.globals_name && data[field.globals_name]) {
            patchValue = data[field.globals_name];
          } else {
            if (data[field.id]) {
              patchValue = data[field.id];
            }
          }

          if (patchValue) {
            patchedFieldValues[field.id] = patchValue;
            fieldValuesRef.current[field.id] = patchValue;
            additionalUpdates++;
          }
        });

        const totalUpdates = updatedCount + additionalUpdates;
        setFieldValues(patchedFieldValues);

        if (totalUpdates > 0) {
          showToast.success(
            `Successfully prefilled ${totalUpdates} fields from ${identifierType} data`
          );
        } else {
          showToast.info(
            `No matching data found to prefill fields for ${identifierType} ID: ${identifierId}`
          );
        }
      } catch (error) {
        console.error("Error fetching prefill data:", error);
        showToast.error(
          `Failed to fetch ${identifierType} data. Please check the ID and try again.`
        );
      } finally {
        setPrefillLoading((prev) => ({ ...prev, [identifierType]: false }));
      }
    },
    [fieldValues]
  );

  // Functions to fetch dropdown options
  const fetchGroupNPIOptions = useCallback(async () => {
    setLoadingOptions((prev) => ({ ...prev, groupNPI: true }));
    try {
      const response = await getNpiInformations({
        type: [
          "nPIDetails",
          "basicInformation.taxIdentificationNumberTINEIN",
          "practiceLocations.state",
          "practiceLocations.address",
        ],
      });

      const apiData =
        response?.data?.getNpiInformations?.data?.data ||
        response?.getNpiInformations?.data?.data ||
        {};
      // Flatten nPIDetails from all items in data array
      const npiDetails = Array.isArray(apiData)
        ? apiData.flatMap((item: any) =>
            Array.isArray(item.nPIDetails)
              ? item.nPIDetails.map((nd: any) => ({
                  ...nd.nPIDetails,
                  practiceLocations: {
                    state: item["practiceLocations.state"] || [],
                    address: item["practiceLocations.address"] || [],
                  },
                  basicInfo: {
                    tin:
                      item["basicInformation.taxIdentificationNumberTINEIN"] ||
                      "",
                  },
                  fullApiResponse: item,
                }))
              : []
          )
        : [];

      // For backward compatibility, if npiDetails is empty, fallback to old structure
      // (optional, remove if not needed)
      // const npiDetails = apiData?.nPIDetails || [];
      // Map practiceLocations from the new apiData structure
      const practiceLocations: { state: any[]; address: any[] } = {
        state: [],
        address: [],
      };
      if (Array.isArray(apiData)) {
        // If apiData is an array, map state and address from each item
        practiceLocations.state = apiData.flatMap((item: any) =>
          Array.isArray(item["practiceLocations.state"])
            ? item["practiceLocations.state"].map((s: any) => s.state || s)
            : []
        );
        practiceLocations.address = apiData.flatMap((item: any) =>
          Array.isArray(item["practiceLocations.address"])
            ? item["practiceLocations.address"].map((a: any) => a.address || a)
            : []
        );
      } else if (typeof apiData === "object" && apiData !== null) {
        // If apiData is an object, map state and address directly
        practiceLocations.state = Array.isArray(
          apiData["practiceLocations.state"]
        )
          ? apiData["practiceLocations.state"].map((s: any) => s.state || s)
          : [];
        practiceLocations.address = Array.isArray(
          apiData["practiceLocations.address"]
        )
          ? apiData["practiceLocations.address"].map((a: any) => a.address || a)
          : [];
      }
      const basicInfo = {
        tin:
          apiData[0]?.["basicInformation.taxIdentificationNumberTINEIN"] || "",
      };

      const options = Array.isArray(npiDetails)
        ? npiDetails.map((item: any) => {
            const npiData = item;
            return {
              id: npiData.nPINumber,
              value: `${npiData.nPINumber || "Unknown"}`,
              entityName: npiData.entityName || "Unknown Entity",
              npi: npiData.nPINumber,
              npiType: npiData.nPIType,
              npiStatus: npiData.nPIStatus,
              enumerationDate: npiData.enumerationDate,
              originalData: {
                ...npiData,
                practiceLocations,
                basicInfo,
                fullApiResponse: apiData,
              },
            };
          })
        : [];

      setDropdownOptions((prev) => ({ ...prev, groupNPI: options }));
    } catch (error) {
      console.error(" Error fetching Group NPI options:", error);
      setDropdownOptions((prev) => ({ ...prev, groupNPI: [] }));
    } finally {
      setLoadingOptions((prev) => ({ ...prev, groupNPI: false }));
    }
  }, []);

  const fetchProviderNPIOptions = useCallback(async () => {
    setLoadingOptions((prev) => ({ ...prev, providerNPI: true }));
    try {
      const response = await getProviderNpi({
        type: ["nPI", "basicInformation", "contactInformation.state"],
      });

      const providerResponse =
        response?.getProviderInformations?.data?.data || null;

      const apiData = providerResponse ? providerResponse : [];

      const options = Array.isArray(apiData)
        ? apiData.map((item: any, index: number) => {
            const providerData = item;
            const npiData = providerData?.nPI || {};
            const basicInfo = providerData?.basicInformation || {};
            const state = providerData?.contactInformation || "";

            const option = {
              id:
                npiData.nPINumber ||
                providerData.id ||
                providerData._id ||
                `provider_${index}`,
              value: `${npiData.nPINumber || "Unknown"}`,
              fullName: basicInfo?.fullName || "",
              firstName: basicInfo?.firstName || "",
              middleName: basicInfo?.middleName || "",
              lastName: basicInfo?.lastName || "",
              suffix: basicInfo?.suffix || "",
              providerType: basicInfo?.providerType || "",
              providerQualifications: basicInfo?.providerQualification || "",
              nUccGrouping: basicInfo?.nUCCGrouping || "",
              npi: npiData?.nPINumber || "",
              npiType: npiData?.nPIType || "",
              npiStatus: npiData?.nPIStatus || "",
              state: state?.state || "",
              enumerationDate: npiData?.enumerationDate || "",
              originalData: {
                ...providerData,
                fullApiResponse: apiData,
              },
            };
            return option;
          })
        : [];

      // If no options were created, add some test data for debugging
      if (options.length !== 0) {
        setDropdownOptions((prev) => ({ ...prev, providerNPI: options }));
      }
    } catch (error) {
      console.error("Error fetching Provider NPI options:", error);
      setDropdownOptions((prev) => ({ ...prev, providerNPI: [] }));
    } finally {
      setLoadingOptions((prev) => ({ ...prev, providerNPI: false }));
    }
  }, []);

  const fetchPayerNameOptions = useCallback(async () => {
    setLoadingOptions((prev) => ({ ...prev, payerName: true }));
    try {
      const response = await getAllPayers({
        page: 1,
        limit: 100,
        filters: "",
        selectedFields: { payerName: 1 },
      });

      // Try multiple possible response paths for getAllPayers
      const payers =
        response?.payers?.payers || // Most likely path
        response?.data?.payers?.payers ||
        response?.payers?.payers[0] ||
        response?.data?.payers?.payers[0] ||
        response?.payers ||
        response?.data?.payers ||
        response?.data ||
        response ||
        [];

      // If payers is not an array, try to extract it differently
      let payersArray = payers;
      if (!Array.isArray(payers) && payers && typeof payers === "object") {
        // Maybe the payers are nested in an object
        payersArray = Object.values(payers);
      }

      const options = Array.isArray(payersArray)
        ? payersArray.map((payer: any, index: number) => {
            const option = {
              id: payer._id || payer.id || `payer_${index}`,
              value: payer.payerName || "Unknown Payer",
              // Store additional payer data for auto-patching
              payerName: payer.payerName || payer.name || "",
              payerId: payer._id || payer.id || "",
              originalData: {
                ...payer,
                fullApiResponse: payersArray,
              },
            };

            return option;
          })
        : [];

      // If no options were created, add some test data for debugging
      if (options.length === 0) {
        const testOptions = [
          {
            id: "test_payer_1",
            value: "Blue Cross Blue Shield",
            payerName: "Blue Cross Blue Shield",
            payerId: "test_payer_1",
            originalData: {
              payerName: "Blue Cross Blue Shield",
              _id: "test_payer_1",
            },
          },
          {
            id: "test_payer_2",
            value: "Aetna",
            payerName: "Aetna",
            payerId: "test_payer_2",
            originalData: { payerName: "Aetna", _id: "test_payer_2" },
          },
        ];
        setDropdownOptions((prev) => ({ ...prev, payerName: testOptions }));
      } else {
        setDropdownOptions((prev) => ({ ...prev, payerName: options }));
      }
    } catch (error) {
      console.error(" Error fetching Payer Name options:", error);
      setDropdownOptions((prev) => ({ ...prev, payerName: [] }));
    } finally {
      setLoadingOptions((prev) => ({ ...prev, payerName: false }));
    }
  }, []);

  // Load dropdown options on component mount
  useEffect(() => {
    const loadAllOptions = async () => {
      await fetchGroupNPIOptions();
      await fetchProviderNPIOptions();
      await fetchPayerNameOptions();
    };

    loadAllOptions();
  }, [fetchGroupNPIOptions, fetchProviderNPIOptions, fetchPayerNameOptions]);

  const fieldValuesRef = useRef<Record<string, unknown>>({});
  const handleFieldValueChange = useCallback(
    (fieldId: string, value: string | number | boolean | string[] | null) => {
      // Update ref immediately
      fieldValuesRef.current = {
        ...fieldValuesRef.current,
        [fieldId]: value,
      };

      setFieldValues((prev) => ({
        ...prev,
        [fieldId]: value,
      }));

      // Clear validation error when field is updated
      if (validationErrors[fieldId]) {
        setValidationErrors((prev) => {
          const newErrors = { ...prev };
          delete newErrors[fieldId];
          return newErrors;
        });
      }

      // Auto-patch Entity Name when Group NPI is selected
      if (typeof value === "string" && value) {
        const allFields = Array.isArray(steps)
          ? steps.flatMap(
              (step) =>
                step.sections?.flatMap((section) => section.fields || []) || []
            )
          : [];

        const changedField = allFields.find((f) => f?.id === fieldId);
        const fieldLabel = changedField?.label?.toLowerCase() || "";

        // Check if this is a Group NPI field
        if (fieldLabel.includes("group") && fieldLabel.includes("npi")) {
          const selectedOption = dropdownOptions.groupNPI.find(
            (option) => option.id === value
          );

          if (selectedOption) {
            const orgData = (selectedOption as DropdownOption).originalData;
            const practiceLocations = orgData?.practiceLocations || {};
            const basicInfo = orgData?.basicInfo || {};
            const apiStates = practiceLocations?.state || [];
            const apiAddresses = practiceLocations?.address || [];
            const dynamicStates = apiStates.map(
              (stateItem: any, index: number) => ({
                id: `api_state_${index}`,
                value: stateItem.state || stateItem,
              })
            );

            const dynamicAddresses = apiAddresses.map(
              (addressItem: any, index: number) => ({
                id: `api_address_${index}`,
                value: addressItem.address || addressItem,
              })
            );

            // Update dynamic field options
            setDynamicFieldOptions({
              states: dynamicStates,
              addresses: dynamicAddresses,
            });
            // 1. ENTITY NAME - Only patch based on selected NPI ID
            const entityFields = allFields.filter((field) => {
              const label = field?.label?.toLowerCase() || "";
              return (
                label.includes("entity") &&
                (label.includes("name") || label === "entity")
              );
            });

            entityFields.forEach((entityField) => {
              if (entityField?.id && entityField.id !== fieldId) {
                const entityName = orgData?.entityName || "Unknown Entity";

                // Update both ref and state for the entity field
                fieldValuesRef.current = {
                  ...fieldValuesRef.current,
                  [entityField.id]: entityName,
                };

                setFieldValues((prev) => ({
                  ...prev,
                  [entityField.id]: entityName,
                }));

                // Clear any validation errors for the patched field
                setValidationErrors((prev) => {
                  const newErrors = { ...prev };
                  delete newErrors[entityField.id];
                  return newErrors;
                });
              }
            });

            // 2. DEFAULT PATCHING - TIN, State, Address (from first/default entry)
            const defaultFieldsToPatch = [
              {
                fieldPattern: ["tin", "tax", "identification"],
                value: basicInfo?.tin || "",
                type: "text",
              },
              {
                fieldPattern: ["address"],
                value:
                  dynamicAddresses.length > 0 ? dynamicAddresses[0].id : "",
                type: "dropdown",
                dropdownKey: "dynamic_address",
              },
              {
                fieldPattern: ["state"],
                value: dynamicStates.length > 0 ? dynamicStates[0].id : "",
                type: "dropdown",
                dropdownKey: "dynamic_state",
              },
            ];
            // Patch default fields
            defaultFieldsToPatch.forEach((patchConfig) => {
              if (!patchConfig.value) {
                return;
              }

              // Find fields matching the pattern
              const matchingFields = allFields.filter((field) => {
                const label = field?.label?.toLowerCase() || "";
                return patchConfig.fieldPattern.some((pattern) =>
                  label.includes(pattern)
                );
              });
              matchingFields.forEach((targetField) => {
                if (targetField?.id && targetField.id !== fieldId) {
                  let valueToSet = patchConfig.value;

                  if (
                    patchConfig.type === "dropdown" &&
                    patchConfig.dropdownKey
                  ) {
                    let dropdownOptionsForField: DropdownOption[] = [];

                    if (patchConfig.dropdownKey === "dynamic_address") {
                      dropdownOptionsForField = dynamicFieldOptions.addresses;
                    } else if (patchConfig.dropdownKey === "dynamic_state") {
                      dropdownOptionsForField = dynamicFieldOptions.states;
                    } else {
                      dropdownOptionsForField =
                        dropdownOptions[patchConfig.dropdownKey] || [];
                    }

                    if (
                      patchConfig.dropdownKey === "dynamic_address" ||
                      patchConfig.dropdownKey === "dynamic_state"
                    ) {
                      valueToSet = patchConfig.value;
                    } else {
                      const matchingOption = dropdownOptionsForField.find(
                        (option) =>
                          option.value
                            .toLowerCase()
                            .includes(patchConfig.value.toLowerCase()) ||
                          option.id.toLowerCase() ===
                            patchConfig.value.toLowerCase() ||
                          patchConfig.value
                            .toLowerCase()
                            .includes(option.value.toLowerCase())
                      );

                      if (matchingOption) {
                        valueToSet = matchingOption.id;
                      } else {
                        return;
                      }
                    }
                  }

                  // Update both ref and state for the target field
                  fieldValuesRef.current = {
                    ...fieldValuesRef.current,
                    [targetField.id]: valueToSet,
                  };

                  setFieldValues((prev) => ({
                    ...prev,
                    [targetField.id]: valueToSet,
                  }));

                  // Clear any validation errors for the patched field
                  setValidationErrors((prev) => {
                    const newErrors = { ...prev };
                    delete newErrors[targetField.id];
                    return newErrors;
                  });
                }
              });
            });
          } else {
          }
        }

        // Check if this is a Provider NPI field
        if (fieldLabel.includes("provider") && fieldLabel.includes("npi")) {
          const selectedOption = dropdownOptions.providerNPI.find(
            (option) => option.id === value
          );

          if (selectedOption) {
            const providerFieldsToPatch = [
              {
                fieldPattern: ["full", "name"],
                value: (selectedOption as any).fullName || "",
                type: "text",
                excludePatterns: ["group", "organization", "entity"],
              },
              {
                fieldPattern: ["fullname"],
                value: (selectedOption as any).fullName || "",
                type: "text",
                excludePatterns: ["group", "organization", "entity"],
              },
              {
                fieldPattern: ["complete", "name"],
                value: (selectedOption as any).fullName || "",
                type: "text",
                excludePatterns: ["group", "organization", "entity"],
              },
              {
                fieldPattern: ["first", "name"],
                value: (selectedOption as any).firstName || "",
                type: "text",
              },
              {
                fieldPattern: ["middle", "name"],
                value: (selectedOption as any).middleName || "",
                type: "text",
              },
              {
                fieldPattern: ["last", "name"],
                value: (selectedOption as any).lastName || "",
                type: "text",
              },
              {
                fieldPattern: ["suffix"],
                value: (selectedOption as any).suffix || "",
                type: "text",
              },
              {
                fieldPattern: ["provider", "suffix"],
                value: (selectedOption as any).suffix || "",
                type: "text",
              },
              {
                fieldPattern: ["provider", "type"],
                value: (selectedOption as any).providerType || "",
                type: "text",
              },
              {
                fieldPattern: ["provider", "qualification"],
                value: (selectedOption as any).providerQualifications || "",
                type: "text",
              },
              {
                fieldPattern: ["nucc", "grouping"],
                value: (selectedOption as any).nUCCGrouping || "",
                type: "text",
              },
              {
                fieldPattern: ["provider", "naics", "grouping"],
                value: (selectedOption as any).naicsGrouping,
                type: "text",
              },
              {
                fieldPattern: ["provider", "npi", "number"],
                value: (selectedOption as any).npi || "",
                type: "text",
              },
              {
                fieldPattern: ["provider", "npi", "type"],
                value: (selectedOption as any).npiType || "",
                type: "text",
              },
              {
                fieldPattern: ["provider", "npi", "status"],
                value: (selectedOption as any).npiStatus || "",
                type: "text",
              },
              {
                fieldPattern: ["provider", "enumeration", "date"],
                value: (selectedOption as any).enumerationDate || "",
                type: "text",
              },
            ];

            providerFieldsToPatch.forEach((patchConfig) => {
              if (!patchConfig.value) {
                return;
              }

              // Define patchedFieldValues to track already patched fields
              const patchedFieldValues: Record<string, unknown> = {};

              const matchingFields = allFields.filter((field) => {
                const normalizedLabel = field?.label
                  ?.toLowerCase()
                  .replace(/\s+/g, "");

                // Extra: Only patch fields that are not already patched
                if (patchedFieldValues[field.id] !== undefined) return false;

                const matchAll = patchConfig.fieldPattern.every((pattern) =>
                  normalizedLabel.includes(pattern.replace(/\s+/g, ""))
                );

                const matchSome = patchConfig.fieldPattern.some((pattern) =>
                  normalizedLabel.includes(pattern.replace(/\s+/g, ""))
                );

                if (patchConfig.fieldPattern.length > 1) {
                  return matchAll;
                } else {
                  return matchSome;
                }
              });

              matchingFields.forEach((targetField) => {
                if (targetField?.id && targetField.id !== fieldId) {
                  const fieldLabel = targetField?.label?.toLowerCase() || "";

                  // Additional safeguard: Don't patch Group/Organization fields when processing Provider NPI
                  if (
                    fieldLabel.includes("group") ||
                    fieldLabel.includes("organization") ||
                    fieldLabel.includes("entity")
                  ) {
                    return;
                  }

                  // Update both ref and state for the target field
                  fieldValuesRef.current = {
                    ...fieldValuesRef.current,
                    [targetField.id]: patchConfig.value,
                  };

                  setFieldValues((prev) => ({
                    ...prev,
                    [targetField.id]: patchConfig.value,
                  }));

                  // Clear any validation errors for the patched field
                  setValidationErrors((prev) => {
                    const newErrors = { ...prev };
                    delete newErrors[targetField.id];
                    return newErrors;
                  });
                }
              });
            });
          }
        }

        // Auto-patch Payer Name when Payer Name field is selected (like Group Information section)
        if (
          fieldLabel.includes("payer") &&
          (fieldLabel.includes("name") || fieldLabel.includes("payor"))
        ) {
          const selectedOption = dropdownOptions.payerName.find(
            (option) => option.id === value
          );

          if (selectedOption) {
            // 1. PAYER NAME - Only patch based on selected Payer ID (like Entity Name)
            const payerFields = allFields.filter((field) => {
              const label = field?.label?.toLowerCase() || "";
              return (
                label.includes("payer") &&
                (label.includes("name") || label === "payer")
              );
            });

            payerFields.forEach((payerField) => {
              if (payerField?.id && payerField.id !== fieldId) {
                const payerName =
                  (selectedOption as any).payerName || "Unknown Payer";

                // Update both ref and state for the payer field
                fieldValuesRef.current = {
                  ...fieldValuesRef.current,
                  [payerField.id]: payerName,
                };

                setFieldValues((prev) => ({
                  ...prev,
                  [payerField.id]: payerName,
                }));

                // Clear any validation errors for the patched field
                setValidationErrors((prev) => {
                  const newErrors = { ...prev };
                  delete newErrors[payerField.id];
                  return newErrors;
                });
              }
            });

            // 2. PAYER ID FIELDS (if any)
            const payerIdFields = allFields.filter((field) => {
              const label = field?.label?.toLowerCase() || "";
              return (
                label.includes("payer") &&
                (label.includes("id") || label.includes("identifier"))
              );
            });

            payerIdFields.forEach((payerIdField) => {
              if (payerIdField?.id && payerIdField.id !== fieldId) {
                const payerId = (selectedOption as any).payerId || "";
                if (payerId) {
                  // Update both ref and state for the payer ID field
                  fieldValuesRef.current = {
                    ...fieldValuesRef.current,
                    [payerIdField.id]: payerId,
                  };

                  setFieldValues((prev) => ({
                    ...prev,
                    [payerIdField.id]: payerId,
                  }));

                  // Clear any validation errors for the patched field
                  setValidationErrors((prev) => {
                    const newErrors = { ...prev };
                    delete newErrors[payerIdField.id];
                    return newErrors;
                  });
                }
              }
            });
          }
        }
      }
    },
    [validationErrors, steps, dropdownOptions, dynamicFieldOptions]
  );

  useEffect(() => {
    const triggerPrefill = async () => {
      if (!Array.isArray(steps) || !fieldValues) return;
      const allFields = steps.flatMap(
        (step) =>
          step.sections?.flatMap((section) => section.fields || []) || []
      );

      Object.entries(fieldValues).forEach(([fieldId, value]) => {
        if (typeof value !== "string" || !value || value.length === 0) return;

        const field = allFields.find((f) => f?.id === fieldId);
        if (!field) return;

        // Check if this field is a trigger field for prefill
        const fieldLabel = field.label?.toLowerCase() || "";
        const fieldGlobalsName = field.globals_name?.toLowerCase() || "";

        const isProviderField =
          (fieldLabel.includes("provider") ||
            fieldGlobalsName.includes("provider")) &&
          (fieldLabel.includes("npi") ||
            fieldLabel.includes("id") ||
            fieldGlobalsName.includes("npi") ||
            fieldGlobalsName.includes("id"));

        const isGroupField =
          (fieldLabel.includes("group") ||
            fieldGlobalsName.includes("group") ||
            fieldLabel.includes("organization") ||
            fieldGlobalsName.includes("organization")) &&
          (fieldLabel.includes("npi") ||
            fieldLabel.includes("id") ||
            fieldGlobalsName.includes("npi") ||
            fieldGlobalsName.includes("id"));

        const isPayerField =
          (fieldLabel.includes("payer") ||
            fieldGlobalsName.includes("payer")) &&
          (fieldLabel.includes("id") || fieldGlobalsName.includes("id"));

        let identifierType = "";
        let prefillFieldIds: string[] = [];

        if (isProviderField) {
          identifierType = "provider";
          if ((field as any).prefillFieldIds) {
            prefillFieldIds = (field as any).prefillFieldIds;
          }
        } else if (isGroupField) {
          identifierType = "group";
          if ((field as any).prefillFieldIds) {
            prefillFieldIds = (field as any).prefillFieldIds;

            const existingFields = allFields.filter((f) =>
              prefillFieldIds.includes(f.id)
            );
            if (existingFields.length === 0) {
              const groupNameField = allFields.find((f) =>
                f.label?.toLowerCase().includes("name")
              );

              if (groupNameField) {
                prefillFieldIds = [groupNameField.id];
              }
            }
          }
        } else if (isPayerField) {
          identifierType = "payer";
          if ((field as any).prefillFieldIds) {
            prefillFieldIds = (field as any).prefillFieldIds;
          }
        }
        if ((field as LocalFieldType).identifierKey) {
          identifierType = (field as LocalFieldType).identifierKey || "";
        } else if ((field as LocalFieldType).identifierType) {
          const explicitIdentifierType =
            (field as LocalFieldType).identifierType || "";
          if (explicitIdentifierType.length > 20) {
          } else {
            identifierType = explicitIdentifierType;
          }
        }

        if (prefillLoading[identifierType]) {
          return;
        }

        const apiCallKey = `${identifierType}_${fieldId}`;
        const enteredValue = (value as string).trim();

        if (!enteredValue) {
          delete lastApiCallValues.current[apiCallKey];
          if (debounceTimeouts.current[apiCallKey]) {
            clearTimeout(debounceTimeouts.current[apiCallKey]);
            delete debounceTimeouts.current[apiCallKey];
          }
          return;
        }

        if (lastApiCallValues.current[apiCallKey] === enteredValue) {
          return;
        }

        if (debounceTimeouts.current[apiCallKey]) {
          clearTimeout(debounceTimeouts.current[apiCallKey]);
        }

        if (identifierType) {
          let fieldsToFill: FieldType[] = [];

          if (prefillFieldIds.length > 0) {
            fieldsToFill = allFields.filter((f) =>
              prefillFieldIds.includes(f.id)
            );
          } else {
            fieldsToFill = allFields.filter((f) => {
              const hasPrefilledProperty = f.prefilled === true;
              const fieldIdentifierType = (f as LocalFieldType).identifierType;
              const fieldIdentifierKey = (f as LocalFieldType).identifierKey;

              let hasMatchingIdentifier = false;

              if (fieldIdentifierKey) {
                hasMatchingIdentifier = fieldIdentifierKey === identifierType;
              } else if (fieldIdentifierType) {
                if (fieldIdentifierType.length > 20) {
                  hasMatchingIdentifier =
                    fieldIdentifierType === identifierType;
                } else {
                  const normalizedFieldType = fieldIdentifierType
                    .toLowerCase()
                    .replace(/\s+/g, "");
                  const normalizedCurrentType = identifierType
                    .toLowerCase()
                    .replace(/\s+/g, "");

                  if (normalizedCurrentType === "payer") {
                    hasMatchingIdentifier =
                      normalizedFieldType.includes("payer") ||
                      normalizedFieldType.includes("payerid");
                  } else {
                    hasMatchingIdentifier =
                      normalizedFieldType === normalizedCurrentType;
                  }
                }
              }
              return hasPrefilledProperty && hasMatchingIdentifier;
            });
          }
          if (fieldsToFill.length > 0) {
            if (!enteredValue || enteredValue.length < 3) {
              return;
            }
            debounceTimeouts.current[apiCallKey] = setTimeout(() => {
              if (prefillLoading[identifierType]) {
                return;
              }
              lastApiCallValues.current[apiCallKey] = enteredValue;
              fetchPrefillData(identifierType, enteredValue, fieldsToFill);

              delete debounceTimeouts.current[apiCallKey];
            }, 1000);
          }
        }
      });
    };

    triggerPrefill();
  }, [fieldValues, steps, fetchPrefillData, prefillLoading]);
  useEffect(() => {
    return () => {
      Object.values(debounceTimeouts.current).forEach((timeout) => {
        clearTimeout(timeout);
      });
      debounceTimeouts.current = {};
    };
  }, []);

  useEffect(() => {
    if (!Array.isArray(steps)) {
      console.error("Steps is not an array in useEffect:", steps);
      return;
    }

    const allFields = steps?.flatMap(
      (step) => step.sections?.flatMap((section) => section.fields || []) || []
    );

    const initialValues: Record<string, unknown> = {};
    allFields?.forEach((field) => {
      if (field.field_type === "select" && field.options?.length) {
        initialValues[field.id] = field.options[0].value;
      } else if (field.field_type === "multiselect") {
        initialValues[field.id] = [];
      } else if (field.field_type === "checkboxes") {
        initialValues[field.id] = [];
      } else if (field.field_type === "toggle") {
        initialValues[field.id] = false;
      } else {
        initialValues[field.id] = "";
      }
    });

    // Initialize both ref and state with default values
    fieldValuesRef.current = {
      ...initialValues,
      ...fieldValuesRef.current,
    };

    setFieldValues((prev) => ({
      ...initialValues,
      ...prev,
    }));
  }, [steps]);

  // Helper function to get dropdown options for specific fields
  const getFieldOptions = useCallback(
    (field: FieldType) => {
      const fieldLabel = field?.label?.toLowerCase() || "";

      // Check if this field should use API-loaded options
      if (fieldLabel.includes("group") && fieldLabel.includes("npi")) {
        return dropdownOptions.groupNPI;
      } else if (
        fieldLabel.includes("provider") &&
        fieldLabel.includes("npi")
      ) {
        return dropdownOptions.providerNPI;
      } else if (fieldLabel.includes("state")) {
        const dynamicStates = dynamicFieldOptions.states;
        if (dynamicStates.length > 0) {
          return dynamicStates;
        }
        return [];
      } else if (fieldLabel.includes("address")) {
        // Use dynamic address options from API
        const dynamicAddresses = dynamicFieldOptions.addresses;
        if (dynamicAddresses.length > 0) {
          return dynamicAddresses;
        }
        return [];
      } else if (
        fieldLabel.includes("payer") &&
        (fieldLabel.includes("name") || fieldLabel.includes("payor"))
      ) {
        return dropdownOptions.payerName;
      }

      return (
        field?.options?.map((opt) => ({ id: opt.id, value: opt.value })) || []
      );
    },
    [dropdownOptions, dynamicFieldOptions]
  );

  // Memoized field mapping function
  const mapFieldForRenderer = useCallback(
    (field: FieldType): FieldOption => {
      const mappedColumns =
        (field as LocalFieldType).columns?.map(
          (col: GridColumn, idx: number) => ({
            ...col,
            id: col.id || `col-${idx}`,
            name: col.name || `Column ${idx + 1}`,
            fieldType: "string",
          })
        ) ?? undefined;

      // Get appropriate options for this field
      const fieldOptions = getFieldOptions(field);

      // Convert field to select type if it has API-loaded options and is currently text
      const fieldType =
        fieldOptions.length > 0 && field?.field_type === "text"
          ? "select"
          : field?.field_type;

      return {
        label: field?.label || "",
        field_type: fieldType,
        required: field?.required,
        options: fieldOptions,
        defaultValue: field?.placeholder,
        placeholder: field?.placeholder,
        globals_name: field?.globals_name,
        columns: mappedColumns,
        rows: (field as LocalFieldType).rows,
        name: field?.id,
      };
    },
    [getFieldOptions]
  );

  // Form validation function
  const validateForm = useCallback((): boolean => {
    const errors: Record<string, string> = {};
    const allFields = Array.isArray(steps)
      ? steps?.flatMap(
          (s) => s.sections?.flatMap((sec) => sec?.fields || []) || []
        )
      : [];

    allFields.forEach((field) => {
      if (field.required && fieldVisibilityMap[field.id]) {
        const value = fieldValuesRef.current[field.id];
        if (
          value === undefined ||
          value === null ||
          value === "" ||
          (Array.isArray(value) && value.length === 0)
        ) {
          errors[field.id] = `${field.label} is required`;
        } else if (field.field_type === "email" && typeof value === "string") {
          // Basic email validation
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(value)) {
            errors[field.id] = `Please enter a valid email address`;
          }
        } else if (field.field_type === "phone" && typeof value === "string") {
          // Basic phone validation (at least 10 digits)
          const phoneRegex = /\d{10,}/;
          if (!phoneRegex.test(value.replace(/\D/g, ""))) {
            errors[field.id] = `Please enter a valid phone number`;
          }
        }
      }
    });

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, [steps, fieldVisibilityMap]);

  // Helper function to convert string to camelCase
  const toCamelCase = (str: string): string => {
    return str
      .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => {
        return index === 0 ? word.toLowerCase() : word.toUpperCase();
      })
      .replace(/\s+/g, "")
      .replace(/[^a-zA-Z0-9]/g, "");
  };

  // Save current step data
  const saveCurrentStepData = useCallback(async () => {
    try {
      // Get current step fields
      const currentStep = steps.find((step) => step.id === selectedStep);
      if (!currentStep) return;

      // Combine with existing field values for processing
      const allValues = { ...fieldValuesRef.current, ...fieldValues };

      // Create section-wise data structure
      const sectionWiseData: Record<string, Record<string, unknown>> = {};

      currentStep.sections?.forEach((section) => {
        const sectionName = toCamelCase(section.name || "unknownSection");
        sectionWiseData[sectionName] = {};

        section.fields?.forEach((field) => {
          const fieldValue = allValues[field.id];
          if (
            fieldValue !== undefined &&
            fieldValue !== null &&
            fieldValue !== ""
          ) {
            // Convert field label to camelCase for the key
            const camelCaseKey = toCamelCase(field.label || field.id);
            sectionWiseData[sectionName][camelCaseKey] = fieldValue;
          }
        });

        // Remove empty sections
        if (Object.keys(sectionWiseData[sectionName]).length === 0) {
          delete sectionWiseData[sectionName];
        }
      });

      // Convert to array format as requested: [sectionName: {key: value}]
      const formattedData = Object.entries(sectionWiseData).map(
        ([sectionName, sectionData]) => ({
          [sectionName]: sectionData,
        })
      );

      // Call updateProviderNpi API to save the current step data
      const updatePayload = {
        input: {
          id: id as string,
          values: JSON.stringify(formattedData),
        },
      };

      const response = await updateProviderNpi(updatePayload);
      console.log(" updateProviderNpi API response:", response);

      showToast.success(`Step data saved successfully!`);
      return true;
    } catch (error) {
      console.error("Error saving step data:", error);
      showToast.error("Failed to save step data");
      return false;
    }
  }, [steps, selectedStep, id, fieldValues]);

  // Handle Exception button
  const handleException = useCallback(async () => {
    try {
      const exceptionData = {
        ticketId: id,
        stepId: selectedStep,
        status: "exception",
        reason: "User marked as exception",
        timestamp: new Date().toISOString(),
      };
      console.log("Marking as exception:", exceptionData);
      showToast.success("Marked as exception successfully!");
    } catch (error) {
      console.error("Error marking as exception:", error);
      showToast.error("Failed to mark as exception");
    }
  }, [id, selectedStep]);

  // Handle Incomplete button
  const handleIncomplete = useCallback(async () => {
    try {
      const incompleteData = {
        ticketId: id,
        stepId: selectedStep,
        status: "incomplete",
        reason: "User marked as incomplete",
        timestamp: new Date().toISOString(),
      };

      console.log("Marking as incomplete:", incompleteData);

      // TODO: Replace with actual API call
      // await markAsIncomplete(incompleteData);

      showToast.success("Marked as incomplete successfully!");
    } catch (error) {
      console.error("Error marking as incomplete:", error);
      showToast.error("Failed to mark as incomplete");
    }
  }, [id, selectedStep]);

  // Handle Move to QC button
  const handleMoveToQC = useCallback(async () => {
    if (!validateForm()) {
      showToast.error("Please fill in all required fields before moving to QC");
      return;
    }

    try {
      // Save current step data first
      const saveSuccess = await saveCurrentStepData();
      if (!saveSuccess) return;

      // Combine values from both ref and state for QC submission
      const allValues = { ...fieldValuesRef.current, ...fieldValues };

      const qcData = {
        ticketId: id,
        templateId: "6870e93ac6b1219039874336",
        values: JSON.stringify(allValues),
        status: "moved_to_qc",
        submittedAt: new Date().toISOString(),
      };

      console.log("Moving to QC:", qcData);

      // TODO: Replace with actual API call
      // await moveToQC(qcData);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Optionally redirect
      setTimeout(() => {
        router.push(`/tickets/${id}`);
      }, 2000);
    } catch (error) {
      console.error("Error moving to QC:", error);
      showToast.error("Failed to move to QC");
    }
  }, [validateForm, saveCurrentStepData, id, fieldValues, router]);

  // Validate current step
  const validateCurrentStep = useCallback((): boolean => {
    const currentStep = steps.find((step) => step.id === selectedStep);
    if (!currentStep) return true;

    const stepFields =
      currentStep.sections?.flatMap((section) => section.fields || []) || [];
    const stepErrors: Record<string, string> = {};

    stepFields.forEach((field) => {
      if (field.required && fieldVisibilityMap[field.id]) {
        const value = fieldValuesRef.current[field.id];
        if (
          value === undefined ||
          value === null ||
          value === "" ||
          (Array.isArray(value) && value.length === 0)
        ) {
          stepErrors[field.id] = `${field.label} is required`;
        }
      }
    });

    if (Object.keys(stepErrors).length > 0) {
      setValidationErrors((prev) => ({ ...prev, ...stepErrors }));
      showToast.error("Please fill in all required fields in this step");
      return false;
    }

    return true;
  }, [steps, selectedStep, fieldVisibilityMap]);

  // Navigate to next step
  const handleNextStep = useCallback(async () => {
    if (!validateCurrentStep()) {
      return;
    }

    // Save current step data before moving to next step
    const saveSuccess = await saveCurrentStepData();
    if (!saveSuccess) {
      showToast.error("Failed to save step data. Please try again.");
      return;
    }
    const currentIndex = steps.findIndex((step) => step.id === selectedStep);
    if (currentIndex < steps.length - 1) {
      setSelectedStep(steps[currentIndex + 1].id);
      showToast.success("Step completed and data saved!");
    }
  }, [steps, selectedStep, validateCurrentStep, saveCurrentStepData]);

  // Navigate to previous step
  const handlePreviousStep = useCallback(() => {
    const currentIndex = steps.findIndex((step) => step.id === selectedStep);
    if (currentIndex > 0) {
      setSelectedStep(steps[currentIndex - 1].id);
    }
  }, [steps, selectedStep]);

  if (loading || ticketLoading) {
    return (
      <div className="flex justify-center items-center h-[60vh]">
        <div className="text-center">
          <CircularProgress />
          <div className="mt-2 text-gray-600">
            {loading ? "Loading template..." : "Loading ticket data..."}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Success Message */}
      <Snackbar
        open={showSuccess}
        autoHideDuration={6000}
        onClose={() => setShowSuccess(false)}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
      >
        <Alert severity="success" onClose={() => setShowSuccess(false)}>
          Provider form submitted successfully! Redirecting...
        </Alert>
      </Snackbar>

      {/* Stepper UI */}
      <div className="flex space-x-1 mb-0 mt-3">
        {Array.isArray(steps) &&
          steps.map((step, index) => {
            const currentStepIndex = steps.findIndex(
              (s) => s.id === selectedStep
            );
            const isActive = index <= currentStepIndex;
            const isLast = index === steps.length - 1;

            let roundedClass = "";
            if (isActive && (isLast || index === currentStepIndex))
              roundedClass = "rounded-r-full";

            return (
              <Box
                key={step.id}
                onClick={() => setSelectedStep(step.id)}
                className={`flex-1 flex items-center justify-center mr-0.5 py-1 px-5 cursor-pointer transition-all duration-200 ${
                  isActive
                    ? `bg-gradient-to-r from-[#A357DF] to-[#0C4DB2] text-white ${roundedClass} m-0`
                    : "bg-[#EDF4FA] text-[#9DA7C1]"
                }`}
              >
                <div className="flex items-center space-x-2">
                  <div
                    className={`w-10 h-10 rounded-full flex items-center justify-center text-xs font-semibold ${
                      isActive
                        ? "bg-white text-[#0C4DB2] border-2 border-[#00B8B0]"
                        : "bg-white border-2 border-[#C2C4D0] text-[#A3AED0]"
                    }`}
                  >
                    {index + 1}
                  </div>
                  <span
                    className={`text-sm ${
                      isActive ? "text-white font-medium" : "text-[#9DA7C1]"
                    }`}
                  >
                    {step.name}
                  </span>
                </div>
              </Box>
            );
          })}
      </div>
      {/* Sections and Fields */}
      <div className="space-y-6">
        <Card className="border-2 border-gray-100 backdrop-blur-sm bg-white/90 shadow-xl hover:shadow-4xl transition-shadow duration-300 box-size !rounded-[4px]">
          {Array.isArray(steps) &&
            steps
              .find((step) => step.id === selectedStep)
              ?.sections?.map((section) => {
                // Check if this is a Provider section (multiple possible names)
                const sectionNameLower = section.name?.toLowerCase() || "";
                const providerKeywords = [
                  "provider",
                  "provider information",
                  "provider details",
                ];
                const isProviderSection = providerKeywords.some((keyword) =>
                  sectionNameLower.includes(keyword)
                );

                // Find enrollment type field with comprehensive search
                let enrollmentTypeField = null;
                let enrollmentTypeValue = null;

                // Try multiple approaches to find the enrollment type field
                const enrollmentTypeKeywords = [
                  "enrollment",
                  "enrolment",
                  "enrollmenttype",
                  "enrolmenttype",
                  "type",
                ];
                enrollmentTypeField = allFields.find((field) =>
                  enrollmentTypeKeywords.some(
                    (keyword) =>
                      field.label?.toLowerCase().includes(keyword) ||
                      field.id?.toLowerCase().includes(keyword)
                  )
                );
                enrollmentTypeValue = enrollmentTypeField
                  ? fieldValues[enrollmentTypeField.id]
                  : null;

                if (!enrollmentTypeField) {
                  for (const field of allFields) {
                    if (
                      field.field_type === "global_select" &&
                      field.label?.toLowerCase().includes("type")
                    ) {
                      enrollmentTypeField = field;
                      enrollmentTypeValue = fieldValues[field.id];
                      break;
                    }
                  }
                }

                // Last resort: find any field with "type" in the label
                if (!enrollmentTypeField) {
                  for (const field of allFields) {
                    if (field.label?.toLowerCase().includes("type")) {
                      enrollmentTypeField = field;
                      enrollmentTypeValue = fieldValues[field.id];
                      break;
                    }
                  }
                }

                // Check for "Individual" value (case-insensitive, handle different formats)
                const isIndividualEnrollment =
                  enrollmentTypeValue &&
                  (enrollmentTypeValue.toString().toLowerCase() === "group" ||
                    enrollmentTypeValue
                      .toString()
                      .toLowerCase()
                      .includes("Group"));

                if (isProviderSection && isIndividualEnrollment) {
                  return null;
                }

                return (
                  <div key={section.id}>
                    <div className="flex justify-between items-center p-3 bg-[#7592F9]/[0.05]  ">
                      <Typography sx={{ fontWeight: "bold", color: "#2B3674" }}>
                        {section.name}
                      </Typography>
                    </div>
                    <Divider className="mb-4" />
                    <div className="grid grid-cols-3 gap-4 p-4">
                      {section.fields
                        .filter(
                          (field) =>
                            fieldVisibilityMap[field.id] &&
                            !(field as any).hidden
                        )
                        .map((field) => (
                          <div
                            key={field.id}
                            className={`col-span-1 ${field.field_type === "grid" ? "col-span-3" : ""}`}
                          >
                            <div className="mt-1">
                              {/* Direct FieldRenderer usage to prevent re-renders */}
                              <div className="w-full">
                                <FieldRenderer
                                  field={mapFieldForRenderer(field)}
                                  setSelectedStateId={setSelectedStateId}
                                  selectedStateId={selectedStateId}
                                  allFields={allFields.map(mapFieldForRenderer)}
                                  fieldValues={fieldValues}
                                  value={
                                    fieldValues[field.id] as
                                      | string
                                      | number
                                      | boolean
                                      | string[]
                                      | null
                                      | undefined
                                  }
                                  onChange={(value) =>
                                    handleFieldValueChange(field.id, value)
                                  }
                                />
                                {validationErrors[field.id] && (
                                  <Typography
                                    variant="caption"
                                    color="error"
                                    className="mt-1 block text-red-500"
                                    sx={{
                                      fontSize: "0.75rem",
                                      marginTop: "4px",
                                    }}
                                  >
                                    {validationErrors[field.id]}
                                  </Typography>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                );
              })}
        </Card>
      </div>
      {/* Form Navigation and Submission */}
      <div className="bg-white p-4 rounded-sm shadow-sm border-gray-200 border">
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-600">
            Step {steps.findIndex((step) => step.id === selectedStep) + 1} of{" "}
            {steps.length}
          </div>
          <div className="flex space-x-3">
            {/* Get current step index */}
            {(() => {
              const currentStepIndex = steps.findIndex(
                (step) => step.id === selectedStep
              );
              const isFirstStep = currentStepIndex === 0;
              const isLastStep = currentStepIndex === steps.length - 1;

              return (
                <>
                  {/* Previous Button - Show on all steps except first */}
                  {!isFirstStep && (
                    <Button
                      text="Previous"
                      onClick={handlePreviousStep}
                      className="px-6 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded"
                    >
                      Previous
                    </Button>
                  )}

                  {/* Exception Button - Show on all steps */}
                  <Button
                    text="Exception"
                    onClick={handleException}
                    className="px-6 py-2 bg-[#FF9E6E] hover:bg-[#FF9E6E] text-white rounded"
                  >
                    Exception
                  </Button>

                  {/* Incomplete Button - Show on all steps */}
                  <Button
                    text="Incomplete"
                    onClick={handleIncomplete}
                    className="px-6 py-2 bg-[#EA6B6B] hover:bg-[#EA6B6B] text-white rounded"
                  >
                    Incomplete
                  </Button>

                  {/* Next Button - Show on all steps except last */}
                  {!isLastStep && (
                    <Button
                      text="Next"
                      onClick={handleNextStep}
                      className="px-6 py-2 bg-[#1567A7] hover:bg-[#1567A7] text-white rounded"
                    >
                      Next
                    </Button>
                  )}

                  {/* Move to QC Button - Show only on last step */}
                  {isLastStep && (
                    <Button
                      text="Move to QC"
                      onClick={handleMoveToQC}
                      className="px-6 py-2 bg-teal-500 hover:bg-teal-500 text-white rounded"
                    >
                      Move to QC
                    </Button>
                  )}
                </>
              );
            })()}
          </div>
        </div>
      </div>
    </div>
  );
}
