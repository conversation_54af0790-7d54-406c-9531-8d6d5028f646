"use client";
import React from "react";
import { TicketPermissionWrapper } from "@/components/permissions/PermissionWrapper";

type TicketFooterActionsProps = {
  onException: () => void;
  onIncomplete: () => void;
  onNext: () => void;
};

export default function TicketFooterActions({
  onException,
  onIncomplete,
  onNext,
}: TicketFooterActionsProps) {
  return (
    <div className="flex justify-end gap-4 p-4 bg-white border-t mt-6">
      {/* Exception button - only show if user has Update permission */}
      <TicketPermissionWrapper permissionName="Update">
        <button
          className="bg-orange-100 text-orange-700 px-4 py-2 rounded"
          onClick={onException}
        >
          Exception
        </button>
      </TicketPermissionWrapper>

      {/* Incomplete button - only show if user has Update permission */}
      <TicketPermissionWrapper permissionName="Update">
        <button
          className="bg-yellow-100 text-yellow-700 px-4 py-2 rounded"
          onClick={onIncomplete}
        >
          Incomplete
        </button>
      </TicketPermissionWrapper>

      {/* Next button - only show if user has View permission */}
      <TicketPermissionWrapper permissionName="View">
        <button
          className="bg-blue-600 text-white px-6 py-2 rounded"
          onClick={onNext}
        >
          Next
        </button>
      </TicketPermissionWrapper>
    </div>
  );
}
