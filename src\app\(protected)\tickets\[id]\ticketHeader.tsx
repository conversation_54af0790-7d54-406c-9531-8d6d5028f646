"use client";
import React from "react";

interface Ticket {
  id: string;
  provider: string;
  specialty: string;
  process: string;
  follower: string;
  status: string;
  receivedDate: string;
  lastUpdated: string;
  requestedBy: string;
}

export default function TicketHeader({ ticket }: { ticket: Ticket }) {
  const fields = [
    { label: "Ticket ID", value: ticket.id },
    { label: "Provider", value: ticket.provider },
    { label: "Specialty", value: ticket.specialty },
    { label: "Process", value: ticket.process },
    { label: "Follower", value: ticket.follower },
    { label: "Status", value: ticket.status },
    { label: "Received Date", value: ticket.receivedDate },
    { label: "Last Updated", value: ticket.lastUpdated },
    { label: "Requested By", value: ticket.requestedBy },
  ];

  return (
    <div className="bg-white px-2 py-4 rounded-md mb-4">
      <div className="flex overflow-x-auto gap-x-2 text-[13px] text-[#1C1C1C] min-w-max">
        {fields.map((field, idx) => (
          <div key={idx} className="min-w-[110px] flex-shrink-0">
            <p className="text-xs text-[#A0A3BD] mb-1">{field.label}</p>
            <p className="text-sm break-words">{field.value}</p>
          </div>
        ))}
      </div>
    </div>
  );
}
