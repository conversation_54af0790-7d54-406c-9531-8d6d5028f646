"use client";
import React from "react";
import clsx from "clsx";
import { Download } from "lucide-react";
import { Mail } from "lucide-react";
import OutlineTimer from "../../../../assests/outlinetimer.svg";
import KeyboardDoubleArrowLeftIcon from "@mui/icons-material/KeyboardDoubleArrowLeft";
import KeyboardDoubleArrowRightIcon from "@mui/icons-material/KeyboardDoubleArrowRight";
import Image from "next/image";

import SimpleTimer from "@/components/timer/SimpleTimer";
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
} from "@mui/lab";
// import { useParams } from "next/navigation";

type TicketHistoryItem = {
  user: string;
  date: string;
  status: "Current" | "Exception" | "Moved to QC" | "Completed";
  totalTime: string;
  exceptionType?: string;
  actionCode?: string;
  statusCode?: string;
  followUpDate?: string;
  comments?: string;
};

type TicketSidebarProps = {
  history: TicketHistoryItem[];
};

const statusStyles: Record<string, string> = {
  Current: "bg-[#4AC7BD] text-white",
  Exception: "bg-[#5585FF] text-white",
  "Moved to QC": "bg-[#FF9E6E] text-white",
  Completed: "bg-[#27B8AF] text-white",
};

export default function TicketSidebar({ history }: TicketSidebarProps) {
  // const params = useParams();
  // const id = params.id;
  const totalDuration = "06 : 34 : 15";
  const [isOpen, setIsOpen] = React.useState(true);
  const [showFullComments, setShowFullComments] = React.useState<
    Record<number, boolean>
  >({});

  return (
    <aside
      className={`transition-all duration-300 h-full flex flex-col  rounded-[5px] bg-white ${
        isOpen ? "w-[400px]" : "w-[40px] min-w-[40px]"
      }`}
      style={{
        overflow: "hidden",
        minWidth: isOpen ? 430 : 40,
        maxWidth: isOpen ? 430 : 40,
      }}
    >
      {/* Total Time Header */}
      {isOpen && (
        <div className="p-2 flex justify-between items-center bg-gray-100 rounded-t-[5px]">
          <div className="flex items-center gap-2">
            <p className="text-[15px] font-bold text-[#717C99]">Total Time</p>
            <div className="flex items-center gap-1">
              {totalDuration.split(" : ").map((part, idx, arr) => (
                <React.Fragment key={idx}>
                  <span className="font-mono text-[14px] border border-gray-300 px-1 py-0.5 bg-white rounded-md min-w-[30px] text-center">
                    {part}
                  </span>
                  {idx < arr.length - 1 && (
                    <span className="mx-0 text-lg font-bold text-gray-500">
                      :
                    </span>
                  )}
                </React.Fragment>
              ))}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button
              type="button"
              className="p-2 rounded-full bg-[white] border border-gray-200 hover:bg-gray-200 transition"
              title="Send Mail"
            >
              <Mail size={18} className="text-gray-600 " />
            </button>
            <button
              type="button"
              className="p-2 rounded-full hover:bg-gray-200 transition"
              title={isOpen ? "Close Sidebar" : "Open Sidebar"}
              onClick={() => setIsOpen((prev) => !prev)}
            >
              <KeyboardDoubleArrowLeftIcon
                className="text-[#1567A7]"
                style={{ fontSize: 20 }}
              />
            </button>
          </div>
        </div>
      )}
      {!isOpen && (
        <button
          type="button"
          className="p-2 rounded-full hover:bg-gray-200 transition"
          title="Open Sidebar"
          onClick={() => setIsOpen(true)}
          style={{
            position: "absolute",
            left: 8,
            top: "50%",
            transform: "translateY(-50%)",
            zIndex: 10,
          }}
        >
          <KeyboardDoubleArrowRightIcon
            className="text-[#1567A7]"
            style={{ fontSize: 20 }}
          />
        </button>
      )}

      {/* Timeline */}
      <div
        className={`flex-1 overflow-y-auto px-4 py-4 space-y-4 relative transition-all duration-300 ${
          isOpen ? "" : "px-0 py-0"
        }`}
      >
        {isOpen && (
          <>
            {/* Render "Current" status cards above the timeline */}
            {history.map((item, idx) =>
              item.status === "Current" ? (
                <div
                  key={idx}
                  className={clsx(
                    "border rounded-lg shadow-sm",
                    "border-gray-200"
                  )}
                  style={{ borderRadius: 5 }}
                >
                  <div className="p-4 space-y-1">
                    {/* Header */}
                    <div className="flex justify-between items-center">
                      <span className="font-semibold text-sm text-[#1765A7]">
                        {item.user}
                      </span>
                      <span
                        className={clsx(
                          "text-[12px] font-medium px-2 py-0.5 rounded-full",
                          statusStyles[item.status]
                        )}
                      >
                        {item.status}
                      </span>
                    </div>
                    <p className="text-xs text-[#414A5B] ">{item.date}</p>
                  </div>
                  {/* Footer */}
                  <div className="flex justify-between items-center p-3 bg-[#F8F8F8] rounded-b-lg">
                    <SimpleTimer
                      initialTime={item.totalTime}
                      showControls={item.status === "Current"}
                      className="text-[#2DA95C] text-sm"
                      onTimeUpdate={(time) => {
                        console.log(`Time updated for ${item.user}: ${time}`);
                      }}
                    />
                    <div className="flex items-center gap-2">
                      <span className="w-6 h-6 flex items-center justify-center rounded-full bg-white border border-gray-300 text-sm">
                        3
                      </span>
                      <span className="w-6 h-6 flex items-center justify-center rounded-full bg-[#E4E4E4] text-sm">
                        <Download size={14} />
                      </span>
                    </div>
                  </div>
                </div>
              ) : null
            )}

            {/* Timeline for non-"Current" items */}
            <Timeline position="right" sx={{ p: 0, m: 0 }}>
              {history.map((item, idx) =>
                item.status !== "Current" ? (
                  <TimelineItem
                    key={idx}
                    sx={{
                      "&::before": {
                        flex: 0,
                        padding: 0,
                      },
                      minHeight: "160px", // Adjust to match your card height
                      "& .MuiTimelineSeparator-root": {
                        alignItems: "center",
                        minHeight: "160px", // Same as above
                      },
                    }}
                  >
                    <TimelineSeparator>
                      <TimelineDot
                        sx={{
                          width: 14,
                          height: 14,
                          padding: 0,
                          bgcolor: idx === 0 ? "#222222" : "#E4E4E4",
                          border: idx === 0 ? "3px solid #B0B0B0" : "none",
                          boxShadow: idx === 0 ? "0 0 0 2px #fff" : "none",
                        }}
                      />
                      {idx !== history.length - 1 && (
                        <TimelineConnector
                          sx={{ bgcolor: "#E4E4E4", width: 2 }}
                        />
                      )}
                    </TimelineSeparator>
                    <TimelineContent sx={{ padding: "6px 0px 6px 16px" }}>
                      {/* Card for non-current items */}
                      <div
                        className={clsx(
                          "border rounded-lg shadow-sm",
                          "bg-white",
                          "border-gray-200"
                        )}
                        style={{ borderRadius: 5 }}
                      >
                        <div className="p-4 space-y-2">
                          {/* Header */}
                          <div className="flex justify-between items-center">
                            <span className="font-semibold text-sm text-[#1765A7]">
                              {item.user}
                            </span>
                            <span
                              className={clsx(
                                "text-[12px] font-medium px-2 py-0.5 rounded-full",
                                statusStyles[item.status]
                              )}
                            >
                              {item.status}
                            </span>
                          </div>
                          <p className="text-xs text-[#414A5B] mt-0.5">
                            {item.date}
                          </p>
                          {/* Metadata */}
                          <div className="grid grid-cols-[90px_1fr] gap-y-1">
                            {item.exceptionType && (
                              <div className="contents">
                                <span className="text-[12px] font-semibold text-[#717C99]">
                                  Exception Type
                                </span>
                                <span className="text-[12px] text-[#363B43]">
                                  : {item.exceptionType}
                                </span>
                              </div>
                            )}
                            {item.actionCode && (
                              <div className="contents">
                                <span className="text-[12px] font-semibold text-[#717C99]">
                                  Action Code
                                </span>
                                <span className="text-[12px] text-[#363B43]">
                                  : {item.actionCode}
                                </span>
                              </div>
                            )}
                            {item.statusCode && (
                              <div className="contents">
                                <span className="text-[12px] font-semibold text-[#717C99]">
                                  Status Code
                                </span>
                                <span className="text-[12px] text-[#363B43]">
                                  : {item.statusCode}
                                </span>
                              </div>
                            )}
                            {item.followUpDate && (
                              <div className="contents mt-1">
                                <span className="text-[12px] font-semibold text-[#717C99]">
                                  Followup Date
                                </span>
                                <span className="text-[12px] text-[#363B43] font-semibold ">
                                  : {item.followUpDate}
                                </span>
                              </div>
                            )}
                            {item.comments && (
                              <div className="contents mt-1 text-[#363B43]">
                                <span className="text-[12px] font-semibold text-[#717C99]">
                                  Comments
                                </span>
                                <span className="text-[12px] text-[#363B43] flex items-center font-semibold">
                                  :&nbsp;
                                  <span
                                    className={`flex-1 ${
                                      !showFullComments?.[idx] ? "truncate" : ""
                                    }`}
                                    style={{
                                      whiteSpace: !showFullComments?.[idx]
                                        ? "nowrap"
                                        : "normal",
                                      overflow: !showFullComments?.[idx]
                                        ? "hidden"
                                        : "visible",
                                      textOverflow: !showFullComments?.[idx]
                                        ? "ellipsis"
                                        : "unset",
                                      display: "block",
                                      maxWidth: !showFullComments?.[idx]
                                        ? "180px"
                                        : "none",
                                    }}
                                  >
                                    {!showFullComments?.[idx]
                                      ? item.comments.length > 30
                                        ? item.comments.slice(0, 30) + "..."
                                        : item.comments
                                      : item.comments}
                                  </span>
                                  {!showFullComments?.[idx] &&
                                    item.comments.length > 30 && (
                                      <span
                                        className="text-blue-500 cursor-pointer flex-shrink-0"
                                        onClick={() => {
                                          setShowFullComments((prev) => ({
                                            ...prev,
                                            [idx]: true,
                                          }));
                                        }}
                                      >
                                        More
                                      </span>
                                    )}
                                  {showFullComments?.[idx] &&
                                    item.comments.length > 30 && (
                                      <span
                                        className="text-blue-500 cursor-pointer ml-1 flex-shrink-0"
                                        onClick={() => {
                                          setShowFullComments((prev) => ({
                                            ...prev,
                                            [idx]: false,
                                          }));
                                        }}
                                      >
                                        Less
                                      </span>
                                    )}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                        {/* Footer */}
                        <div className="flex justify-between items-center p-3 bg-[#F8F8F8] rounded-b-lg">
                          <div className="flex items-center gap-2">
                            <Image
                              src={OutlineTimer}
                              alt="Timer Icon"
                              width={20}
                              height={20}
                            />
                            <span className="text-[#222222] text-[14px] font-normal">
                              Total Time Worked:
                            </span>
                            <span className="text-[#1465AB] text-[14px] font-bold">
                              {item.totalTime}
                            </span>
                          </div>
                          <div className="flex items-center gap-2 text-[#555555]">
                            <span className="w-6 h-6 flex items-center justify-center rounded-full bg-white border border-gray-300 text-sm">
                              3
                            </span>
                            <span className="w-6 h-6 flex items-center justify-center rounded-full bg-gray-300 text-sm">
                              <Download size={14} />
                            </span>
                          </div>
                        </div>
                      </div>
                    </TimelineContent>
                  </TimelineItem>
                ) : null
              )}
            </Timeline>
          </>
        )}
      </div>
    </aside>
  );
}
