"use client";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  CircularProgress,
  Typography,
} from "@mui/material";
import React, { useEffect } from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useState } from "react";
import { MenuItem, Button, InputLabel, Select } from "@mui/material";
import { getPreSignedUrl } from "@/api/file/file";
import { startImport } from "@/api/import/import";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { getTemplates } from "@/api/templates/templates";
import { showToast } from "@/components/toaster/ToastProvider";
import Cookies from "js-cookie";
// import { PermissionGuard, PermissionWrapper, hasSubModuleAccess } from '@/utils/permissions';

const Page = () => {
  const orgId = Cookies.get("orgId");
  const [emr, setEmr] = useState("upsert");
  const [fileType, setFileType] = useState("xlsx");
  const [selectedFile, setSelectedFile] = useState<string>("");
  const [loader, setLoader] = useState<boolean>(true);
  const [upload, setupload] = useState<boolean>(false);
  const [validationStrategy, setValidationStrategy] = useState<boolean>(false);
  const [templateName, setTemplateName] = useState("");
  const user = useSelector((state: RootState) => state.user.id);
  useEffect(() => {
    getTemplates({
      search: "",
      filters: { type: "provider_credentials", isActive: true },
    })
      .then((res) => {
        console.log(
          "res.templates.data.templates?.[0]._id",
          res.templates.data.templates?.[0]._id
        );

        setTemplateName(res.templates.data.templates?.[0]._id);
      })
      .catch((err) => {
        console.error(err);
      });
  }, [selectedFile]);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    setupload(true);
    if (file) {
      const path = `import/${file.name}`; // <-- desired path in storage
      const payload = {
        input: { filename: path as string, contentType: "application/pdf" },
      };

      try {
        const uploadPath = await getPreSignedUrl(payload);

        console.log("uploadPath", uploadPath.generateUploadUrl.data.uploadUrl);
        // const path = `organization/profile/${value}`; // <-- desired path in storage
        const uploadResponse = await fetch(
          uploadPath.generateUploadUrl.data.uploadUrl,
          {
            method: "PUT",
            body: file,
            headers: {
              "Content-Type": "*", // fallback
            },
          }
        );

        if (!uploadResponse.ok) {
          throw new Error("Something went wrong. Please try again.");
        }
        setLoader(false);
        setSelectedFile(path);
        console.log(uploadPath, "uploadPath");
      } catch (e) {
        setLoader(false);
        setupload(false);

        console.error("Image upload failed:", e);
      }
    }
  };

  const handleimport = () => {
    const payload = {
      input: {
        filePath: selectedFile,
        collectionName: "tickets",
        createdBy: user,
        type: emr,
        templateId: templateName,
        isStopOnError: validationStrategy,
        orgId: orgId,
      },
    };
    startImport(payload)
      .then((res) => {
        console.log(res);
        showToast.success(res.startImport.message);
        setSelectedFile("");
        setupload(false);
        setLoader(true);
      })
      .catch((err) => {
        console.log(err);
      });
  };
  // const headerProcess:any =  useSelector((state: RootState) => state.headerProcess);

  return (
    // <PermissionGuard moduleName="Tickets" subModuleName="Import" permissions={headerProcess?.role?.permissions}>
    <div className="py-4 space-y-8 bg-[white] !rounded-[16px] h-[100%]">
      {/* Debug section - remove in production */}
      {/* <div className="mb-4 p-4 bg-gray-100 rounded">
                    <Typography variant="subtitle2" className="mb-2">Permission Status:</Typography>
                    <p>Can access Import: {hasSubModuleAccess(headerProcess?.role?.permissions || [], 'Tickets', 'Import') ? '✅ Yes' : '❌ No'}</p>
                </div> */}

      <Box sx={{ width: "100%" }}>
        <Accordion defaultExpanded className="!mt-3">
          <AccordionSummary
            expandIcon={<ExpandMoreIcon />}
            className="!mb-0 !bg-[#7592F90D] !min-h-[40px]"
            sx={{
              "& .MuiAccordionSummary-content": {
                marginY: "0px !important",
              },
            }}
          >
            <div className="w-full flex justify-between items-center">
              <Typography
                variant="h6"
                sx={{ color: "#2B3674", fontWeight: 600, fontSize: "16px" }}
                className="!my-3"
              >
                {"Input Behaviour"}
              </Typography>
            </div>
          </AccordionSummary>
          <AccordionDetails className="!p-0">
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {/* EMR Dropdown */}
                <div className="w-full">
                  <InputLabel>Type</InputLabel>
                  <Select
                    className="w-full"
                    value={emr}
                    size="small"
                    // label="EMR"
                    onChange={(e) => setEmr(e.target.value)}
                  >
                    <MenuItem value="create">Create</MenuItem>
                    <MenuItem value="update">Update</MenuItem>
                    <MenuItem value="upsert">Create & Update</MenuItem>
                  </Select>
                </div>

                {/* Validation Strategy */}
                <div className="w-full">
                  <InputLabel>Validation Strategy</InputLabel>
                  <Select
                    className="w-full"
                    size="small"
                    value={validationStrategy}
                    // label="Validation Strategy"
                    onChange={(e) =>
                      setValidationStrategy(e.target.value === "true")
                    }
                  >
                    <MenuItem value="true">Stop on Error</MenuItem>
                    <MenuItem value="false">Skip error and execution</MenuItem>
                  </Select>
                </div>

                {/* File Type */}
                <div className="w-full">
                  <InputLabel>File Type</InputLabel>
                  <Select
                    value={fileType}
                    size="small"
                    className="w-full"
                    // label="File  Type"
                    onChange={(e) => {
                      setFileType(e.target.value);
                      setSelectedFile("");
                      setupload(false);
                    }}
                  >
                    <MenuItem value="xlsx">Excel</MenuItem>
                    <MenuItem value="csv">CSV</MenuItem>
                  </Select>
                </div>
              </div>

              {/* File Import Section */}
              <div className="mt-6 flex flex-col md:flex-row md:items-center gap-4">
                <label className="flex items-center gap-2">
                  <input
                    type="file"
                    onChange={handleFileChange}
                    className="hidden"
                    id="file-upload"
                    accept={`.${fileType}`}
                  />
                  <Button
                    variant="contained"
                    component="span"
                    onClick={() => document.getElementById("file-upload")}
                    className="!text-[14px] !h-[30px] text-gray-700 !p-4 !shadow-none !my-5"
                  >
                    Browse File
                  </Button>
                </label>
                <span className="text-gray-500">
                  {selectedFile ? (
                    selectedFile
                  ) : upload ? (
                    <CircularProgress size={12} />
                  ) : (
                    "No file selected."
                  )}
                </span>
              </div>

              {/* Import Actions */}
              <div className="m-0 flex gap-4 flex-wrap">
                {/* Start Import Button - Always visible if user has Import access */}
                <Button
                  variant="contained"
                  disabled={loader}
                  className="!text-[14px] !h-[30px] !bg-teal-400  text-teal-700 !p-4 !shadow-none !my-5"
                  onClick={() => handleimport()}
                >
                  Start Import
                </Button>

                {/* Example: Export functionality - only visible if user has Export permission */}
                {/* <PermissionWrapper
                                    moduleName="Tickets"
                                    subModuleName="All Ticket"
                                    permissionName="Export"
                                    permissions={headerProcess?.role?.permissions || []}
                                >
                                    <Button
                                        variant="outlined"
                                        className="!text-[14px] !h-[30px] !border-blue-400 !text-blue-700 !p-4 !shadow-none !my-5"
                                        onClick={() => showToast.info('Export functionality would be here')}
                                    >
                                        Export Template
                                    </Button>
                                </PermissionWrapper> */}

                {/* Example: Delete functionality - only visible if user has Delete permission */}
                {/* <PermissionWrapper
                                    moduleName="Tickets"
                                    subModuleName="All Ticket"
                                    permissionName="Delete"
                                    permissions={headerProcess?.role?.permissions || []}
                                >
                                    <Button
                                        variant="outlined"
                                        color="error"
                                        className="!text-[14px] !h-[30px] !p-4 !shadow-none !my-5"
                                        onClick={() => showToast.info('Delete functionality would be here')}
                                    >
                                        Clear History
                                    </Button>
                                </PermissionWrapper> */}
              </div>
            </div>
          </AccordionDetails>
        </Accordion>
      </Box>
    </div>
    // </PermissionGuard>
  );
};
export default Page;
