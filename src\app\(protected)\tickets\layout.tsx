"use client";
import TicketTabs from "@/components/providerCredential/TicketTabs";
import { getprovidermodulePermissionCommon } from "@/utils/generic";
import { usePathname, useRouter } from "next/navigation";
import React, { useMemo } from "react";

export default function TicketsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const router = useRouter();

  // Check permissions for each tab
  const allTicketsPermission = getprovidermodulePermissionCommon(
    "Tickets",
    "All Ticket"
  );
  const exceptionsPermission = getprovidermodulePermissionCommon(
    "Tickets",
    "Exception"
  );
  const completedPermission = getprovidermodulePermissionCommon(
    "Tickets",
    "Completed"
  );
  const sourcePermission = getprovidermodulePermissionCommon(
    "Tickets",
    "Source"
  );
  const importPermission = getprovidermodulePermissionCommon(
    "Tickets",
    "Import"
  );

  // Create array of enabled tabs with their original indices
  const enabledTabs = useMemo(() => {
    const tabs = [
      {
        label: "All Tickets",
        route: "/tickets/Alltickets",
        originalIndex: 0,
        enabled: allTicketsPermission?.isEnabled,
      },
      {
        label: "Exceptions",
        route: "/tickets/Exceptions",
        originalIndex: 1,
        enabled: exceptionsPermission?.isEnabled,
      },
      {
        label: "Completed",
        route: "/tickets/Completed",
        originalIndex: 2,
        enabled: completedPermission?.isEnabled,
      },
      {
        label: "Source",
        route: "/tickets/Source",
        originalIndex: 3,
        enabled: sourcePermission?.isEnabled,
      },
      {
        label: "Import",
        route: "/tickets/import",
        originalIndex: 4,
        enabled: importPermission?.isEnabled,
      },
    ];

    return tabs.filter((tab) => tab.enabled);
  }, [
    allTicketsPermission,
    exceptionsPermission,
    completedPermission,
    sourcePermission,
    importPermission,
  ]);

  const activeTab = useMemo(() => {
    // Find the active tab index in the filtered enabled tabs array
    const currentTab = enabledTabs.findIndex((tab) =>
      pathname.includes(tab.route.split("/").pop() || "")
    );
    return currentTab >= 0 ? currentTab : 0;
  }, [pathname, enabledTabs]);

  const handleTabChange = (idx: number) => {
    const selectedTab = enabledTabs[idx];
    if (selectedTab) {
      router.push(selectedTab.route);
    }
  };

  return (
    <div>
      <TicketTabs
        activeTab={activeTab}
        setActiveTab={handleTabChange}
        tabs={enabledTabs}
      />
      <main className="flex-1 overflow-auto p-4">{children}</main>
    </div>
  );
}
