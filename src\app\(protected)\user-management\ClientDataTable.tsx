/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import * as React from "react";
import { DataTable } from "../../[components]/tableGrid/DataTable";
// import { ClientData } from "@/types/user";
import {  useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
// import Pagination from "../../[components]/tableGrid/PaginationControls";
import { Button } from "@mui/material";
// import DeleteModal from "@/app/components/deleteModel";
import { showToast } from "@/components/toaster/ToastProvider";
import DeleteModal from "@/components/deleteModel";
import CreateDrawer from "./createDrawer";
import { deleteSystemUsers } from "@/api/user-management/systemUsers/systemUsers";
import { TableHeader } from "@/app/[components]/tableGrid/TableHeader";
import { usePathname, useRouter } from "next/navigation";
import { deleteOrgRoles } from "@/api/user-management/organization-roles/orgRoles";
import { deleteExports } from "@/api/exportAction/export";
import { clearExportId, setExportId } from "@/features/export/exportSlice";


export function ClientDataTable({title,handleGetApi,pagination,setQuery,query,role, handlePrint}:{title:string,handleGetApi:()=>void,pagination?:any,setQuery?: any,query?:any,role:any, handlePrint?:(filename:string)=>void;}) {
  // const { tableConfig, data: initialData } = useSelector((state: RootState) => state.client);
  const tableData: any = useSelector((state: RootState) => state.client);
  const [tableConfig, setTableConfig] = React.useState<any>(); // [tableConfig]
  const [clients, setClients] = React.useState<{ [key: string]: string | boolean }[]>([]);
  const [filters] = React.useState<Record<string, string[]>>({});
  const [sortColumn, setSortColumn] = React.useState<string>('');
  const [sortDirection, setSortDirection] = React.useState<"asc" | "desc" | null>(
    'asc'
  );
  const [drawerOpen,setDrawerOpen] = React.useState(false)
const [page, setPage] = React.useState(1);
const [openDelete, setOpenDelete] = React.useState(false);
const [_id, setId] = React.useState('');
  const [pageSize, setPageSize] = React.useState(10);
  const [totalItems, setTotalItems] = React.useState(0);
  const [totalPages, settotalPages] = React.useState(0);
  const pathname = usePathname()
  const router = useRouter();
 const dispatch = useDispatch();
  const exportId = useSelector((state: RootState) => state.exportId.exportId);
   const userId = useSelector((state: RootState) => state.user.id);

  const [ids, setIds]=React.useState<string[]>([])
  console.log('ids',ids);
  
  React.useEffect(()=>{
    if(exportId.length==0){
    setIds([])}
  },[exportId])

    React.useEffect(()=>{
      dispatch(clearExportId())
    },[])

React.useEffect(()=>{
  setPage(pagination?.page)
  setTotalItems(pagination?.total)
  settotalPages(pagination?.totalPages)
},[pagination])

  React.useEffect(()=>{
    console.log('tableData',tableData);
    
    setTableConfig(tableData.tableConfig);
    setClients(tableData.data);
    
  },[tableData])


  React.useEffect(()=>{
    setPageSize(query?.limit)
    setSortDirection(query?.sortOrder)
  },[query])
  

  const handleAddClient = () => {
    setId('')
    setTimeout(()=>{
      setDrawerOpen(true)
    },100)
    
  }
  const handleCloseDelete=()=>{
    setOpenDelete(false)
  }
  console.log('tableConfig',tableConfig);
  const handlePageChange = (newPage: number) => {
    console.log('totalSize',totalItems,totalPages,pageSize,pagination)
    if (newPage < 1 || newPage > totalPages) return;
    setPage(newPage);
     setQuery((prev: any) => ({
    ...prev,
    page: newPage,
  }));
  };

  const handlePageSizeChange = (newSize: number) => {
    console.log('totalSize',totalItems,totalPages,pageSize,pagination)
    setPageSize(newSize);
    setPage(1); // reset to page 1 when page size changes
        setQuery((prev: any) => ({
    ...prev,
    limit: newSize,
    }));
  };

  React.useEffect(()=>{
    setSortColumn(query.sortBy)
    setSortDirection(query.sortOrder)
  },[query])

 const handleToggleSelectAll = () => {
    if (!tableConfig?.settings?.selectable) return;

    const pageClientIds = filteredAndSortedClients.map(
      (client: any) => client._id
    );

    const allOnPageSelected = pageClientIds.every((id: any) =>
      ids.includes(id)
    );

    let newIds: string[];

    if (allOnPageSelected) {
      newIds = ids.filter((id) => !pageClientIds.includes(id));
    } else {
      newIds = [...new Set([...ids, ...pageClientIds])];
    }
    setIds(newIds);
    dispatch(setExportId(newIds));
  };

   const handleToggleSelect = (id: string) => {
    handleCheck(id)
  };

  const handleCheck = (id: string) => {
    let newData: string[];
  
    if (ids.includes(id)) {
      // Remove the id
      newData = ids.filter(item => item !== id);
    } else {
      // Add the id
      newData = [...ids, id];
    }
  
    setIds(newData);
    dispatch(setExportId(newData));
  };

  const handleEdit = (id: string) => {
     setId(id);
     if(pathname.includes(`user-management/systemUsers/`)){
      setDrawerOpen(true)
     }else if(pathname.includes(`user-management/roles-managements/`)){
      router.push(`/user-management/roles-managements/${id}/edit`);
     }else if(pathname.includes(`user-management/organization-roles/`)){
      router.push(`/user-management/organization-roles/${id}/edit`);
     }
     else if(pathname.includes(`/exports`)){
 router.push(`/exports/${id}/task-details`);
     }
  };

  const handleDelete = (id: string) => {
    setId(id);
    console.log(id)
    console.log("Delete client:", id,userId);
    setOpenDelete(true);
  };

 const submitDelete = async () => {
  console.log('Deleting ID:', _id);

  if (_id === userId) {
    showToast.error("You cannot delete the user account that is currently logged in.");
    setOpenDelete(false);
    return;
  }
  try {
    let response;
    if (title === 'System Users') {
      response = await deleteSystemUsers({ id: _id });
      showToast.success(response?.deleteCpt?.data?.message || 'System User deleted successfully.');
    }else if(title === `Org Roles and Management`){
      response = await deleteOrgRoles({ id: _id });
      showToast.success(response?.deleteCpt?.data?.message || 'Organization Roles deleted successfully.');
    }
   else if(title === `File Transfer`){
 response = await deleteExports({ taskId: _id });
      showToast.success(response?.deleteTask?.message );
   }

    
   if((filteredAndSortedClients.length === 1 || filteredAndSortedClients.length === 1) && page != 1){
    handlePageChange(page-1)
  await  handleGetApi();
  }else{
    await handleGetApi();
  }
    setOpenDelete(false);
  } catch (error:unknown) {
    console.error('Delete error:', error);
    showToast.error((error as any).message);
  }
};


 const handleFilterChange = (column: string, values: string[]) => {
  setQuery((prev: any) => {
  console.log('filters', prev.filters);

  let currentFilters = {};
  try {
    if (typeof prev?.filters === "string" && prev.filters.trim() !== "") {
      currentFilters = JSON.parse(prev.filters);
    } else if (typeof prev?.filters === "object") {
      currentFilters = prev.filters;
    }
  } catch (e) {
    console.warn("Invalid filters JSON:", e);
    currentFilters = {};
  }

  const updatedFilters = {
    ...currentFilters,
    [column]: values[0],
  };

  return {
    ...prev,
    filters: JSON.stringify(updatedFilters),
    page: 1
  };
});

  console.log('filter updated:', column, values[0]);
};
  const handleSort = (column: string) => {
    setQuery((prev: any) => ({
  ...prev,
  sortBy:column,
  sortOrder:prev.sortOrder === 'asc' ? 'desc' : 'asc'
}))
 setSortColumn(column);
 setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc')

  };
  const filteredAndSortedClients = React.useMemo(() => {
    let filtered = clients;

    // Apply filters
    if (tableConfig?.settings?.filterable) {
      Object.entries(filters).forEach(([column, values]) => {
        if (values.length > 0) {
          const columnConfig = tableConfig?.columns.find((col:{title:string}) => col.title === column);
          if (columnConfig) {
            filtered = filtered.filter((client) =>
              values.includes(String(client[columnConfig.id as keyof Record<string, string>]))
            );
          }
        }
      });
    }

    // Apply sorting
    if (sortColumn && sortDirection) {
      const columnConfig = tableConfig?.columns.find((col:{title:string}) => col.title === sortColumn);
      if (columnConfig?.sortable) {
        filtered = [...filtered].sort((a, b) => {
          const aVal = String(a[columnConfig.id as keyof Record<string, string>]).toLowerCase();
          const bVal = String(b[columnConfig.id as keyof Record<string, string>]).toLowerCase();

          if (sortDirection === "asc") {
            return aVal.localeCompare(bVal);
          } else {
            return bVal.localeCompare(aVal);
          }
        });
      }
    }
    return filtered;
    
  }, [clients, filters, sortColumn, sortDirection, tableConfig]);

  const allSelected =
    tableConfig?.settings?.selectable &&
    filteredAndSortedClients?.length > 0 &&
    filteredAndSortedClients?.every((client: any) => ids.includes(client._id));

  return (
    <main className="overflow-hidden mx-auto my-0 w-full bg-white rounded-xl max-w-[1900px] shadow-[0_1px_3px_rgba(0,0,0,0.1)]">
     
      <hr className="border-slate-100" />
      <div className="border border-slate-100 rounded-lg">
      <p className="px-4 mt-2 text-md font-semibold text-[#1465AB] flex justify-between items-center">{title} 
         {title!=='Sub Organizations' && title!=='Roles' && title!=='File Transfer' && role
  .filter((p:{isEnabled:boolean}) => p.isEnabled)
  .map((p:{displayName:string}) => p.displayName).includes('Add') && <span>
        <Button className="!h-[40px] rounded-[3px] !m-0 !bg-teal-500 p-2 w-[auto] !inset-shadow-none" onClick={handleAddClient}><span className="text-[14px] mx-2">Add New {title}</span></Button>
      </span>}
      </p>
       <hr className="mx-0 mt-2 border-slate-100" />
      {tableConfig?.columns?.length!==0  ? 
      <>
      <TableHeader 
      title={title}
              page={page}
              totalPages={totalPages}
              pageSize={pageSize}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              handleGetApi={handleGetApi}
              query={query}
               totalItems={totalItems}/>
      <DataTable
        tableConfig={{
          ...tableConfig,
          settings: {
            ...tableConfig?.settings,
            defaultSortDirection: query.sortOrder as "asc" | "desc"
          }
        }}
                     clients={filteredAndSortedClients?.map((client: any) => ({
                ...client,
                isSelected: ids.includes(client.id),
              }))}
        onToggleSelectAll={handleToggleSelectAll}
        onToggleSelect={handleToggleSelect}
        onEdit={handleEdit}
        onDelete={handleDelete}
         handleCheck={handleCheck}
        title={title}
        allSelected={allSelected}
        filters={filters}
        onFilterChange={handleFilterChange}
        handlePrint={handlePrint}
        onSort={handleSort}
        sortColumn={sortColumn}
        sortDirection={sortDirection}
        role={role}
      /></>
    :
    <h5 className="px-4 my-4 text-md font-semibold text-center">No Records found</h5>}
      {/* <Pagination
              page={page}
              totalPages={totalPages}
              pageSize={pageSize}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
            /> */}
             </div>
          { openDelete &&  <DeleteModal isOpen={openDelete} onClose={handleCloseDelete} onDelete={submitDelete} />}

          <CreateDrawer
                    open={drawerOpen}
                    _id={_id}
                    onClose={() => {setDrawerOpen(false)
                    }}
                    onSave={() => {
                      handleGetApi()
                      setQuery((prev: any) => ({
                        ...prev,
                        page: 1,
                      }));
                    }}
                    role={role}
                  />
                   <div style={{ display: "none" }}>
        
      </div>
    </main>
  );
}

export default ClientDataTable;
