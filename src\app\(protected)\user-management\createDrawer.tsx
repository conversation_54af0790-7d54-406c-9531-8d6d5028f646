/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';
import { <PERSON><PERSON>, Text<PERSON>ield, Typography, IconButton, InputLabel, MenuItem } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useEffect, useState } from "react";
import { createSystemUser, getSystemUserById, updateSystemUser } from "@/api/user-management/systemUsers/systemUsers";
import { showToast } from "@/components/toaster/ToastProvider";
import Loader from "@/components/loader/loader";
import { usePathname } from "next/navigation";
import { createOrgRoles } from "@/api/user-management/organization-roles/orgRoles";


const CreateDrawer = ({
  open,
  onClose,
  _id,
  onSave,
  role
}: {
  open: boolean;
  onClose: () => void;
  onSave: () => void;
  _id?: string;
  role:any
}) => {
  
  const permission =role?.filter((p:{isEnabled:boolean}) => p.isEnabled)
  .map((p:{displayName:string}) => p.displayName).includes('Edit') 
  const [name,setName] = useState('')
  const [empId,setEmpId] = useState('')
  const [roleName,setRoleName] = useState('')
  const [email,setEmail] = useState('')
  const [loader,setLoader] = useState(false)
  const [orgType, setOrgType] = useState('');
  const [status, setStatus] = useState('');
  const pathName = usePathname()
  const isOrgRoles = pathName.includes('user-management/organization-roles/');

  const handleChange = (label: string, value: string) => {
    switch (label) {
      case 'roleName':
        setRoleName(value);
        break;
      case 'name':
        setName(value);
        break;
      case 'empId':
        setEmpId(value);
        break;
      case 'email':
        setEmail(value);
        break;
      case 'orgType':
        setOrgType(value);
        break;
      case 'status':
        setStatus(value);
        break;
    }
  };

  useEffect(()=>{
    console.log('idddddd',_id)
    setLoader(true)
    if(_id && _id !== ''){
      getSystemUserById({id:_id})
              .then((res) => {
                setLoader(false)
                console.log(res, 'res.getUsersWithPagination.users');
                setName(res?.systemUser?.name)
                setRoleName(res?.systemUser?.roleName)
                setEmail(res?.systemUser?.email)
                setEmpId(res?.systemUser?.employeeId)
              })
              
              .catch((err) => {
                setLoader(false)
                console.error(err);
              });
    }else{
      
      setName('')
                setRoleName('')
                setEmail('')
                setEmpId('')
                setOrgType('')
                setStatus('')
                setLoader(false)
    }
  },[_id,open])

  const isEmpty = (value: string | null | undefined): boolean => {
    return !value || value.trim() === '';
  };

  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim());
  };

  const handleSave = () => {
    setLoader(true)
   
    if(isOrgRoles){
      if (isEmpty(name) || isEmpty(orgType) || isEmpty(status)) {
        showToast.error("Please fill in all required fields");
        setLoader(false);
        return;
      }
      const payload = {
        input: {
         name: name,
         type: orgType,
         isActive: status === 'Active' ? true : false,
        },
      };
      createOrgRoles(payload).then((res) => {
        showToast.success(res?.code);
        onClose()
        onSave()
        setOrgType('')
        setStatus('')
        setLoader(false)
      })
      .catch((err) => {
        showToast.error(err.message);
        console.error(err);
        setLoader(false)
      });
    }else{
      if (isEmpty(name) || isEmpty(email) || isEmpty(empId) || isEmpty(roleName)) {
        showToast.error("Please fill in all required fields");
        setLoader(false);
        return;
      }

      if (!isValidEmail(email)) {
        showToast.error("Please enter a valid email address");
        setLoader(false);
        return;
      }
      
      if(_id && _id !== ''){

        const payload = {
          input: {
           id:_id,
           name: name,
           email: email,
           employeeId: empId,
           roleName: roleName
          },
        };
      
        updateSystemUser(payload).then((res) => {
          showToast.success(res?.code);
          onClose()
          onSave()
          setRoleName('')
          setName('')
          setEmpId('')
          setEmail('')
          setLoader(false)
        })
        .catch((err) => {
          showToast.error(err.message);
          console.error(err);
          setLoader(false)
        });
      }else{
        const payload = {
          input: {
           name: name,
           email: email,
           employeeId: empId,
           roleName: roleName
          },
        };
      
         createSystemUser(payload).then((res) => {
          showToast.success(res?.code);
          onClose()
          onSave()
          setRoleName('')
          setName('')
          setEmpId('')
          setEmail('')
          setLoader(false)
        })
        .catch((err) => {
          showToast.error(err.message);
          console.error(err);
          setLoader(false)
        });
      }
    }
    
    

  };
  // console.log('columns', columns);

  return (
    <>
    {loader&&
      <Loader/>}
    <Drawer anchor="right" open={open} onClose={onClose}>
        <div className="w-[400px] p-6 flex flex-col h-full">
          <div className="flex justify-between items-center mb-4">
            <section className="w-full">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2 bg-blue-50 px-2 py-1 pt-[10px]">
                <Typography variant="h6" className="font-semibold text-gray-900">
                  {isOrgRoles ? `Org Roles and Management` : `System Users`}
                </Typography>
                <IconButton onClick={onClose}>
                  <CloseIcon />
                </IconButton>
              </div>
            </section>
          </div>

          <div className="flex-1 overflow-auto">
             <div className="flex flex-col mt-3">
                <InputLabel shrink className="!text-[22px] font-semibold text-gray-800">
                  Name
                </InputLabel>
                <TextField disabled={!permission} required fullWidth variant="outlined" value={name} onChange={(e) => handleChange('name', e.target.value)} />
              </div>
            {!isOrgRoles && (
              <>
                {/* Name */}
                

                {/* Email */}
                <div className="flex flex-col mt-3">
                <InputLabel shrink className="!text-[22px] font-semibold text-gray-800">
                  Email
                </InputLabel>
                <TextField disabled={!permission} required={!isOrgRoles ? true : false} fullWidth variant="outlined" slotProps={{
            input: {
              readOnly: _id && _id !== '' ? true : false,
            },
          }} value={email} onChange={(e) => handleChange('email', e.target.value)} />
                  
                </div>

                {/* Employee ID */}
                <div className="flex flex-col mt-3">
                <InputLabel shrink className="!text-[22px] font-semibold text-gray-800">
                  Employee Id
                </InputLabel>
                <TextField disabled={!permission} fullWidth variant="outlined" slotProps={{
            input: {
              readOnly: _id && _id !== '' ? true : false,
            },
          }}  required={!isOrgRoles ? true : false} value={empId} onChange={(e) => handleChange('empId', e.target.value)} />

                </div>

                {/* Role Name */}
                <div className="flex flex-col mt-3">
                <InputLabel  shrink className="!text-[22px] font-semibold text-gray-800">
                  Role Name
                </InputLabel>
                <TextField disabled={!permission}  required={!isOrgRoles ? true : false} fullWidth variant="outlined" value={roleName} onChange={(e) => handleChange('roleName', e.target.value)} />

                </div>
              </>
            )}

            {isOrgRoles && (
              <>
                {/* Organization Type */}
                <div className="flex flex-col mt-3">
                <InputLabel shrink className="!text-[22px] font-semibold text-gray-800">
                  Organization Type
                </InputLabel>
                <TextField
                disabled={!permission}
                required={isOrgRoles ? true : false}
  select
  fullWidth
  value={orgType}
  onChange={(e) => handleChange('orgType', e.target.value)}
>
  <MenuItem value="MAIN_ORGANISATION">MAIN_ORGANISATION</MenuItem>
  <MenuItem value="SUB_ORGANISATION">SUB_ORGANISATION</MenuItem>
</TextField>
                </div>

                {/* Status */}
                <div className="flex flex-col mt-3">
                <InputLabel shrink className="!text-[22px] font-semibold text-gray-800">
                  Status
                </InputLabel>
                <TextField
                disabled={!permission}
                required={isOrgRoles ? true : false}
  select
  fullWidth
  value={status}
  onChange={(e) => handleChange('status', e.target.value)}
>
  <MenuItem value="Active">Active</MenuItem>
  <MenuItem value="Inactive">Inactive</MenuItem>
</TextField>
                </div>
              </>
            )}
          </div>

          <div className="flex justify-end mt-6">
            <button disabled={!permission} className="bg-[#1969AE] hover:bg-[#155b96] text-white font-bold py-2 px-6 rounded w-[25%]" onClick={handleSave}>
              Save
            </button>
          </div>
        </div>
      </Drawer>
    </>
  );
};

export default CreateDrawer;
