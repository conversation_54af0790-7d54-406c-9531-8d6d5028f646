/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';
import { useEffect, useMemo, useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  ChevronsLeft,
  ChevronsRight,
} from 'lucide-react';
import { setSideNav } from '@/features/client/clientSlice';
import { useDispatch, useSelector } from 'react-redux';
import systemUserIcon from '../../../assests/systemUserIcon.svg'
import rolesManagementIcon from '../../../assests/rolesManagementIcon.svg'
import orgRolesIcon from '../../../assests/orgRolesIcon.svg'


import Image from 'next/image';
import { RootState } from '@/store';
export default function ClientsLayout({ children }: { children: React.ReactNode }) {
  const [collapsed, setCollapsed] = useState(false);
  const pathname = usePathname();
  const dispatch = useDispatch();
 const headerProcess:any =  useSelector((state: RootState) => state.headerProcess);
  
const [roles,setRoles]= useState<any>([])
console.log('roles',roles);
useEffect(() => {
  setRoles(
    headerProcess?.role?.permissions?.filter((item:{moduleName:string}) =>
      item.moduleName.includes('User Management')
    )
  );
}, [headerProcess]);
  useEffect(()=>{
    dispatch(setSideNav(collapsed));
  },[collapsed])


const availableLinks = useMemo(() => {
  if (!roles || roles.length === 0) return [];

  // Get the subModules from the first User Management role
  const subModules = roles[0]?.subModules || [];

  // Make a set of enabled submodule names
  const enabledSubModules = new Set(
    subModules
      .filter((sm: any) => sm.isEnabled)
      .map((sm: any) => sm.moduleName)
  );

  // Define all possible links
  const allLinks = [
    {
      href: `/user-management/systemUsers`,
      text: 'System Users',
      moduleName: 'System Users',
      icon: <Image src={systemUserIcon} alt="systemUserIcon" width={20} height={20} />
    },
    {
      href: `/user-management/roles-managements`,
      text: 'Roles and Managements',
      moduleName: 'Roles and Managements',
      icon: <Image src={rolesManagementIcon} alt="rolesManagementIcon" width={20} height={20} />
    },
    {
      href: `/user-management/organization-roles`,
      text: 'Org Roles and Managements',
      moduleName: 'Org Roles and Managements',
      icon: <Image src={orgRolesIcon} alt="orgRolesIcon" width={20} height={20} />
    }
  ];

  // Return only links whose moduleName is enabled
  return allLinks.filter(link => enabledSubModules.has(link.moduleName));
}, [roles]);

  return (
       <div className="flex" style={{ height: "calc(100vh - 191px)" }}>

      <aside
        className={`bg-[#F8F8F9] transition-all duration-300 ${
          collapsed ? 'w-20' : 'w-64'
        }`}
      >
        {/* Toggle Collapse Button */}
        <button
          onClick={() => setCollapsed(!collapsed)}
          className="flex items-center justify-end w-full py-4 pr-3"
        >
          {collapsed ? <ChevronsRight size={20} /> : <ChevronsLeft size={20} />}
        </button>

        {/* Navigation Links */}
 <nav className="flex flex-col">
  {availableLinks.map(({ href, text, icon }) => {
    const isActive = pathname.startsWith(href);
    return (
      <Link
        key={href}
        href={href}
        className={`${collapsed && "justify-center"} flex items-center  px-4 py-3 text-sm font-medium ${
          isActive
            ? 'text-gray-900 bg-blue-50 border-l-4 active:border-[#1465ab] bg-white'
            : 'text-gray-700 hover:bg-white hover:border-l-4 hover:border-[#1465ab] active:bg-white active:border-l-4 active:border-[#1465ab]'
        }`}
      >
        <span className={`${collapsed ? 'mr-0' : 'mr-3'}`}>{icon}</span>
        {!collapsed && <span>{text}</span>}
      </Link>
    );
  })}
</nav>
      </aside>

      {/* Main Content */}
      <main className="flex-1 p-4 overflow-auto">{children}</main>
    </div>
  );
}
