"use client";
import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import RolePermissionForm, { RoleWithPermissions } from "../../orgRolePermissionForm";
import Loader from "@/components/loader/loader";
import { getByIdOrgRoles } from "@/api/user-management/organization-roles/orgRoles";



export default function AddCPTDictionaryForm() {
  const [role, setRole] = useState<RoleWithPermissions | null>(null);
  const [loader,setLoader] = useState(true)

  const params = useParams();
  const rawId = params?.id;
  const id: string | null =
    typeof rawId === "string" ? rawId : Array.isArray(rawId) ? rawId[0] : null;

  useEffect(() => {
    if (!id) return;
    setLoader(true)
    getByIdOrgRoles({ id })
      .then((res) => {
        setLoader(false)
        console.log(res, "roles getBy");
        setRole(res.organisationRole); // Adjust if needed depending on API structure
      })
      .catch((err) => {
        console.error(err);
        setLoader(false)
      });
  }, [id]);

  if (!role || loader) return <Loader/>;

  return <RolePermissionForm role={role} />;

}
