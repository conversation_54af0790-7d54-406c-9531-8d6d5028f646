/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import * as React from "react";
import ClientDataTable from "../ClientDataTable";
import { useDispatch, useSelector, } from "react-redux";
import { setClientTable } from "@/features/client/clientSlice";
import { TableData } from "@/types/user"; // Update with your actual types
// import { form } from "./create/form";
// import { getOrganizationUser } from "@/api/organizations/organizations";
import { convertFormJsonFromClients, transformedClients } from "@/utils/generic";
// import { getTemplates } from "@/api/templates/templates";
import { setHeaders,setHeadersDefault } from "@/features/headers/headersSlice";
import Loader from "@/components/loader/loader";
// import { getSystemUsers } from "@/api/user-management/systemUsers/systemUsers";
import { getOrgList } from "@/api/user-management/organization-roles/orgRoles";
import { RootState } from "@/store";


export default function Page() {
  const dispatch = useDispatch();
      const headerProcess:any =  useSelector((state: RootState) => state.headerProcess);
  console.log('headerProcess',headerProcess);
  const [pagination, setPagination] = React.useState(1);
  const [loader, setLoader] = React.useState(false);
  const [query, setQuery] = React.useState({
    search: '',
    // filters:{},
    sortBy: '',
    sortOrder: 'asc',
    page: 1,
    limit: 10,
  });
const [roles,setRoles]= React.useState<any>([])
console.log('roles',roles);

React.useEffect(() => {
  console.log('headerProcess', );
  const role=headerProcess?.role?.permissions?.filter((item:{moduleName:string}) =>
      item.moduleName.includes('User Management')
    )[0].subModules?.filter((data:{moduleName:string})=>data.moduleName=="Org Roles and Managements")[0].permissions
  setRoles(role);
}, [headerProcess]);


  React.useEffect(() => {
    handleGetApi()
    console.log('each');
  }, [query]);


  const handleGetApi =async()=>{
    setLoader(true)
    
      // const result = Object.fromEntries(res.templates.data.templates[0]?.view_summary?.inGrid.map((key: any) => [key, 1]));
       const titles = [ "name","type","createdAt", "updatedAt","isActive"];
          dispatch(setHeaders(titles))
          dispatch(setHeadersDefault(titles as any))
     await getOrgList(query)
        .then((res) => {
          console.log(res?.organisationRoles, 'res.getUsersWithPagination.users');
          const data = transformedClients(res?.organisationRoles?.items);
          const tableData = convertFormJsonFromClients(data);
          dispatch(setClientTable(tableData as TableData));
          setPagination(res?.organisationRoles?.pagination)
          setLoader(false)
        })
        
        .catch((err) => {
          setLoader(false)
          console.error(err);
        });
  };
  return(
    <>
{loader&&
<Loader/>}
<ClientDataTable title={"Org Roles and Management"} handleGetApi={handleGetApi} setQuery={setQuery} query={query} pagination={pagination} role={roles} />
 </>
  ) 
}
