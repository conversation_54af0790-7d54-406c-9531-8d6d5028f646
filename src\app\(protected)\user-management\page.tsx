"use client";
import { getOrganizationSubModules } from "@/utils/generic";
import { Typography } from "@mui/material";
import { useRouter } from "next/navigation";
import React, { useState } from 'react'

const Page = () => {
  const [loader, setLoader]=useState(true)
  const router=useRouter()
React.useEffect(()=>{
    if(getOrganizationSubModules('User Management').filter((item)=>item.moduleName=='System Users')?.[0]?.isEnabled)
 { router.push('/user-management/systemUsers')}
    else if(getOrganizationSubModules('User Management').filter((item)=>item.moduleName=='Roles and Managements')?.[0]?.isEnabled){
router.push('/user-management/roles-managements')
    }
    else if(getOrganizationSubModules('User Management').filter((item)=>item.moduleName=='Org Roles and Managements')?.[0]?.isEnabled){
 router.push('/user-management/organization-roles')
    }
    else{
      setLoader(false)
    }
},[])
  return (
    !loader && <div className="py-4 space-y-8 bg-[white] !rounded-[16px] h-[100%] flex items-center justify-center">
        <Typography variant="h6" sx={{ color: "#2B3674", fontWeight: 600, fontSize: '16px' }}>
          {`Access Denied: You don't have permission to access this page.`}
        </Typography>      </div>
  )
}

export default Page
