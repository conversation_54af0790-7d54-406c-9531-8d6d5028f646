"use client";
import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { getByIdRoles } from "@/api/user-management/roles-permission/rolesPermission";
import RolePermissionForm, { RoleWithPermissions } from "../../rolePermissionForm";
import Loader from "@/components/loader/loader";



export default function AddCPTDictionaryForm() {
  const [role, setRole] = useState<RoleWithPermissions | null>(null);
  const [loader,setLoader] = useState(true)

  const params = useParams();
  const rawId = params?.id;
  const id: string | null =
    typeof rawId === "string" ? rawId : Array.isArray(rawId) ? rawId[0] : null;

  useEffect(() => {
    if (!id) return;
    setLoader(true)
    getByIdRoles({ id })
      .then((res) => {
        setLoader(false)
        console.log(res, "roles getBy");
        setRole(res.role); // Adjust if needed depending on API structure
      })
      .catch((err) => {
        console.error(err);
        setLoader(false)
      });
  }, [id]);

  if (!role || loader) return <Loader/>;

  return <RolePermissionForm role={role} />;

}
