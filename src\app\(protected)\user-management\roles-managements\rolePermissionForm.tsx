/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import {
  Typo<PERSON>,
  Switch,
  RadioGroup,
  FormControlLabel,
  Radio,
  Button,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import React, { useEffect, useState } from "react";
import { updateRolesPermissions } from "@/api/user-management/roles-permission/rolesPermission";
import { useParams, useRouter } from "next/navigation";
import { showToast } from "@/components/toaster/ToastProvider";
import { useSelector } from "react-redux";
import { RootState } from "@/store";

interface Permission {
  displayName: string;
  isEnabled: boolean;
}

interface SubModule {
  displayName: string;
  isEnabled: boolean;
  permissions: Permission[];
}

export interface Module {
  moduleName: string;
  displayName: string;
  isEnabled: boolean;
  subModules: SubModule[];
}

export interface RoleWithPermissions {
  category: string
  name: string;
  permissions: Module[];
}

const RolePermissionForm = ({ role }: { role: RoleWithPermissions }) => {
  const [formState, setFormState] = useState<Module[]>(() =>
    JSON.parse(JSON.stringify(role.permissions))
  );
  const headerProcess:any =  useSelector((state: RootState) => state.headerProcess);
 
  // const headerProcess: any = useSelector((state: RootState) => state.headerProcess);
  const params = useParams();
  const rawId = params?.id;
  const id: string | null = typeof rawId === "string" ? rawId : Array.isArray(rawId) ? rawId[0] : null;
  const router = useRouter();

  const toggleModule = (index: number) => {
    
    const updated = JSON.parse(JSON.stringify(formState));
    const module1 = updated[index];
    console.log('moduleeeee',updated[index])
    module1.isEnabled = !module1.isEnabled;

    if (!module1.isEnabled) {
      module1.subModules.forEach((sub: { isEnabled: boolean; permissions: any[]; }) => {
        sub.isEnabled = false;
        sub.permissions.forEach((perm: { isEnabled: boolean; }) => perm.isEnabled = false);
      });
    }else{
      module1.subModules.forEach((sub: { isEnabled: boolean; permissions: any[]; }) => {
        sub.isEnabled = true;
        sub.permissions.forEach((perm: { isEnabled: boolean; }) => perm.isEnabled = true);
      });
    }

    setFormState(updated);
  };

  const toggleSubModule = (modIndex: number, subIndex: number) => {
    const updated = JSON.parse(JSON.stringify(formState));
  
    const subModule = updated[modIndex].subModules[subIndex];
    subModule.isEnabled = !subModule.isEnabled;
  
    if (!subModule.isEnabled) {
      subModule.permissions.forEach((perm: { isEnabled: boolean }) => perm.isEnabled = false);
    } else {
      subModule.permissions.forEach((perm: { isEnabled: boolean }) => perm.isEnabled = true);
    }
    const anySubEnabled = updated[modIndex].subModules.some(
      (sub: { isEnabled: boolean }) => sub.isEnabled
    );
  
    updated[modIndex].isEnabled = anySubEnabled;
  
    setFormState(updated);
  };

  const toTitleCase = (str: string) =>
    str
      .toLowerCase()
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

  const updatePermission = (
    modIndex: number,
    subIndex: number,
    permIndex: number,
    value: boolean
  ) => {
    const updated = JSON.parse(JSON.stringify(formState));
    const permission = updated[modIndex].subModules[subIndex].permissions[permIndex];
    permission.isEnabled = value;

    // If all permissions are false, disable submodule
    const allPermsDisabled = updated[modIndex].subModules[subIndex].permissions.every((p: { isEnabled: any; }) => !p.isEnabled);
    if( allPermsDisabled === false){
      updated[modIndex].subModules[subIndex].isEnabled = !allPermsDisabled;
    } 
    
    
    console.log('updated[modIndex].subModules[subIndex].isEnabled',updated[modIndex].subModules[subIndex].isEnabled , allPermsDisabled)

    // If all submodules are disabled, disable module
    const allSubDisabled = updated[modIndex].subModules.every((s: { isEnabled: any; }) => !s.isEnabled);
    updated[modIndex].isEnabled = !allSubDisabled;

    setFormState(updated);
  };

  const [roles,setRoles]= React.useState<any>([])
  console.log('roles',roles);
  
  React.useEffect(() => {
    console.log('headerProcess', );
    const role=headerProcess?.role?.permissions?.filter((item:{moduleName:string}) =>
        item.moduleName.includes('User Management')
      )[0].subModules?.filter((data:{moduleName:string})=>data.moduleName=="Org Roles and Managements")[0].permissions
    setRoles(role
  .filter((p:{isEnabled:boolean}) => p.isEnabled)
  .map((p:{displayName:string}) => p.displayName).includes('Edit') );
  }, [headerProcess])

  const handleSubmit = () => {
    const payload = {
      input: {
        roleId: id, // ensure this comes from backend _id
        permissions: formState,
      },
    };
    updateRolesPermissions(payload) .then((res) => {
      console.log(res, "roles getBy");
      showToast.success("Permissions Updated Successfully")
      router.push(`/user-management/roles-managements`);
    })
    .catch((err) => {
      console.error(err);
      showToast.error(err?.err?.message)
    });
  };

  useEffect(()=>{
    console.log('form',formState)
  },[])

  return (
    <div className="p-6 bg-white rounded-md">
      {/* Role Name */}
      <div className="mb-6">
  <div className="flex gap-4">
    {/* First input with label */}
    <div className="flex w-1/2">
    <div className="mr-3">
    <label className="block !text-[#2B3674] !font-bold text-base leading-7 tracking-[-0.02em] font-dm !mb-2">
        Role Name
      </label>
      <input
        type="text"
        disabled
        value={role.name}
        className="w-75 px-4 py-2 bg-gray-100 border border-gray-300 text-gray-700 rounded-md"
      />
    </div>

    <div>
    <label className="block !text-[#2B3674] !font-bold text-base leading-7 tracking-[-0.02em] font-dm !mb-2">
        Category
      </label>
      <input
        type="text"
        disabled
        value={role.category}
        className="w-75 px-4 py-2 bg-gray-100 border border-gray-300 text-gray-700 rounded-md"
      />
    </div>
     
    </div>

    {/* Second input with its own label */}
    <div className="w-1/2">
      
    </div>
  </div>
</div>


      {/* Permissions List */}
      {formState.map((mod, modIndex) => (
        <Accordion key={mod.moduleName} defaultExpanded={mod.isEnabled}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />} >
            <div className="flex justify-between items-center w-full">
              <Typography className="!text-[#1765A7] font-medium text-base">
                {toTitleCase(mod.displayName)}
              </Typography>
              <Switch
              disabled={!roles}
                checked={mod.isEnabled || false}
                onChange={(e) => {
                  e.stopPropagation()
                  toggleModule(modIndex)
                }}
                color="primary"
              />
            </div>
          </AccordionSummary>
          <AccordionDetails className="bg-[#F6F9FF] rounded-b-md">
            {mod.subModules.map((sub, subIndex) => (
              <div
                key={sub.displayName}
                className="mb-4"
              >
                <div className="flex justify-between items-center mb-3">
                  <Typography className="!text-[#2B3674] !font-bold text-base leading-7 tracking-[-0.02em] font-dm"
                  >
                    {toTitleCase(sub.displayName)}
                  </Typography>
                  <Switch
                   disabled={!roles}
                    checked={sub.isEnabled}
                    onChange={() => toggleSubModule(modIndex, subIndex)}
                    color="primary"
                  />
                </div>
                {/* <Divider className="mb-4" /> */}
                
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mt-5 !bg-[#FCFDFF]">
                    {sub.permissions.map((perm, permIndex) => (
                      perm.displayName!=='View' &&
                      <div key={perm.displayName} className="p-4">
                        <Typography className="!text-black !font-medium text-base leading-[100%] tracking-[-0.02em] font-dm"
                        >
                          {toTitleCase(perm.displayName)}
                        </Typography>
                        <RadioGroup
                          row
                          
                          value={perm.isEnabled ? "yes" : "no"}
                          onChange={(e) =>
                            updatePermission(
                              modIndex,
                              subIndex,
                              permIndex,
                              e.target.value === "yes"
                            )
                          }
                        >
                          <FormControlLabel
                           disabled={!roles}
                            value="yes"
                            control={<Radio size="small" color="primary" />}
                            label="Yes"
                          />
                          <FormControlLabel
                           disabled={!roles}
                            value="no"
                            control={<Radio size="small" color="primary" />}
                            label="No"
                          />
                        </RadioGroup>
                      </div>
                    ))}
                  </div>
                
              </div>
            ))}
          </AccordionDetails>
        </Accordion>
      ))}

      {/* Submit */}
      
      <div className="flex justify-end px-4">
              <Button className="!h-[40px] rounded-[3px] !my-4 !bg-teal-500 p-2 w-[auto] !inset-shadow-none"  disabled={!roles} onClick={handleSubmit}><span className="text-[14px] mx-2">Submit</span></Button>
      </div>
    </div>
  );
};

export default RolePermissionForm;
