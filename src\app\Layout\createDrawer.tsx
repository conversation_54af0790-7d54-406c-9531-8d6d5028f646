/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';
import { Avatar, Drawer } from "@mui/material";

import Loader from "@/components/loader/loader";
import { useEffect, useState } from "react";
import { getNotificationList } from "@/api/header/header";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { formatDistanceToNow } from 'date-fns';
import { useRouter } from "next/navigation";

type PriorityLevel = 'HIGH' | 'MEDIUM' | 'LOW';

const priorityColors: Record<PriorityLevel, string> = {
  HIGH: '#ff4d4f',
  MEDIUM: '#faad14',
  LOW: '#52c41a'
};
const CreateDrawer = ({
  open,
  onClose,
  
}: {
  open: boolean;
  onClose: () => void;
 
}) => {
  const [loader,setLoader] = useState(false)
  const userId = useSelector((state: RootState) => state.user.id);
  const [notifications, setNotifications] = useState<any[]>([]);
  const router = useRouter()
  useEffect(() => {
    if (!open) return;
    setLoader(true);
    const payload = { input: { 
      userId,
      page: 1,
      limit: 10,
     } };

    getNotificationList(payload)
      .then((res) => {
        setNotifications(res?.notificationHistory?.notifications || []);
        setLoader(false);
      })
      .catch((err) => {
        console.error(err);
        setLoader(false);
      });
  }, [open]);

  const getAvatarColor = (priority: string) =>
    priorityColors[priority as PriorityLevel] || '#d9d9d9';

  return (
    <>
    {loader&&
      <Loader/>}
     <Drawer anchor="right" open={open} onClose={onClose}>
        <div className="w-[400px] flex flex-col h-full bg-white overflow-y-auto">
          <h2 className="text-xl text-white bg-teal-500 font-semibold mb-4 p-4">Notifications</h2>
          {notifications.map((note, idx) => {
            const name = (note.senderName || 'NA').slice(0, 2).toUpperCase();
            const bgColor = note.isRead ? 'bg-gray-100' : 'bg-white';
            const avatarBg = getAvatarColor(note.priority);

            return (
              <div
                key={idx}
                className={`flex items-start gap-3 p-3 mb-3 rounded-lg shadow-sm ${bgColor}`}
              >
                <Avatar sx={{ bgcolor: avatarBg }}>{name}</Avatar>
                <div className="flex-1">
                  <p className="text-sm font-medium">{note.message}</p>
                  <span className="text-xs text-gray-500">
                    {formatDistanceToNow(new Date(note.createdAt), { addSuffix: true })}
                  </span>
                </div>
              </div>
            );
          })}
          
        </div>
        <div className="p-4 border-t border-gray-200">
    <button
      onClick={() => {
        router.push(`/notifications/`)
        onClose()
      }} // Replace with actual navigation
      className="w-full py-2 text-sm font-semibold text-white bg-teal-500 hover:bg-teal-600 rounded-md"
    >
      View All
    </button>
  </div>
      </Drawer>
    </>
  );
};

export default CreateDrawer;
