/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import * as React from "react";
import {
  Select,
  MenuItem,
  FormControl,
  styled,
  Badge,
  IconButton,
} from "@mui/material";
import Image from "next/image";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import { useMsal } from "@azure/msal-react";
import { useSelector, useDispatch } from "react-redux";
import { RootState, persistor } from "../../store";
import { clearUser } from "../../features/users/userSlice";
import {
  setClearProcess,
  setProcess,
  setRole,
} from "@/features/headers/headersSlice";
import {
  getNotificationCount,
  markAsAllRead,
  selectHeaders,
} from "@/api/header/header";
import NotificationsIcon from "@mui/icons-material/Notifications";
import logo from "../../assests/rcm-logo.png";
import userLogo from "../../assests/userImage.png";
import { usePathname, useRouter } from "next/navigation";
import { setOrgList } from "@/features/client/clientSlice";
import Cookies from "js-cookie";
import { setLoading } from "@/features/headers/loaderSlice";
import { useIdleTimer } from "@/hooks/useIdleTimer";
import CreateDrawer from "./createDrawer";
import { useNotificationContext } from "../[components]/NotificationContext";

const StyledMenuItem = styled(MenuItem)({
  fontSize: "12px",
  fontWeight: 500,
  color: "#4B5563",
  padding: "8px",
  "&:hover": { backgroundColor: "#F8FAFC" },
});

interface SelectOption {
  id: string;
  value: string;
}

interface orgSelectOption {
  id: string;
  value: string;
  organisationId: string;
}

interface ProcessState {
  organizationID: string;
  subOrganizationId: string;
  processId: string;
}

interface QueryState {
  name: string;
  userId: string;
  organisationId: string;
  subOrganisationId: string;
}

export default function Header() {
  const dispatch = useDispatch();
  const pathname = usePathname();
  const user = useSelector((state: RootState) => state.user);
  const roleacess = useSelector((state: RootState) => state as RootState);
  const orgList = useSelector((state: RootState) => state as RootState).orgList
    .orgList;
  const { notificationTriggered } = useNotificationContext();

  const process = useSelector((state: RootState) => state.process);
  const { instance } = useMsal();
  const router = useRouter();
  const [open, setOpen] = React.useState(false);
  const menuRef = React.useRef<HTMLDivElement>(null);

  const [select1, setSelect1] = React.useState<orgSelectOption[]>([]);
  const [select2, setSelect2] = React.useState<SelectOption[]>([]);
  const [select3, setSelect3] = React.useState<SelectOption[]>([]);
  const pathName = usePathname();
  const [selectProcess, setSelectProcess] = React.useState<ProcessState>({
    organizationID: "",
    subOrganizationId: "",
    processId: "",
  });
  const [openDrawer, setopenDrawer] = React.useState(false);
  // const [isUnread,setIsUnread] = React.useState(false)
  const [unreadCount, setUnreadCount] = React.useState(0);
  const userId = useSelector((state: RootState) => state.user.id);
  React.useEffect(() => {
    console.log("orgList", orgList);

    setSelect1(orgList);
  }, [orgList]);

  React.useEffect(() => {
    if (notificationTriggered) {
      notificationCount();
      // trigger API or UI update here
    }
  }, [notificationTriggered]);

  React.useEffect(() => {
    notificationCount();
  }, []);

  React.useEffect(() => {
    if (select1.length == 0) {
      // router.push('/process')
      dispatch(setLoading(false));
    }
    const orgNotFound = select1.some(
      (item) => item.id === selectProcess.organizationID
    );
    const subOrgNotFound = select2.some(
      (item) => item.id === selectProcess.subOrganizationId
    );
    const processNotFound = select3.some(
      (item) => item.id === selectProcess.processId
    );

    const isAnyNotFound = orgNotFound && subOrgNotFound && processNotFound;

    console.log(
      "isAnyNotFound",
      isAnyNotFound,
      orgNotFound,
      subOrgNotFound,
      processNotFound
    );

    if (isAnyNotFound) {
      dispatch(setLoading(false));
    } else {
      dispatch(setLoading(true));
    }
  }, [select1, select2, select3, selectProcess]);

  const notificationCount = () => {
    getNotificationCount({ userId: userId })
      .then((res) => {
        console.log(
          "getNotificationCount",
          res?.notificationStats?.unreadCount
        );
        setUnreadCount(
          res?.notificationStats?.unreadCount
            ? res?.notificationStats?.unreadCount
            : 0
        );
        // if(res?.notificationStats?.unreadCount && res?.notificationStats?.unreadCount > 0){
        //   setIsUnread(true)
        // }else{
        //   setIsUnread(false)
        // }
      })
      .catch((err) => {
        console.error(err);
      });
  };

  const markasAllRead = () => {
    markAsAllRead()
      .then((res) => {
        console.log(res);
        notificationCount();
      })
      .catch((err) => {
        console.error(err);
      });
  };

  const [query, setQuery] = React.useState<QueryState>({
    name: "organisation",
    userId: user.id || "",
    organisationId: "",
    subOrganisationId: "",
  });

  React.useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  console.log("Header roleacess", (roleacess as any)?.roleProcess?.role?.name);

  const handleLogout = async () => {
    // 1. Remove your app's cookies
    document.cookie = "token=; path=/; max-age=0; samesite=lax";
    document.cookie = "isAssigned=; path=/; max-age=0; samesite=lax";

    // 2. Remove sessionStorage / localStorage (your app tokens/state)
    sessionStorage.removeItem("ms_access_token");
    sessionStorage.removeItem("isAssigned");

    // 3. Clear Redux state
    dispatch(clearUser());
    dispatch(setRole({}));
    dispatch(setClearProcess());

    // 4. Clear Redux Persist
    await persistor.purge();
    // 5. Optionally also logout from MSAL
    if (instance) {
      instance.logoutRedirect();
    } else {
      // Redirect to login page manually
      router.replace("/");
    }

    instance.logoutRedirect({ postLogoutRedirectUri: "/" });
  };

  useIdleTimer(handleLogout);

  const initialQuery = {
    name: "organisation",
    userId: user.id || "",
    organisationId: "",
    subOrganisationId: "",
  };

  React.useEffect(() => {
    // Always load initial organization data
    selectHeaders(initialQuery)
      .then((res) => {
        console.log("Initial organizations loaded:", res.selectHeader);
        if (res.selectHeader.length !== 0) {
          setSelect1(res.selectHeader);
          dispatch(setOrgList(res.selectHeader));
        } else {
          router.push("/process");
        }
      })
      .catch((err) =>
        console.log("error loading initial organization options:", err)
      );
  }, []);

  // === Sync from Redux store to local state ===
  React.useEffect(() => {
    if (
      process &&
      (process.organizationID || process.subOrganizationId || process.processId)
    ) {
      console.log("Initial process state from Redux:", process);

      // Set the local state from Redux
      setSelectProcess({
        organizationID: process.organizationID || "",
        subOrganizationId: process.subOrganizationId || "",
        processId: process.processId,
      });

      if (process.organizationID) {
        // Load sub-organizations
        const subOrgQuery = {
          name: "suborganisation",
          userId: user.id || "",
          organisationId: process.organizationID,
          subOrganisationId: "",
        };

        selectHeaders(subOrgQuery).then((res) => {
          console.log("Sub-organizations loaded:", res.selectHeader);
          setSelect2(res.selectHeader);

          if (process.subOrganizationId) {
            const processQuery = {
              name: "process",
              userId: user.id || "",
              organisationId: process.organizationID,
              subOrganisationId: process.subOrganizationId,
            };

            selectHeaders(processQuery).then((res) => {
              console.log("Processes loaded:", res.selectHeader);
              setSelect3(res.selectHeader);
            });
          }
        });
      }
    }
  }, [process, user.id]);

  React.useEffect(() => {
    if (query.name === "organisation" && select1.length === 0) {
      return;
    }
    if (query.name === "suborganisation" || query.name === "process") {
      selectHeaders(query)
        .then((res) => {
          if (query.name === "suborganisation") {
            console.log("Dynamic sub-organizations loaded:", res.selectHeader);
            setSelect2(res.selectHeader);
          } else if (query.name === "process") {
            console.log("Dynamic processes loaded:", res.selectHeader);
            setSelect3(res.selectHeader);
          }
        })
        .catch(() => console.log("error loading select options"));
    }
  }, [query, select1.length]);

  const handleOrganizationChange = (event: any) => {
    const val = event.target.value as string;
    dispatch(setLoading(true));
    setSelectProcess({
      organizationID: val,
      subOrganizationId: "",
      processId: "",
    });
    dispatch(
      setProcess({ organizationID: val, subOrganizationId: "", processId: "" })
    );

    const nextQuery = {
      ...query,
      organisationId: val,
      subOrganisationId: "",
      name: "suborganisation",
    };
    setQuery(nextQuery);

    selectHeaders(nextQuery).then((res) => setSelect2(res.selectHeader));
  };

  const handleSubOrganizationChange = (event: any) => {
    const val = event.target.value as string;
    dispatch(setLoading(true));
    setSelectProcess({
      ...selectProcess,
      subOrganizationId: val,
      processId: "",
    });
    dispatch(
      setProcess({
        organizationID: selectProcess.organizationID,
        subOrganizationId: val,
        processId: "",
      })
    );

    const nextQuery = {
      ...query,
      organisationId: selectProcess.organizationID,
      subOrganisationId: val,
      name: "process",
    };
    setQuery(nextQuery);

    // Load processes
    selectHeaders(nextQuery).then((res) => setSelect3(res.selectHeader));
  };

  // const handleBack = () => {
  //   if(typeof window !== 'undefined')
  //   {window.history.back();}
  // }

  // === Handle Process Change ===
  const handleProcessChange = (event: any) => {
    console.log(
      "Selected Process: header ",
      select3.filter((item) => item.id == event.target.value)
    );
    // const selected = JSON.parse(event.target.value as string);

    dispatch(
      setRole(select3.filter((item) => item.id == event.target.value)[0])
    );
    dispatch(
      setProcess({
        organizationID: selectProcess.organizationID,
        subOrganizationId: selectProcess.subOrganizationId,
        processId: event.target.value,
      })
    );
    setSelectProcess({
      ...selectProcess,
      processId: event.target.value,
    });
    dispatch(setLoading(false));
    router.push("/tickets");
  };
  console.log(selectProcess.processId);

  return (
    <div className="rounded-none">
      <div className="flex flex-wrap gap-5 justify-between p-2.5 border border-slate-50">
        <Image alt="logo" src={logo} className="object-contain w-[141px]" />

        <div className="flex gap-2 items-center">
          {!pathName.includes("access-denied") && (
            <>
              {/* <Image alt="search" src={searchIcon} className="w-[30px]" />
          <Image alt="message" src={messageIcon} className="w-[30px]" /> */}
              {/* {isUnread && (
            <Image
            alt="notification"
            src={notificationIcon}
            className="w-[35px] cursor-pointer"
            onClick={()=>{setopenDrawer(true)
              markasAllRead()
            }}
          />
          )}
          {!isUnread && (
             <Image
             alt="notification"
             src={unreadIcon}
             className="w-[35px] cursor-pointer"
             onClick={()=>{setopenDrawer(true)
              markasAllRead()
             }}
           />
          )} */}

              <Badge
                badgeContent={unreadCount}
                color="error"
                overlap="circular"
                anchorOrigin={{
                  vertical: "top",
                  horizontal: "right",
                }}
              >
                <IconButton
                  onClick={() => {
                    setopenDrawer(true);
                    markasAllRead();
                  }}
                  className={`${pathname.includes("notifications") ? "!bg-[#1465ab]" : "!bg-teal-500"}`}
                  sx={{
                    width: 35,
                    height: 35,
                  }}
                >
                  <NotificationsIcon fontSize="small" className="!text-white" />
                </IconButton>
              </Badge>

              {/* Organisation Select */}
              <FormControl variant="standard" sx={{ minWidth: 120 }}>
                <Select
                  labelId="demo-simple-select-standard-label"
                  id="demo-simple-select-standard"
                  value={selectProcess.organizationID}
                  onChange={handleOrganizationChange}
                  IconComponent={ArrowDropDownIcon}
                  disableUnderline
                  sx={{
                    fontSize: "12px",
                    color: "#4b4b4b",
                    minWidth: 120,
                    maxWidth: 180,
                    "& .MuiSelect-select": {
                      fontSize: "12px",
                      color: "#4b4b4b",
                      padding: "6px 8px",
                      paddingRight: "24px",
                      width: "100%",
                      maxWidth: "160px",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                      display: "block",
                      // opacity:1
                    },
                    "& .MuiSvgIcon-root": {
                      right: "0px !important",
                    },
                  }}
                >
                  {select1.map((item, i) => (
                    <StyledMenuItem
                      onClick={() => {
                        Cookies.set("orgId", item.organisationId, {
                          expires: 1,
                        });
                      }}
                      value={item.id}
                      key={i}
                    >
                      {item.value}
                    </StyledMenuItem>
                  ))}
                </Select>
              </FormControl>

              {/* Suborganisation Select */}
              <FormControl variant="standard" sx={{ minWidth: 120 }}>
                <Select
                  labelId="demo-simple-select-standard-label"
                  id="demo-simple-select-standard"
                  value={selectProcess.subOrganizationId}
                  disabled={!selectProcess.organizationID}
                  onChange={handleSubOrganizationChange}
                  IconComponent={ArrowDropDownIcon}
                  disableUnderline
                  sx={{
                    fontSize: "12px",
                    color: "#4b4b4b",
                    minWidth: 120,
                    maxWidth: 180,
                    "& .MuiSelect-select": {
                      fontSize: "12px",
                      color: "#4b4b4b",
                      padding: "6px 8px",
                      paddingRight: "24px",
                      idth: "100%",
                      maxWidth: "160px",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                      display: "block",
                    },
                    "& .MuiSvgIcon-root": {
                      right: "0px !important",
                    },
                  }}
                >
                  {select2.map((item, i) => (
                    <StyledMenuItem
                      onClick={() => {
                        Cookies.set("subOrgId", item.id, { expires: 1 });
                      }}
                      value={item.id}
                      key={i}
                    >
                      {item.value}
                    </StyledMenuItem>
                  ))}
                </Select>
              </FormControl>

              {/* Process Select */}
              <FormControl variant="standard" sx={{ minWidth: 120 }}>
                <Select
                  labelId="demo-simple-select-standard-label"
                  id="demo-simple-select-standard"
                  value={selectProcess.processId}
                  disabled={!selectProcess.subOrganizationId}
                  onChange={handleProcessChange}
                  IconComponent={ArrowDropDownIcon}
                  disableUnderline
                  sx={{
                    fontSize: "12px",
                    color: "#4b4b4b",
                    minWidth: 120,
                    maxWidth: 180,
                    "& .MuiSelect-select": {
                      fontSize: "12px",
                      color: "#4b4b4b",
                      padding: "6px 8px",
                      paddingRight: "24px",
                      width: "100%",
                      maxWidth: "160px",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                      display: "block",
                    },
                    "& .MuiSvgIcon-root": {
                      right: "0px !important",
                    },
                  }}
                >
                  {select3.map((item, i) => (
                    <StyledMenuItem
                      onClick={() => {
                        Cookies.set("processId", item.id, { expires: 1 });
                      }}
                      value={item.id}
                      key={i}
                    >
                      {item.value}
                    </StyledMenuItem>
                  ))}
                </Select>
              </FormControl>
            </>
          )}
          <Image alt="user" src={userLogo} className="w-[54px]" />
          <div className="flex flex-col whitespace-nowrap">
            <div className="text-base font-medium text-blue-900">
              {user.name}
            </div>
            <div className="text-xs text-slate-400">
              {(roleacess as any)?.roleProcess?.role?.name}
            </div>
          </div>

          <div className="relative inline-block text-left" ref={menuRef}>
            <span
              className="cursor-pointer px-2 py-1 hover:bg-gray-100 rounded"
              onClick={() => setOpen((prev) => !prev)}
            >
              ⋮
            </span>
            {open && (
              <div className="absolute right-0 mt-2 w-40 bg-white border rounded-md shadow-lg z-50 border border-gray-300">
                <button
                  onClick={handleLogout}
                  className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100"
                >
                  Logout
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
      <CreateDrawer
        open={openDrawer}
        onClose={() => {
          setopenDrawer(false);
        }}
      />
    </div>
  );
}
