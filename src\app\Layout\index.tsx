import { Box } from '@mui/material';
import React, { useEffect, useState } from 'react'
import Header from './header';
import Footer from './footer';
import NavBar from './navBar';
import { usePathname } from 'next/navigation';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import NotificationSetup from '../[components]/NotificationSetup';
import { NotificationContext } from '../[components]/NotificationContext';

const Layout = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const pathName=usePathname()
  const loader:boolean =  useSelector((state: RootState) => state.loader);
  console.log('loader',loader);
  const [notificationTriggered, setNotificationTriggered] = useState(false);

  const triggerPushAction = () => {
    console.log("🔁 Push notification boolean triggered");
    setNotificationTriggered(true); // your logic here
  };

  useEffect(() => {
    if (notificationTriggered) {
      const timer = setTimeout(() => {
        setNotificationTriggered(false);
        console.log("🔕 notificationTriggered reset to false");
      }, 5000); // change to 10000 for 10 seconds
  
      return () => clearTimeout(timer); // cleanup on unmount or re-trigger
    }
  }, [notificationTriggered]);
 
  return (
    <>
     <NotificationContext.Provider value={{ triggerPushAction, notificationTriggered }}>
    <NotificationSetup />
    <Box
      sx={{ display: "flex", flexDirection: "column", minHeight: "100vh" }}
    >
      <Header />
     { !pathName.includes('access-denied') && 
      <NavBar />}
      <Box sx={{ display: "flex", flexGrow: 1, overflow: "hidden" }} className='border-y border-gray-200'>
        <Box component="nav" sx={{ height: "calc(100vh - 191px)" }}>
          {/* <Sidebar setLoading={setLoading} open={open} setOpen={setOpen}/> */}
        </Box>

        <Box
        className='overflow-auto'
          component="main"
          sx={{
            flexGrow: 1,
            display: "flex",
            flexDirection: "column",
          }}
        >
           {loader &&  <Box
              sx={{
                position: "absolute",
                top: 90,
                left: 0,
                width: "100%",
                height: "90vh",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                bgcolor: "rgba(255, 255, 255, 0.6)",
                zIndex: 21, // ensure it's above modals
              }}
            ></Box>}
          {children}

        </Box>


      
      </Box>
      <Footer />
    </Box>
    </NotificationContext.Provider>
    </>
    
  );
}

export default Layout
