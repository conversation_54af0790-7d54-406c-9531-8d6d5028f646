/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import React, { useEffect, useState } from "react";
import { NavItemProps } from "@/types/user";

import Image from "next/image";
import { useRouter,usePathname } from "next/navigation";

import { navItems } from "@/utils/data";
import Loader from "@/components/loader/loader";
import ImportExportIcon from '@mui/icons-material/ImportExport';
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { Tooltip } from "@mui/material";

const NavBar: React.FC = () => {
  const [loader,setLoader] = React.useState(false)
const router = useRouter();
const pathname = usePathname();
console.log('router,',router);
 
  const [activeItem, setActiveItem] = useState<string>('');
  const handleItemClick = async(itemId: string) => {
    setLoader(true)
    setActiveItem(itemId);
    await router.push(`/${itemId}`);
      setLoader(false)
  };
  useEffect(()=>{
     console.log('get',pathname.split("/")[1]);
  setActiveItem(pathname.split("/")[1])
  },[pathname])
  

  return (
    <div className="flex items-center w-full bg-teal-500 h-[49px] max-md:overflow-x-auto">
      <div className="flex items-center w-full bg-teal-500 h-[49px] max-md:overflow-x-auto">
      {navItems.map((item) => (
        <NavItem
          key={item.id}
          id={item.id}
          icon={item.icon}
          label={item.label}
          isActive={activeItem === item.id}
          onClick={() => handleItemClick(item.id)}
        />
      ))}
      </div>
     <NavItem
      id={'exports'}
          icon={<ImportExportIcon/>}
          isActive={activeItem === 'exports'}
          onClick={() => handleItemClick('exports')}
        />
       {loader && <Loader />}
    </div>
  );
};

export default NavBar;
const NavItem: React.FC<NavItemProps> = ({ id,icon, label, isActive, onClick }) => {
 const headerProcess:any =  useSelector((state: RootState) => state.headerProcess);
console.log('headerProcess',headerProcess);

  const params=usePathname();
  return (
    <>
    {headerProcess?.role?.permissions?.map((item:{moduleName:string, isEnabled:boolean},i:number)=>{
      console.log('headerProcess',item.moduleName,id);
      
      if(label!==undefined && item.moduleName==label && item.isEnabled){
        return(
           <Tooltip title={label}  placement="bottom"  key={i}>
<div
      className={`flex gap-2 justify-center items-center px-5 py-0 h-full text-base text-white cursor-pointer ${label==undefined? 'border-l-white border-l-opacity-50 border-l border-solid':'border-r-white border-r-opacity-50 border-r border-solid'} max-md:px-4 max-md:py-0 max-md:min-w-max max-sm:px-3 max-sm:py-0 max-sm:w-[49px] "bg-teal-600" ${isActive && "bg-[#1465AB]" }`}
      onClick={()=>{if(!params.includes("access-denied")){ onClick()}}}
      
      aria-current={isActive ? "page" : undefined}
    >
      <div>
         {label==undefined?<ImportExportIcon/>:<Image alt='settings' src={icon} />}
      </div>
      <div className="max-sm:hidden">{label}</div>
    </div></Tooltip>
     ) }

    }
    )} 
    {label==undefined&& 
     <Tooltip title={'import & Export'}  placement="bottom-start">
     <div
      className={`flex gap-2 justify-center items-center px-5 py-0 h-full text-base text-white cursor-pointer ${label==undefined? 'border-l-white border-l-opacity-50 border-l border-solid':'border-r-white border-r-opacity-50 border-r border-solid'} max-md:px-4 max-md:py-0 max-md:min-w-max max-sm:px-3 max-sm:py-0 max-sm:w-[49px] "bg-teal-600" ${isActive && "bg-[#1465AB]" }`}
      onClick={()=>{if(!params.includes("access-denied")){ onClick()}}}
      
      aria-current={isActive ? "page" : undefined}
    >
      <div>
         {label==undefined?<ImportExportIcon/>:<Image alt='settings' src={icon} />}
      </div>
      <div className="max-sm:hidden">{label}</div>
    </div>
    </Tooltip>}
    </>
  )
};

