// src/app/providers.tsx
'use client';

import { ReactNode } from 'react';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { store, persistor } from '../store';
import { ApolloProvider } from '@apollo/client';
import client from '@/lib/apollo-client';
import ThemeRegistry from '@/theme/ThemeRegistry';
import { ToastProvider } from '@/components/toaster/ToastProvider';
import MsalProviderWrapper from '@/components/MsalProviderWrapper';

export default function Providers({ children }: { children: ReactNode }) {
  return (
    <Provider store={store}>
       <MsalProviderWrapper>
      <PersistGate loading={null} persistor={persistor}>
        <ToastProvider />
         <ApolloProvider client={client}>
                    <ThemeRegistry>{children}</ThemeRegistry>
                </ApolloProvider>
      </PersistGate>
      </MsalProviderWrapper>
    </Provider>
  );
}
