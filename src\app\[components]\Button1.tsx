import type React from "react";
import { ReactNode } from "react";
interface CustomButtonProps {
  text: string;
  onClick?: () => void;
  isLoading?: boolean;
  disabled?: boolean;
  size?: string; 
  className?: string;
  variant?: string;
children?: ReactNode;
}


const CustomButton: React.FC<CustomButtonProps> = ({
  onClick,
  className,
  children,
  isLoading = false,
  disabled = false,
}) => {
  return (
    <button
      type="button"
      onClick={onClick}
      disabled={isLoading || disabled}
      className={className}
    >
      {children}
    </button>
  );
};

export default CustomButton;
