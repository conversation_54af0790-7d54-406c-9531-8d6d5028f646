/* eslint-disable prefer-const */
/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import "./style.css";
import { getGlobalOptions } from "@/api/Globals/globals";
import { toCamelCase } from "@/components/gridTable/tableUtils";
import {
  Box,
  Checkbox,
  FormControlLabel,
  FormGroup,
  // InputAdornment,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TextField,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { getImagedUrl } from "@/api/file/file";
import { LocalizationProvider, TimePicker } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
import AttachFileIcon from "@mui/icons-material/AttachFile";

interface Field {
  label: string;
  field_type: string;
  placeholder: any;
  name: string;
  globals_name: string;
  required?: boolean;
  options: any[];
  id: React.Key | null | undefined;
  columns: any[];
  rows: any[];
}

interface RenderFieldProps {
  field: Field;
  sectionName: string;
  formData: any;
  handleFieldChange: any;
  type: string;
  handleGridEdit: any;
  handleGridDelete: any;
  editIcon: any;
  deleteIcon: any;
  access:boolean
}
const RenderFields: React.FC<RenderFieldProps> = ({
  field,
  sectionName,
  formData,
  handleFieldChange,
  type,
  handleGridEdit,
  handleGridDelete,
  editIcon,
  deleteIcon,
  access
}) => {
  const [globalOptions, setGlobalOptions] = useState<
    { id: string; value: string }[]
  >([]);
  const [isGlobalLoading, setIsGlobalLoading] = useState(false);
  const section = toCamelCase(sectionName);
  const fieldName = toCamelCase(field.label);
  console.log(
    "formData?.[section as keyof typeof formData]?.[fieldName]",
    formData?.[section as keyof typeof formData]?.[fieldName],
    fieldName
  );

  const value: any = field.field_type=='time'
    ? dayjs(
        formData?.[section as keyof typeof formData]?.[fieldName]
      ).local() 
    : (formData?.[section as keyof typeof formData]?.[fieldName] ?? "");
  console.log("value", value);
  const tableValue = formData?.[section as keyof typeof formData];
  console.log("formData", formData);
  console.log("field.field_type", field.field_type);

  const fetchImageUrl = async (filename: string) => {
    try {
      const res = await getImagedUrl({ filename });
      console.log(res.generateViewUrl.data.viewUrl);

      // Optional fetch check if you want:
      const response = await fetch(res.generateViewUrl.data.viewUrl, {
        method: "GET",
        headers: {
          "Content-Type": "image/png",
        },
      });
      console.log(response);
      window.open(res.generateViewUrl.data.viewUrl, "_blank");
      // setViewUrl(res.generateViewUrl.data.viewUrl);
    } catch (err) {
      console.error(err);
      // setError('Failed to load image');
    }
  };

  //    const fetchImageUrl = (filename) => {
  //          getImagedUrl({ filename }).then((res)=>{
  //  console.log('generateViewUrl',res.generateViewUrl.data.viewUrl);

  //           // Optional fetch check if you want:
  //           const response =  fetch(res.generateViewUrl.data.viewUrl, {
  //             method: "GET",
  //             headers: {
  //               "Content-Type": "image/png",
  //             },
  //           });
  //           console.log('response',response);
  //            window.open(res.generateViewUrl.data.viewUrl, '_blank');
  //          })

  //       };

  useEffect(() => {
    console.log(
      'field.field_type === "global_select" && field.label',
      field.field_type === "global_select" && field.label
    );

    if (field.field_type === "global_select" && field.label) {
      const fetchGlobalOptions = async () => {
        if (!field.label) return;
        setIsGlobalLoading(true);
        // console.log(' Object.entries(formData?.[section as keyof typeof formData])', Object.entries(formData!==null&&formData?.[section as keyof typeof formData]));

        try {
          let payload: any = {
            name: field.globals_name ?? "",
          };
          if (field.globals_name !== "City") {
            // const cleanedLabel = field.globals_name;
            const res = await getGlobalOptions(payload);
            console.log("Fetched global options:", res);

            // const data = await res.json();
            const data = res?.getGlobalByName;
            if (Array.isArray(data)) {
              setGlobalOptions(data);
            } else {
              console.error("Unexpected global field data", data);
              setGlobalOptions([]);
            }
          }
        } catch (err) {
          console.error("Error fetching global options:", err);
          setGlobalOptions([]);
        } finally {
          setIsGlobalLoading(false);
        }
      };
      if (field.globals_name !== undefined) {
        fetchGlobalOptions();
      }
    }
  }, [field.label, field.field_type]);
  useEffect(() => {
    const fetchGlobalOptions = async () => {
      let payload: any = {
        name: field.globals_name ?? "",
      };
      if (field.globals_name == "City") {
        const targetData = formData?.[section as keyof typeof formData];
        if (
          targetData !== undefined &&
          targetData &&
          typeof targetData === "object"
        ) {
          const result = Object.entries(targetData).find(
            ([key]) => key.includes("state") || key.includes("State")
          );
          const stateId = result ? result[1] : undefined;
          payload["stateId"] = stateId;
        }
      }
      // const cleanedLabel = field.globals_name;
      const res = await getGlobalOptions(payload);
      console.log("Fetched global options:", res);

      // const data = await res.json();
      const data = res?.getGlobalByName;
      if (Array.isArray(data)) {
        setGlobalOptions(data);
      } else {
        console.error("Unexpected global field data", data);
        setGlobalOptions([]);
      }
    };
    fetchGlobalOptions();
  }, [formData]);

  switch (field.field_type) {
    case "text":
    case "email":
    case "phone":
    case "number":
    case "date":

    case "password":
      return (
        <TextField
          fullWidth
          size="small"
          
          type={
            field.field_type === "phone"
              ? "tel"
              : field.field_type == "number"
                ? "number"
                : field.field_type === "date"
                  ? "date"
                  : field.field_type === "password"
                    ? "password"
                    : "text"
          }
          // inputProps={
          //   field.field_type === "number"
          //     ? { inputMode: "numeric", pattern: "[0-9]*" }
          //     : {}
          // }
          InputProps={{
            sx: {
              "& input.Mui-disabled": {
                color: "#000000",
                WebkitTextFillColor: "#000000", //for Safari/Chrome
              },
            },
          }}
          placeholder={field.placeholder || field.label}
          value={value}
          className={`${type === "edit" && field.label.includes("Email") ? "bg-gray-100" : "bg-transparent"}`}
          disabled={!access ?true: type === "edit" && field.label.includes("Email")}
          onChange={(e) => {
            const target = e.target as HTMLInputElement;
            handleFieldChange(sectionName, field.label, target.value);
          }}
          required={field.required}
        />
      );
    
    case "multiselect":
      return (
        <TextField
          select
          size="small"
          fullWidth
          disabled={!access}
          value={value || []} // should be an array
          onChange={(e) =>
            handleFieldChange(sectionName, field.label, e.target.value)
          }
          required={field.required}
          SelectProps={{
            multiple: true,
            MenuProps: {
              PaperProps: {
                sx: {
                  maxHeight: "12rem",
                  overflowY: "auto",
                },
              },
            },
          }}
          InputProps={{
            sx: {
      // Set base color
      color: value === "" ? "#aca1a49e" : "#000000",

      // Fix for disabled state (text and placeholder)
      "&.Mui-disabled": {
        color: "#000000",
        WebkitTextFillColor: "#000000", // for Safari
      },
      "& .MuiSelect-select.Mui-disabled": {
        color: "#000000",
        WebkitTextFillColor: "#000000",
      },
    },
          }}
        >
          {field.options?.map((opt) => (
            <MenuItem key={opt.id} value={opt.value}>
              {opt.value}
            </MenuItem>
          ))}
        </TextField>
      );
    case "image":
    case "file_upload":
      return (
        <div className="flex w-full flex-col sm:flex-row">
          {/* File upload button with icon */}
          <label
            htmlFor={`file-upload-${field.label}`}
            className="flex items-center justify-center border border-gray-300 rounded-l px-3 bg-white cursor-pointer min-w-[44px] h-[40px]"
            style={{ borderRight: "none" }}
          >
            <AttachFileIcon className="text-gray-500" />
            <input
              id={`file-upload-${field.label}`}
              type="file"
              style={{ display: "none" }}
              aria-label="upload file"
              disabled={!access}
              onChange={(e) => {
                const target = e.target as HTMLInputElement;
                handleFieldChange(sectionName, field.label, target.files?.[0]);
              }}
              required={field.required}
            />
          </label>
          {/* File name display */}
          <div className="flex items-center border border-gray-300 rounded-r px-3 bg-white w-full min-h-[40px] overflow-x-auto">
            {value ? (
              <a
                className="truncate text-blue-500 underline cursor-pointer w-full"
                onClick={() => fetchImageUrl(value)}
                title={typeof value === "string" ? value : value.name}
              >
                {typeof value === "string" ? value : value.name}
              </a>
            ) : (
              <span className="text-gray-400 text-sm">No file selected</span>
            )}
          </div>
        </div>
      );
    case "select":
      return (
        <TextField
          select
          fullWidth
          size="small"
          disabled={!access}
          className="!text-[#000000]"
          value={value}
          onChange={(e) =>
            handleFieldChange(sectionName, field.label, e.target.value)
          }
          required={field.required}
          SelectProps={{
            displayEmpty: true,
            multiple: false,
            MenuProps: {
              PaperProps: {
                sx: {
                  maxHeight: "12rem",
                  overflowY: "auto",
                },
              },
            },
          }}
          InputProps={{
 sx: {
      // Set base color
      // color: value === "" ? "#aca1a49e" : "#000000",

      // Fix for disabled state (text and placeholder)
      // "&.Mui-disabled": {
      //   color: "#000000",
      //   WebkitTextFillColor: "#000000", // for Safari
      // },
      "& .MuiSelect-select.Mui-disabled": {
        color: "#000000",
        WebkitTextFillColor: "#000000",
      },
    },
          }}
        >
          <MenuItem value="" disabled>
            {field.placeholder}
          </MenuItem>
          {field.options?.map((opt) => (
            <MenuItem key={opt.id} value={opt.value}>
              {opt.value}
            </MenuItem>
          ))}
        </TextField>
      );
    case "global_select":
      return (
        <Box sx={{ position: "relative" }}>
          <TextField
            select
            fullWidth
            size="small"
            name={field.name}
            disabled={!access}
            InputProps={{
                  sx: {
                    
                    "& input.MuiSelect-select":{
                      color: "#000000",
                      WebkitTextFillColor: "#000000 !important",
                    }
                  },
                }}
            required={field.required}
            variant="outlined"
            value={value ?? ""}
            onChange={(e) =>
              handleFieldChange(sectionName, field.label, e.target.value)
            }
            // InputProps={{
            //   endAdornment: (
            //     <FieldActionsMenn onEdit={onEdit} onDelete={field.onDelete} />
            //   ),
            // }}
            SelectProps={{
              displayEmpty: true,
              MenuProps: {
                PaperProps: {
                  sx: {
                    maxHeight: "12rem",
                    overflowY: "auto",
                  },
                },
              },
            }}
          >
            <MenuItem disabled value="">
              {field.placeholder || `Select ${field.label}`}
            </MenuItem>

            {isGlobalLoading ? (
              <MenuItem disabled value="">
                Loading options...
              </MenuItem>
            ) : globalOptions.length === 0 ? (
              <MenuItem disabled value="">
                No options found
              </MenuItem>
            ) : (
              globalOptions.map((opt) => (
                <MenuItem key={opt.id} value={opt.value}>
                  {opt.value}
                </MenuItem>
              ))
            )}
          </TextField>
        </Box>
      );
      case "time":
      return (
        <Box sx={{ position: "relative" }}>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <TimePicker
                value={value} // should be an array
                disabled={!access}
                onChange={(e) => handleFieldChange(sectionName, field.label, e)}
                className="text-cyan-600"
                slotProps={{
                  textField: {
                    fullWidth: true,
                    required: field.required,
                    name: field.name,
                    variant: "outlined",
                    // size: "small",
                    sx: {
                      borderRadius: "6px",
                      backgroundColor: "#fff",
                      fontSize: "14px",
                      //  color: "#000000",

  // Fix for disabled state
  "& .MuiPickersInputBase-root": {
    color: "#000000",
    WebkitTextFillColor: "#000000", // Safari
  },
  "& .MuiInputBase-input.Mui-disabled": {
    color: "#000000",
    WebkitTextFillColor: "#000000",
  },

  "& .MuiOutlinedInput-root": {
    paddingRight: "10px",
    color: "#000000",
  },

                      "&:hover .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#3182ce",color:'#000000'
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#3182ce",color:'#000000'
                      },
                    },
                  },
                  popper: {
                    sx: {
                      zIndex: 1400,
                      "& .MuiPaper-root": {
                        borderRadius: "8px",
                        boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
                        backgroundColor: "#fff",
                        paddingBottom: "0px",
                      },
                    },
                  },
                  layout: {
                    sx: {
                      padding: "0px !important",
                      "& .MuiPickersLayout-actionBar": {
                        justifyContent: "space-around",
                        backgroundColor: "#f9fafb",
                        padding: "8px",
                      },
                      "& .MuiMultiSectionDigitalClockSection-item.Mui-selected":
                        {
                          backgroundColor: "#27B8AF",
                        },
                    },
                  },
                  actionBar: {
                    actions: ["cancel", "accept"],
                    sx: {
                      "& button:first-of-type": {
                        color: "#fff",
                        fontWeight: 500,
                        borderRadius: "6px",
                        // height: "31px",
                        boxShadow: "none",
                        fontSize: "15px",
                        marginTop: "5px",
                        marginRight: "8px",
                      },
                      "& button:last-of-type": {
                        backgroundColor: "#27B8AF",
                        color: "#fff",
                        fontWeight: 500,
                        borderRadius: "6px",
                        boxShadow: "none",
                        // height: "31px",
                        fontSize: "15px",
                        marginTop: "5px",
                        "&:hover": {
                          backgroundColor: "#27B8AF",
                        },
                      },
                    },
                  },
                }}
              />
            </LocalizationProvider>
          </div>
          <Box
            sx={{
              position: "absolute",
              top: 35,
              right: 0,
              zIndex: 1,
            }}
          ></Box>
        </Box>
      );

    case "textarea":
      return (
        <TextField
          fullWidth
          multiline
          minRows={4}
          disabled={!access}
          placeholder={field.placeholder || field.label}
          value={value}
          onChange={(e) =>
            handleFieldChange(sectionName, field.label, e.target.value)
          }
          required={field.required}
          InputProps={{
            sx: {
              "& input.Mui-disabled": {
                color: "#000000",
                WebkitTextFillColor: "#000000", //for Safari/Chrome
              },
            },
          }}
        />
      );
    case "checkboxes":
      return (
        // <div className="flex gap-2 items-start">
        <FormGroup>
          {field.options.map((option) => (
            <FormControlLabel
              key={option.id}
              disabled={!access}
              control={
                <Checkbox
                  checked={value?.includes(option.id)}
                  onChange={(e) => {
                    const newValue = e.target.checked
                      ? [...(value || []), option.id]
                      : (value || []).filter((v: string) => v !== option.id);
                    handleFieldChange(sectionName, field.label, newValue);
                  }}
                />
              }
              label={option.value}
            />
          ))}
        </FormGroup>
      );
    case "grid":
      const gridRows = Array.isArray(tableValue) ? tableValue : []; // value from formData
      console.log("gridRows", gridRows);
      return (
        <div
          key={field.id}
          className="px-4 pb-4 overflow-x-scroll
   "
        >
          <Table sx={{ border: "1px solid #E2E8F0" }} size='small'>
            <TableHead sx={{ backgroundColor: "#f0fdff" }}>
              <TableRow>
                {field.columns.map((col) => (
                  <TableCell
                    key={col.id}
                    sx={{ color: "#1E40AF", fontWeight: 600 }}
                  >
                    {col.name}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {gridRows.length > 0 ? (
                gridRows.map((row, rowIndex) => (
                  <TableRow key={rowIndex}>
                    {field.columns.map((col) =>
                      col.name !== "Actions" && col.name !== "Action" ? (
                        <TableCell key={col.id}>
                          {row?.[toCamelCase(col.name)] === "true"
                            ? "Yes"
                            : row?.[toCamelCase(col.name)] === "false"
                              ? "No"
                              : (row?.[toCamelCase(col.name)] ??
                                row?.[col.id] ??
                                "")}
                        </TableCell>
                      ) : (
                        <TableCell key={col.id}>
                          <div className="flex gap-2.5 items-center transition-opacity">
                            <button
                              type="button"
                              onClick={() =>
                                handleGridEdit(
                                  sectionName,
                                  field.label,
                                  rowIndex,
                                  field.columns,
                                  row
                                )
                              }
                              className="p-2 rounded hover:bg-blue-50 transition-colors"
                            >
                              {editIcon}
                            </button>
                            <button
                              type="button"
                              onClick={() =>
                                handleGridDelete(sectionName, rowIndex)
                              }
                              className="p-2 rounded hover:bg-red-50 transition-colors"
                            >
                              {deleteIcon}
                            </button>
                          </div>
                        </TableCell>
                      )
                    )}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={field.columns.length}
                    align="center"
                    sx={{ color: "#64748B", fontSize: "14px" }}
                  >
                    No Data Found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      );
    default:
      return <div>Unsupported field type</div>;
  }
};

export const RenderField = React.memo(RenderFields);
