"use client";

import { useEffect } from "react";
import { getToken, onMessage } from "firebase/messaging";
import { addPushNotificationToken } from "@/api/tickets/source";
import { useNotificationContext } from "./NotificationContext";

const NotificationSetup = () => {
  const { triggerPushAction } = useNotificationContext();
  
  useEffect(() => {
    // Ensure runs only in browser
    if (
      typeof window !== "undefined" &&
      "serviceWorker" in navigator &&
      "PushManager" in window &&
      "Notification" in window
    ) {
      const init = async () => {
        try {
          const { messaging } = await import("../../lib/firebast"); // dynamic import inside useEffect

          const registration = await navigator.serviceWorker.register(
            "/firebase-messaging-sw.js"
          );
          console.log("✅ Service Worker Registered", registration);

          const token = await getToken(messaging, {
            vapidKey:
              "BEv2hJ-2qYcuO3dxt8aa2N-2y13SB6UtF5BMDpcQAmjfbzJ-6iwE06cHz38k3jbBbiS2rzhNMwKcELSYY8wk0B4",
            serviceWorkerRegistration: registration,
          });

          if (token) {
            console.log("✅ FCM Token:", token);
            await addPushNotificationToken({ token: [token] });
          }

          onMessage(messaging, (payload) => {
            console.log("📩 Foreground message received:", payload);
            triggerPushAction?.();
            // Show toast or update UI
          });

          navigator.serviceWorker.addEventListener("message", (event) => {
            if (event.data?.type === "NOTIFICATION_RECEIVED") {
              console.log("📨 Background SW Message:", event.data);
              triggerPushAction?.();
            }
          });
        } catch (err) {
          console.error("❌ Push Notification Setup Error:", err);
        }
      };

      init();
    }
  }, []);

  return null;
};

export default NotificationSetup;
