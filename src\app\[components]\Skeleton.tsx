import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Paper,
  Skeleton,
} from "@mui/material";

const columnCount = 7; // Checkbox + 6 data columns

const TableSkeleton = ({ rows = 5 }) => {
  return (
    <TableContainer component={Paper} className="p-3">
      <Table>
        <TableBody>
          {[...Array(rows)].map((_, rowIndex) => (
            <TableRow key={rowIndex}>
              <TableCell padding="checkbox">
                <Skeleton variant="rectangular" width={20} height={20} />
              </TableCell>
              {[...Array(columnCount - 2)].map((_, cellIndex) => (
                <TableCell key={cellIndex}>
                  <Skeleton variant="text" width="90%" height={20} />
                </TableCell>
              ))}
              {/* Action column skeleton (icon/button style) */}
              <TableCell>
                <Skeleton  variant="text" width="90%" height={20} />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default TableSkeleton;