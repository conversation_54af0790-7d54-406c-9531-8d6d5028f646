// components/ui/Button.tsx
type ButtonProps = {
  label: string;
  onClick?: () => void;
  type?: "button" | "submit";
};

const ButtonComponent: React.FC<ButtonProps> = ({ label, onClick, type = "button" }) => (
  <button
    type={type}
    onClick={onClick}
    className="bg-teal-500 text-white px-6 py-2 rounded hover:bg-teal-600 transition"
  >
    {label}
  </button>
);

export default ButtonComponent;
