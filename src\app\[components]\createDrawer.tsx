/* eslint-disable prefer-const */
/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';
import { Drawer, Typography, IconButton} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useEffect, useState } from "react";
import { toCamelCase } from "@/utils/generic";
import { RenderField } from "./drawerFields";

const CreateDrawer = ({
  title,
  open,
  onClose,
  columns,
  onSave,
  editRow = {},
}: {
  title: string;
  open: boolean;
  onClose: () => void;
  columns: any[];
  onSave: (rowData: Record<string, string>) => void;
  editRow?: Record<string, string>;
}) => {
  const [rowData, setRowData] = useState<Record<string, string>>({});
const [globalOptions, setGlobalOptions] = useState<
    { id: string; value: string }[]
  >([]);
  const [isGlobalLoading, setIsGlobalLoading] = useState(false);
  const handleChange = (colId: string, value: string) => {
    setRowData((prev) => ({ ...prev, [toCamelCase(colId)]: value }));
  };

  const handleFileChange = (colId: string, event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setRowData((prev) => ({ ...prev, [toCamelCase(colId)]: file.name }));
    }
  };
  
  useEffect(() => {
    if (open && editRow && Object.keys(editRow).length > 0) {
      setRowData(editRow);
    } else if (open && (!editRow || Object.keys(editRow).length === 0)) {
      setRowData({}); // Reset when creating a new row
    }
  }, [open, editRow]);

  const handleSave = () => {
    const newRow = {
      // id: `row_${Date.now()}`,
      ...rowData,
    };
    console.log('newRow', newRow);

    onSave(newRow);
    // if (closeDrawer) {
    setRowData({});
    onClose();
    // }
  };



  return (
    <Drawer anchor="right" open={open} onClose={onClose}>
      <div className="w-[400px] p-6 flex flex-col h-full">
        <div className="flex justify-between items-center mb-4">
          <section className="w-full">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2 bg-blue-50 px-2 py-1 pt-[10px]">
              <Typography
                variant="h6"
                className="font-semibold text-gray-900"
              >
                {title}
              </Typography>
              <IconButton onClick={onClose}>
                <CloseIcon />
              </IconButton>
            </div>
          </section>
        </div>

        <div className="flex-1 overflow-auto">
          {columns?.map((col) => (
            (col.name !== 'Actions' && col.name !== 'Action') && 
            <RenderField
  key={col.id}
  col={col}
  value={rowData[toCamelCase(col.name)] || ""}
  rowData={rowData}
  handleFileChange={handleFileChange}
  handleChange={handleChange}
  isGlobalLoading={isGlobalLoading}
  setIsGlobalLoading={setIsGlobalLoading}
  globalOptions={globalOptions}
  setGlobalOptions={setGlobalOptions}
/>
          ))}
        </div>
        
        <div className="flex justify-end">
          <button
            className="bg-[#1969AE] hover:bg-[#155b96] text-white font-bold py-2 px-6 rounded w-[25%]"
            onClick={handleSave}
          >
            Save
          </button>
        </div>
      </div>
    </Drawer>
  );
};


export default CreateDrawer;
