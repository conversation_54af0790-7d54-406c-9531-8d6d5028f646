// components/ui/FormDateInput.tsx
type FormDateInputProps = {
  label: string;
  name: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
};

const FormDateInput: React.FC<FormDateInputProps> = ({ label, name, value, onChange }) => (
  <div className="flex flex-col gap-1">
    <label className="text-sm font-medium text-gray-700">{label}</label>
    <input
      type="date"
      name={name}
      value={value}
      onChange={onChange}
      className="border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-teal-500"
    />
  </div>
);

export default FormDateInput;
