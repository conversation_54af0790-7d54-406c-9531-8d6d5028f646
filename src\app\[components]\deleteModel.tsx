import { Dialog, DialogContent, IconButton } from '@mui/material';
import React from 'react';
import CloseIcon from "@mui/icons-material/Close";

export default function DeleteModals({ isOpen, onClose, onDelete,title, message,action }: {
  isOpen: boolean;
  onClose: () => void;
  onDelete: () => void;
  title:string;
   message :string;
   action:string
}) {
  if (!isOpen) return null;

  return (
      <Dialog open={isOpen} onClose={onClose} fullWidth maxWidth="xs">
          <DialogContent sx={{ position: "relative" }}>
        <div className="fixed inset-0 flex justify-center items-center p-5 bg-opacity-50 z-50">
          <div className="relative bg-white rounded-3xl shadow-2xl w-[545px]">
            <div className="flex  items-center px-9 py-6  rounded-t-3xl">
            <IconButton
              onClick={onClose}
              sx={{ position: "absolute", top: 20, right: 8 }}
            >
              <CloseIcon />
            </IconButton>
         <div className='w-full'>
             <div >
             <div className="text-xl font-bold text-neutral-800 !text-left">
                 {title}
              </div>
        <p className="text-lg text-gray-700 my-6">
         {message}
        </p>
   <div className="w-full flex justify-between !mt-5"><button
               onClick={onClose}
                className="w-[105px] px-3 text-md font-medium border border-sky-700 text-sky-700 rounded-md h-[40px] "
              >
                Cancel
              </button>
                <button
                onClick={onDelete}
                  className={`w-[105px] px-3 text-md font-medium rounded-md h-[40px] text-zinc-50 bg-sky-700 hover:bg-sky-800}`}
                >
                 {action}
                </button>
              </div>
       
      </div>
      </div>
       
    </div>
      </div>
         </div>
          </DialogContent>
             </Dialog>
  );
}
