/* eslint-disable prefer-const */
import { getGlobalOptions } from "@/api/Globals/globals";
import { toCamelCase } from "@/utils/generic";
import { Typography, InputLabel, Select, SelectChangeEvent, MenuItem, Switch, TextField, Box } from "@mui/material";
// import { Box } from "lucide-react";
import { useEffect } from "react";

/* eslint-disable @typescript-eslint/no-explicit-any */
export const RenderField = ({
  col,
  value,
  rowData,
  handleFileChange,
  handleChange,
  isGlobalLoading,
  setIsGlobalLoading,
  globalOptions,
  setGlobalOptions,
}:{
  col: any;
  value: string;
  rowData: Record<string, string>;
  handleFileChange: (colId: string, event: React.ChangeEvent<HTMLInputElement>) => void;
  handleChange: (colId: string, value: string) => void;
  isGlobalLoading: boolean;
  setIsGlobalLoading: React.Dispatch<React.SetStateAction<boolean>>;
  globalOptions: { id: string; value: string }[];
  setGlobalOptions: React.Dispatch<React.SetStateAction<{ id: string; value: string }[]>>;

}) => {
    const fieldName = toCamelCase(col.name);
    // const value = rowData[fieldName] || "";

useEffect(() => {
  if (col?.fieldType !== "global_select" || !col?.name) return;
  
  const isCity = toCamelCase(col.name) === "city";
  if (isCity) return; // city handled below

  // For other global_select fields (fetch only once on mount) useEffect(() => {
      if (col?.fieldType === "global_select" && col?.name && toCamelCase(col.name)) {
        const fetchGlobalOptions = async () => {
          if (!col.name) return;
          setIsGlobalLoading(true);
          // console.log(' Object.entries(formData?.[section as keyof typeof formData])', Object.entries(formData!==null&&formData?.[section as keyof typeof formData]));
  
          try {
            let payload: any = {
              name: toCamelCase(col.name) ?? "",
            };
            if (toCamelCase(col.name) !== "city") {
              // const cleanedLabel = field.globals_name;
              const res = await getGlobalOptions(payload);
              console.log("Fetched global options:", res);
  
              // const data = await res.json();
              const data = res?.getGlobalByName;
              if (Array.isArray(data)) {
                setGlobalOptions(data);
              } else {
                console.error("Unexpected global field data", data);
                setGlobalOptions([]);
              }
            }
          } catch (err) {
            console.error("Error fetching global options:", err);
            setGlobalOptions([]);
          } finally {
            setIsGlobalLoading(false);
          }
        };
        console.log('col.globalFieldId !== undefined',col?.globalFieldId
 !== undefined);
        
        if (col?.globalFieldId
 !== undefined) {
          fetchGlobalOptions();
        }
      }
    }, [col]);

// useEffect(() => {
//   if (col?.fieldType !== "global_select" || !col?.name) return;
  
//   const isCity = toCamelCase(col.name) === "city";
//   if (!isCity) return;

//   if (!rowData["state"]) return; // only fetch when state is chosen

//   const fetchOptions = async () => {
//     setIsGlobalLoading(true);
//     try {
//       const payload = {
//         name: toCamelCase(col.name),
//         stateId: rowData["state"],
//       };
//       const res = await getGlobalOptions(payload);
//       const data = res?.getGlobalByName;
//       setGlobalOptions(Array.isArray(data) ? data : []);
//     } catch (err) {
//       console.error("Error fetching city options:", err);
//       setGlobalOptions([]);
//     } finally {
//       setIsGlobalLoading(false);
//     }
//   };

//   fetchOptions();
// }, [col?.fieldType, col?.name, rowData["state"]]);

    useEffect(() => {
      
      const fetchGlobalOptions = async () => {
        let payload: any = {
          name: toCamelCase(col.name) ?? "",
        };
        if (col?.fieldType === "global_select" && toCamelCase(col?.name) == "city") {
          
            payload["stateId"] = rowData["state"];
          }
       
        const res = await getGlobalOptions(payload);
        console.log("Fetched global options:", res);
  
        // const data = await res.json();
        const data = res?.getGlobalByName;
        if (Array.isArray(data)) {
          setGlobalOptions(data);
        } else {
          console.error("Unexpected global field data", data);
          setGlobalOptions([]);
        }
      };
      if (col?.globalFieldId
 !== undefined) {
          fetchGlobalOptions();
        }
    }, [rowData,col]);
    // Handle different field types
    switch (col.fieldType) {
      case "file":
      case "file_upload":
      case "image":
        return (
          <Box key={col.id} className="flex flex-col mb-4">
            <input
              type="file"
              id={fieldName}
              onChange={(e) => handleFileChange(col.name, e)}
              accept={
                col.fieldType === "image" 
                  ? ".jpg,.jpeg,.png,.gif,.bmp,.webp"
                  : ".pdf,.doc,.docx,.txt,.xls,.xlsx,.csv,.zip,.rar"
              }
              className="hidden"
            />
            <label
              htmlFor={fieldName}
              className="cursor-pointer border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-blue-500 transition-colors mb-4"
            >
              <Typography variant="body2" color="textSecondary" className="mb-2">
                {value ? `Selected: ${value}` : `Click to upload ${col.name}`}
              </Typography>
              <Typography variant="caption" color="textSecondary">
                {col.fieldType === "image" 
                  ? "Supports: JPG, PNG, GIF, BMP, WEBP"
                  : "Supports: PDF, DOC, DOCX, TXT, XLS, XLSX, CSV, ZIP, RAR"
                }
              </Typography>
            </label>
          </Box>
        );

      case "select":
        return (
          <Box key={col.id} className="flex flex-col mb-4">
            <InputLabel
              htmlFor={fieldName}
              shrink
              className="!text-[22px] font-semibold text-gray-800 mb-1 select-none"
            >
              {col.name}
            </InputLabel> 
            <Select
              fullWidth
              id={fieldName}
              value={value}
              onChange={(e: SelectChangeEvent) => handleChange(col.name, e.target.value)}
              size="small"
              sx={{ borderRadius: "5px" }}
              displayEmpty={true}
            > 
              <MenuItem disabled value="">
                Select {col.name}
              </MenuItem>
              {col.options?.map((option: any) => (
                <MenuItem key={option.id || option.value} value={option.value}>
                  {option.value}
                </MenuItem>
              ))}
            </Select>
          </Box>
        );
case "toggle":
  return (
    <Box key={col.id} className="flex flex-col mb-4">
      <InputLabel
        htmlFor={fieldName}
        shrink
        className="!text-[22px] font-semibold text-gray-800 mb-1 select-none"
      >
        {col.name}
      </InputLabel>
      <Box display="flex" alignItems="center">
        <Switch
          checked={value === "true"}
          onChange={(e) => handleChange(col.name, e.target.checked ? "true" : "false")}
          color="primary"
        />
        <Typography variant="body2" className="ml-2">
        </Typography>
      </Box>
    </Box>
  );
   case "global_select":
        return (
          <Box sx={{ position: "relative" }} className=' mb-4'>
            <InputLabel
        htmlFor={fieldName}
        shrink
        className="!text-[22px] font-semibold text-gray-800 mb-1 select-none"
      >
        {col.name}
      </InputLabel>
            <TextField
              select
              fullWidth
              name={fieldName}
              variant="outlined"
              size="small"
              value={value ?? ""}
              onChange={(e) =>
                handleChange(col.name,e.target.value)
              }
              // InputProps={{
              //   endAdornment: (
              //     <FieldActionsMenn onEdit={onEdit} onDelete={field.onDelete} />
              //   ),
              // }}
              SelectProps={{
                displayEmpty: true,
                MenuProps: {
                  PaperProps: {
                    sx: {
                      maxHeight: "12rem",
                      overflowY: "auto",
                    },
                  },
                },
              }}
            >
              <MenuItem disabled value="">
                {col.placeholder || `Select ${col.name}`}
              </MenuItem>
  
              {isGlobalLoading ? (
                <MenuItem disabled value="">
                  Loading options...
                </MenuItem>
              ) : globalOptions.length === 0 ? (
                <MenuItem disabled value="">
                  No options found
                </MenuItem>
              ) : (
                globalOptions.map((opt) => (
                  <MenuItem key={opt.id} value={opt.value}>
                    {opt.value}
                  </MenuItem>
                ))
              )}
            </TextField>
          </Box>
        );
      case "textarea":
        return (
          <Box key={col.id} className="flex flex-col mb-4">
            <InputLabel
              htmlFor={fieldName}
              shrink
              className="!text-[22px] font-semibold text-gray-800 mb-1 select-none"
            >
              {col.name}
            </InputLabel>
            <TextField
              fullWidth
              multiline
              minRows={4}
              placeholder={`Enter ${col.name}`}
              value={value}
              onChange={(e) => handleChange(col.name, e.target.value)}
              size="small"
              variant="outlined"
            />
          </Box>
        );

      default:
        return (
          <Box key={col.id} className="flex flex-col mb-4">
            <InputLabel
              htmlFor={fieldName}
              shrink
              className="!text-[22px] font-semibold text-gray-800 mb-1 select-none"
            >
              {col.name}
            </InputLabel>
            <TextField
              type={col.fieldType || "text"}
              fullWidth
              className="!mb-4"
              variant="outlined"
              value={value}
              onChange={(e) => handleChange(col.name, e.target.value)}
              size="small"
              placeholder={`Enter ${col.name}`}
            />
          </Box>
        );
    }
  };