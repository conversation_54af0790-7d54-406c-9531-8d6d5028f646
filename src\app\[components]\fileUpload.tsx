// components/ui/FileUpload.tsx
import { Upload } from "lucide-react"; // or use your preferred icon lib

type FileUploadProps = {
  label: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
};

const FileUpload: React.FC<FileUploadProps> = ({ label, onChange }) => (
  <div className="flex flex-col gap-2">
    <label className="text-sm font-medium text-gray-700">{label}</label>
    <div className="border border-dashed border-gray-400 rounded p-6 text-center cursor-pointer relative">
      <input
        type="file"
        onChange={onChange}
        className="hidden"
        id="file-upload"
      />
      <label htmlFor="file-upload" className="flex flex-col items-center justify-center gap-2 text-sm text-gray-500 cursor-pointer">
        <Upload className="w-6 h-6 text-gray-500" />
        <p> <span className="underline">Choose a File </span> <span>or drag & drop it</span></p>
      </label>
    </div>
  </div>
);

export default FileUpload;
