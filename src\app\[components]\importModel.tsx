'use client'
import {  Box, CircularProgress, Dialog, DialogContent, FormControl, IconButton } from '@mui/material'
import React, { useEffect, useRef } from 'react'
import { useState } from "react";
import { MenuItem, Button, InputLabel, Select } from "@mui/material";
import { getPreSignedUrl } from '@/api/file/file';
import { importData, startImport } from '@/api/import/import';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { getTemplates } from '@/api/templates/templates';
import { showToast } from '@/components/toaster/ToastProvider';
import CloseIcon from "@mui/icons-material/Close";
import { downloadExcelWithHeaders } from '@/utils/generic';
import { exportTableData } from '@/api/exportAction/export';

interface DocumentRow {
    id: number | "new";
    systemAttribute: string;
    emrMapping: string;
    required: boolean;
    unique: boolean;
  }

const initialRows: DocumentRow[] = [
    {
      id: 1,
      systemAttribute: "Provider Name",
      emrMapping: "Provider Name",
      required: true,
      unique: false,
    },
    {
      id: 2,
      systemAttribute: "Speciality",
      emrMapping: "Speciality",
      required: false,
      unique: false,
    },
    {
      id: 3,
      systemAttribute: "Patient First Name",
      emrMapping: "First Name",
      required: false,
      unique: false,
    },
    {
      id: 4,
      systemAttribute: "Middle Name",
      emrMapping: "Middle Name",
      required: true,
      unique: false,
    },
    {
      id: 5,
      systemAttribute: "Last Name",
      emrMapping: "Last Name",
      required: false,
      unique: false,
    },
    {
      id: 6,
      systemAttribute: "Primary Payer",
      emrMapping: "Primary Payer",
      required: false,
      unique: false,
    },
    {
      id: 7,
      systemAttribute: "Primary Member ID",
      emrMapping: "Primary Member ID",
      required: true,
      unique: false,
    },
    {
      id: 8,
      systemAttribute: "Received Date",
      emrMapping: "Received Date",
      required: true,
      unique: false,
    },
  ];
  

const ImportModel = ({ isOpen, onClose ,title}: {
  isOpen: boolean;
  onClose: () => void;
  title:string
}) => {
    const [emr, setEmr] = useState("upsert");
        const [fileType, setFileType] = useState("xlsx");
        const [selectedFile, setSelectedFile] = useState<string>('');
        const [loader, setLoader] = useState<boolean>(true);
        const [upload, setupload] = useState<boolean>(false);
        const[validationStrategy, setValidationStrategy] = useState<boolean>(false)
        const [templateName, setTemplateName] = useState('')
        const selectRef = useRef<HTMLSelectElement>(null);
        const [downloadOption, setDownloadOption] = useState("");
        const [rows, setRows] = useState<DocumentRow[]>(initialRows);
        const [templateFlowName, setTemplateFlowName] = useState('')
        const user = useSelector((state: RootState) => state.user.id);
        console.log('templateName',templateName);
        
        useEffect(() => {
            getTemplates({ search: "", filters:{key: title=='Payer'?'payer':title=='CPT Dictionary'?'cpt':title=="Action and Status"?"action-status-code":"exception", type:'Master',isActive:true}}).then((res) => {
                console.log('templates',res.templates.data.templates);
                setTemplateName(res.templates.data.templates?.[0]?._id)
    console.log('es.templates.data.templates',res.templates.data.templates);
    
            }).catch((err) => {
                console.error(err);
    
            })
        }, [selectedFile])
    

        useEffect(() => {
            importData({ templateId: templateName })
              .then((res) => {
                setTemplateFlowName(res.getImportConfigurationByTemplateId.collectionName);
                const mappingJson = res.getImportConfigurationByTemplateId.mappingJson;
        
                const requiredFields =
                  res.getImportConfigurationByTemplateId.requiredFields;
        
                const uniqueFields =
                  res.getImportConfigurationByTemplateId.uniqueFields;
        
                // Conversion
                const converted = Object.keys(mappingJson).map((key, index) => ({
                  id: index + 1,
                  systemAttribute: key,
                  emrMapping: mappingJson[key],
                  required: requiredFields.includes(key),
                  unique: uniqueFields.includes(key),
                }));
        
                console.log(converted);
                const outputJsonString = JSON.stringify(converted, null, 2);
        
                console.log("outputJsonString", outputJsonString, converted);
                setRows(converted);
              })
              .catch((err) => {
                console.error(err);
              });
          }, []);
    
        const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
            const file = e.target.files?.[0];
            setupload(true)
            if (file) {
                const path = `import/${file.name}`; // <-- desired path in storage
                const payload = {
                    input: { filename: path as string, contentType: "application/pdf" },
                };
    
                try {
                    const uploadPath = await getPreSignedUrl(payload);
    
                    console.log(
                        "uploadPath",
                        uploadPath.generateUploadUrl.data.uploadUrl
                    );
                    // const path = `organization/profile/${value}`; // <-- desired path in storage
                    const uploadResponse = await fetch(
                        uploadPath.generateUploadUrl.data.uploadUrl,
                        {
                            method: "PUT",
                            body: file,
                            headers: {
                                "Content-Type": "*", // fallback
                            },
                        }
                    );
    
    
                    if (!uploadResponse.ok) {
                        throw new Error("Something went wrong. Please try again.");
                    }
                    setLoader(false)
                    setSelectedFile(path);
                    console.log(uploadPath, "uploadPath");
                    showToast.success('File Uploaded Successfully.')
                } catch (e) {
                    setLoader(false)
                    setupload(false)
    showToast.error("Image upload failed:",)
                    console.error("Image upload failed:", e);
                }
            }
        };
    
        const handleimport = () => {
            const payload =
            {
                input: {
    
                    filePath: selectedFile,
                    collectionName: title=='Payer'?'payers':title=='CPT Dictionary'?'cpts':title=="Action and Status"?"action_status_code_maps":"exceptions",
                    createdBy: user,
                    type: emr,
                    templateId: templateName,
                    isStopOnError: validationStrategy,
                    // orgId:orgId
                }
            }
            startImport(payload).then((res) => {
                console.log(res);
                showToast.success(res.startImport.message)
                setSelectedFile('');
                setupload(false)
                setLoader(true)
                onClose()
            }).catch((err) => {
                console.log(err);
                showToast.error(err.message)
    
            })
        }

        const handleSelect = async (
            e:
              | React.ChangeEvent<Omit<HTMLInputElement, "value"> & { value: string }>
              | (Event & { target: { value: string; name: string } })
          ) => {
            setDownloadOption(e.target.value);
            const array = rows.map((item) => item.emrMapping);
            if (e.target.value == "withoutData") {
              downloadExcelWithHeaders(array);
            } else {
              const payload = {
                input: {
                  collection: templateFlowName,
                  createdBy: user,
                  fields: array,
                  fileType: "xlsx",
                  selectedRow: [],
                },
              };
              await exportTableData(payload)
                .then((res) => {
                  console.log(res);
                  showToast.success(res.startExport.message);
                })
                .catch((err) => {
                  console.error(err);
                });
            }
          };
          
  return (
      <Dialog open={isOpen} onClose={onClose} fullWidth maxWidth="lg">
      <DialogContent sx={{ position: "relative" }}>
            <div className="!py-4 space-y-8 bg-[white] !rounded-[16px]">
                 <IconButton
                              onClick={onClose}
                              sx={{ position: "absolute", top: 20, right: 8 }}
                            >
                              <CloseIcon />
                            </IconButton>
               
                <Box sx={{ width: '100%' }} className='pt-4'>
                    <div className='flex flex-items justify-between items-center mt-3'>
                    <p className="px-4 mt-2 text-md font-semibold text-[#1465AB] flex justify-between items-center">Import</p>
                    <FormControl>
                  <Select
                    displayEmpty
                    defaultValue=""
                    inputRef={selectRef}
                    labelId="download-option-label"
                    className="w-[250px] !h-[30px]"
                    value={downloadOption}
                    onClick={(e) => e.stopPropagation()}
                    // label="Download Option"
                    onChange={(e) => {
                      e.stopPropagation();
                      handleSelect(e);
                    }}
                    inputProps={{ "aria-label": "Download Option" }}
                  >
                    <MenuItem value="" disabled className="!hidden">
                      Download Sample
                    </MenuItem>
                    <MenuItem value="withData">Download With Data</MenuItem>
                    <MenuItem value="withoutData">
                      Download Without Data
                    </MenuItem>
                  </Select>
                </FormControl>
                    </div>
                    
                        <div className="p-6">
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">

                                {/* EMR Dropdown */}
                                <div className="w-full">
                                    <InputLabel>EMR</InputLabel>
                                    <Select
                                        className="w-full"
                                        value={emr}
                                        size='small'
                                        // label="EMR"
                                        onChange={(e) => setEmr(e.target.value)}
                                    >
                                        <MenuItem value="create">Create</MenuItem>
                                        <MenuItem value="update">Update</MenuItem>
                                        <MenuItem value="upsert">Create & Update</MenuItem>
                                    </Select>
                                </div>

                                {/* Validation Strategy */}
                                <div className="w-full">
                                    <InputLabel>Validation Strategy</InputLabel>
                                    <Select
                                        className="w-full"
                                        size='small'
                                        value={validationStrategy}
                                        // label="Validation Strategy"
                                        onChange={(e) => setValidationStrategy(e.target.value === 'true')}                                    >
                                        <MenuItem value="true">Stop on Error</MenuItem>
                                        <MenuItem value="false">Skip error and execution</MenuItem>
                                    </Select>
                                </div>


                                {/* File Type */}
                                <div className="w-full">
                                    <InputLabel>File Type</InputLabel>
                                    <Select
                                        value={fileType}
                                        size='small'
                                        className="w-full"
                                        // label="File  Type"
                                        onChange={(e) => {setFileType(e.target.value);setSelectedFile('');setupload(false)}}
                                    >
                                        <MenuItem value="xlsx">Excel</MenuItem>
                                        <MenuItem value="csv">CSV</MenuItem>
                                    </Select>
                                </div>
                            </div>

                            {/* File Import Section */}
                            <div className="mt-6 flex flex-col md:flex-row md:items-center gap-4">
                                <label className="flex items-center gap-2">
                                    <input
                                        type="file"
                                        onChange={handleFileChange}
                                        className="hidden"
                                        id="file-upload"
                                        accept={`.${fileType}`}
                                                                            />
                                    <Button
                                        variant="contained"
                                        component="span"
                                        onClick={() => document.getElementById('file-upload')}
                                        className="!text-[14px] !h-[30px] text-gray-700 !p-4 !shadow-none !my-5"
                                    >
                                        Browse File
                                    </Button>

                                </label>
                                <span className="text-gray-500">{selectedFile ? selectedFile : upload ? <CircularProgress size={12} /> : "No file selected."}</span>
                            </div>

                            {/* Import Actions */}
                            <div className="m-0 flex gap-4 flex-wrap">
                                {/* Start Import Button - Always visible if user has Import access */}
                                <Button
                                    variant="contained"
                                    disabled={loader}
                                    className="!text-[14px] !h-[30px] !bg-teal-400  text-teal-700 !p-4 !shadow-none !my-5"
                                    onClick={() => handleimport()}
                                >
                                    Start Import
                                </Button>

                                {/* Example: Export functionality - only visible if user has Export permission */}
                                {/* <PermissionWrapper
                                    moduleName="Tickets"
                                    subModuleName="All Ticket"
                                    permissionName="Export"
                                    permissions={headerProcess?.role?.permissions || []}
                                >
                                    <Button
                                        variant="outlined"
                                        className="!text-[14px] !h-[30px] !border-blue-400 !text-blue-700 !p-4 !shadow-none !my-5"
                                        onClick={() => showToast.info('Export functionality would be here')}
                                    >
                                        Export Template
                                    </Button>
                                </PermissionWrapper> */}

                                {/* Example: Delete functionality - only visible if user has Delete permission */}
                                {/* <PermissionWrapper
                                    moduleName="Tickets"
                                    subModuleName="All Ticket"
                                    permissionName="Delete"
                                    permissions={headerProcess?.role?.permissions || []}
                                >
                                    <Button
                                        variant="outlined"
                                        color="error"
                                        className="!text-[14px] !h-[30px] !p-4 !shadow-none !my-5"
                                        onClick={() => showToast.info('Delete functionality would be here')}
                                    >
                                        Clear History
                                    </Button>
                                </PermissionWrapper> */}
                            </div>
                        </div>

                    {/* </AccordionDetails>
                </Accordion> */}
            </Box>

            </div>
        </DialogContent>
                     </Dialog>
    )
}

export default ImportModel
