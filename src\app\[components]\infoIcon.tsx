import React from "react";
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { IconButton, Tooltip } from "@mui/material";

interface InfoIconProps {
  title?: string;
  className?: string;
}

const InfoIconComponent: React.FC<InfoIconProps> = ({ title = "Information", className = "" }) => {
  return (
    <Tooltip title={title}>
      <IconButton size="small" className={className}>
        <InfoOutlinedIcon fontSize="small" color="info" />
      </IconButton>
    </Tooltip>
  );
};

export default InfoIconComponent;
