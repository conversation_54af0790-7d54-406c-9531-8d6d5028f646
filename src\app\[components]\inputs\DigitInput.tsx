"use client";

import React, { useRef, useEffect } from "react";

interface DigitInputProps {
  value: string;
  isActive: boolean;
  name: string
  onChange: (value: string) => void;
  onFocus: () => void;
  onBlur?: () => void;
  error?: boolean
  helperText?: string
}

const DigitInput: React.FC<DigitInputProps> = ({
  value,
  isActive,
  name,
  onChange,
  onBlur,
  error,
  helperText,
  onFocus,
}) => {
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isActive && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isActive]);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Allow navigation with arrow keys
    if (e.key === "Backspace" && !value) {
      // Move to previous input on backspace if current is empty
      const prevInput = inputRef.current
        ?.previousElementSibling as HTMLInputElement;
      if (prevInput) {
        prevInput.focus();
      }
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    // Only take the last character if multiple are pasted
    const digit = newValue.slice(-1);

    // Only allow digits
    if (digit && /^\d$/.test(digit)) {
      onChange(digit);

      // Automatically move to next input
      const nextInput = inputRef.current
        ?.nextElementSibling as HTMLInputElement;
      if (nextInput) {
        nextInput.focus();
      }
    } else {
      // If not a digit, keep the previous value
      onChange("");
    }
  };

  return (
    <>
      <div
        className={`flex justify-center items-center text-lg bg-white rounded-md border h-[50px] w-[50px] ${isActive ? "border-sky-700" : "border-neutral-200"
          }`}
      >
        <input
          ref={inputRef}
          type="text"
          inputMode="numeric"
          pattern="[0-9]*"
          maxLength={1}
          value={value}
          name={name}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          onFocus={onFocus}
          className="w-full h-full text-center text-lg text-stone-500 bg-transparent outline-none"
          aria-label={`Verification code digit ${value || "empty"}`}
          onBlur={onBlur}
        />

      </div>
      {error && <span style={{ color: 'red' }}>{helperText}</span>}</>
  );
};

export default DigitInput;
