import { InputFieldProps } from "@/types/user";
import React from "react";

const InputField: React.FC<InputFieldProps> = ({
  label,
  name,
  type,
  disabled,
  placeholder,
  value,
  onChange,
    onBlur,
              error,
              helperText,
}) => {
  return (
    <div className="mb-5">
      <label className="mb-2.5 block text-lg font-semibold leading-6 text-neutral-800">
        {label}
      </label>
      <div className="flex items-center px-5 py-3.5 w-full bg-white rounded-md border border-neutral-200 h-[49px]">
        <input
          type={type}
          name={name}
          disabled={disabled}
          placeholder={placeholder}
          value={value}
          onChange={onChange}
          className="w-full text-lg leading-6 text-stone-500 outline-none"
          onBlur={onBlur}

        />
       
      </div>
       {error && <span style={{color:'red'}}>{helperText}</span>}
    </div>
  );
};

export default InputField;
