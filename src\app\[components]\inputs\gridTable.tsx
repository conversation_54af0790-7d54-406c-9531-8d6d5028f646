// components/CustomDataGrid.tsx
'use client';
import React from 'react';
import { DataGrid, GridColDef, GridRowsProp } from '@mui/x-data-grid';
import { Box} from '@mui/material';

type CustomDataGridProps = {
  columns: GridColDef[];
  rows: GridRowsProp;
};

const CustomDataGrid: React.FC<CustomDataGridProps> = ({ columns, rows }) => {

  return (
    <Box sx={{ width: '100%', height: 600 }}>
      <DataGrid
        rows={rows}
        columns={columns}
        // pageSize={10}
        // rowsPerPageOptions={[5, 10, 20]}
        checkboxSelection
        // disableSelectionOnClick
       sx={{
          '& .MuiDataGrid-columnHeaders': {
             backgroundColor:"#27B8AF1A",
            color: '#273B98',
            fontWeight: 'bold',
            fontSize: '0.95rem',
          },
          '& .MuiDataGrid-columnHeader': {
            backgroundColor:"#27B8AF1A"
          },
          '& .MuiDataGrid-columnSeparator': {
            display: 'none',
          },
        }}
      />
    </Box>
  );
};

export default CustomDataGrid;
