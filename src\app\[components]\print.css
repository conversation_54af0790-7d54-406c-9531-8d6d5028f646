@media print {
  .printable-table-wrapper {
    overflow: visible !important;
    height: auto !important;
    max-height: none !important;
  }
.small-font {
    font-size: 10px !important;
  }
  .printable-table {
    width: 100% !important;
    table-layout: auto;
    border-collapse: collapse;
  }

  .printable-table th,
  .printable-table td {
    border: 1px solid #ccc;
    padding: 8px;
    font-size: 12px;
    word-break: break-word;
  }

  .printable-table thead {
    display: table-header-group;
  }

  .printable-table tr {
    page-break-inside: avoid;
    break-inside: avoid;
  }

  /* Optional: show full content only during print */
  body * {
    visibility: hidden;
  }

  .printable-table-wrapper,
  .printable-table-wrapper * {
    visibility: visible;
  }

  .printable-table-wrapper {
    position: absolute;
    top: 0;
    left: 0;
  }
}
