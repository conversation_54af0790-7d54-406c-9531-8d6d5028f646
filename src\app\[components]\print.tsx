/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { RootState } from "@/store";
import React, { forwardRef } from "react";
import { useSelector } from "react-redux";

interface MUIFormRendererProps {
  data: any;
}

// ✅ Make this component support ref
const MUIFormRenderer = forwardRef<HTMLDivElement, MUIFormRendererProps>(({ data }, ref) => {
    console.log('props', data);
  const formData = useSelector((state: RootState) => state.print);
  console.log('formData',formData);
  
    return (

        <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }} ref={ref}>
            {data?.map((item: { values: string; }, i: React.Key | null | undefined) => {
                const parsedValues = typeof item.values === 'string' ?JSON.parse(item.values):item.values;
                return (
                    <div key={i} style={{
                        border: '1px solid #e5e7eb',      // Tailwind: border (gray-200)
                        borderRadius: '0.5rem',           // Tailwind: rounded (8px)
                        padding: '1rem',                  // Tailwind: p-4 (16px)
                        backgroundColor: 'white',         // Tailwind: bg-white
                        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)', // Tailwind: shadow (default)
                    }}>
                        {Object.entries(parsedValues).map(([sectionKey, sectionValue], index) => (
                            <div key={index} style={{ marginBottom: '1.5rem' }}>
                                <h2 style={{
                                    fontSize: '1.125rem', // text-lg
                                    fontWeight: 600,      // font-semibold
                                    marginBottom: '0.5rem', // mb-2
                                    borderBottom: '1px solid #e5e7eb', // border-b (default Tailwind border color)
                                    paddingBottom: '0.5rem', // pb-2
                                    textTransform: 'capitalize', // capitalize
                                }}>
                                    {sectionKey.replace(/([A-Z])/g, ' $1')}
                                </h2>

                                {/* Handle object */}
                                {typeof sectionValue === 'object' && sectionValue !== null &&  !Array.isArray(sectionValue) && (
                                    //   <table className="w-full text-sm text-left border">
                                    <div style={{ display: 'flex' , flexWrap: 'wrap', gap: '16px' }}>
                                        {Object.entries(sectionValue)?.map(([key, value], idx) => (
                                            <div key={idx}  style={{ flex: '1 0 32%' }}>
                                                <p style={{ padding: '0.5rem', margin:'0px', fontWeight: 'bold', textTransform: 'capitalize' }}>{key}:</p>
                                                    <p style={{ fontWeight: 500, padding: '0.5rem', margin:'0px', }}>{String(value)}</p>
                                            </div>
                                        ))}
                                    </div>
                                    //   </table>
                                )}

                                {Array.isArray(sectionValue) && (
                                    <div style={{ marginBottom: '1rem', overflow: 'auto' }}>
                                        <table style={{
                                            width: '100%',
                                            fontSize: '0.875rem', // Tailwind's text-sm = 14px = 0.875rem
                                            textAlign: 'left',
                                            border: '1px solid #e5e7eb', // Tailwind's default border color (gray-200)
                                        }}>
                                            <thead>
                                                <tr style={{
                                                    backgroundColor: '#f3f4f6', // Tailwind's gray-100
                                                }}>
                                                    {Object.keys(sectionValue[0] || {}).map((key, headIndex) => (
                                                        <th key={headIndex} style={{
                                                            padding: '0.5rem',           // Tailwind's p-2
                                                            textTransform: 'capitalize', // capitalize
                                                            border: '1px solid #e5e7eb', // Tailwind's default border color (gray-200)
                                                        }}>
                                                            {key}
                                                        </th>
                                                    ))}
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {sectionValue.map((row: any, rowIndex: number) => (
                                                    <tr key={rowIndex} style={{
                                                        borderTop: '1px solid #e5e7eb', // Tailwind's default border color (gray-200)
                                                    }}>
                                                        {Object.values(row).map((value, valIndex) => (
                                                            <td key={valIndex} style={{
                                                                padding: '0.5rem', // Tailwind p-2 = 8px = 0.5rem
                                                                border: '1px solid #e5e7eb', // Tailwind default border color (gray-200)
                                                            }}>
                                                                {String(value)}
                                                            </td>
                                                        ))}
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>
                );
            })}
        </div>

    );
});

MUIFormRenderer.displayName = "MUIFormRenderer";

export default MUIFormRenderer;
