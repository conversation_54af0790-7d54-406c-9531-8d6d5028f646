/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import * as React from "react";
import { TableHeader } from "./TableHeader";
import { DataTable } from "./DataTable";
// import { ClientData } from "@/types/user";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
// import Pagination from "./PaginationControls";
import { Button } from "@mui/material";
import { useParams, useRouter } from "next/navigation";
// import DeleteModal from "@/app/components/deleteModel";
import {  getByOrganization } from "@/api/organizations/organizations";
import { showToast } from "@/components/toaster/ToastProvider";
import DeleteModal from "@/components/deleteModel";
import { setSingleClient } from "@/features/client/clientByIdSlice";
import { deleteCpt } from "@/api/masters/cpt-code/cptCode";
import { deletePayer, getPayer } from "@/api/masters/payer/payer";
import { deleteProvider, getProvider } from "@/api/masters/provider/provider";
import { deleteActionStatus } from "@/api/masters/action-status/actionStatus";
import { deleteException } from "@/api/masters/exception/exception";
import { clearExportId, setExportId } from "@/features/export/exportSlice";
import { updateExpiration } from "@/api/masters/expiration/expiration";
import { setLoading } from "@/features/headers/loaderSlice";

export function ClientDataTable({ title, handleGetApi, pagination, setQuery, query, role }: { title: string, handleGetApi: () => void, pagination?: any, setQuery?: any, query?: any, role: any, }) {
  // const { tableConfig, data: initialData } = useSelector((state: RootState) => state.client);
  const tableData: any = useSelector((state: RootState) => state.client);
  const [tableConfig, setTableConfig] = React.useState<any>(); // [tableConfig]
  const [clients, setClients] = React.useState<{ [key: string]: string | boolean }[]>([]);
  const [filters] = React.useState<Record<string, string[]>>({});
  const [sortColumn, setSortColumn] = React.useState<string>('');
  const [sortDirection, setSortDirection] = React.useState<"asc" | "desc" | null>(
    'asc'
  );
  const [page, setPage] = React.useState(1);
  const [openDelete, setOpenDelete] = React.useState(false);
  const [_id, setId] = React.useState('');
  const { id } = useParams()
  const [pageSize, setPageSize] = React.useState(10);
  const [totalItems, setTotalItems] = React.useState(0);
  const [totalPages, settotalPages] = React.useState(0);
  const [openEdit, setOpenEdit] = React.useState('')
  const [changedValue, setChangedValue] = React.useState({ key: "", value: "" })

  const router = useRouter();
  const dispatch = useDispatch();
  console.log('id', id);
  const exportId = useSelector((state: RootState) => state.exportId.exportId);

  const [ids, setIds] = React.useState<string[]>([])
  console.log('ids', ids);

  React.useEffect(() => {
    if (exportId.length == 0) {
      setIds([])
    }
  }, [exportId])

  React.useEffect(() => {
    dispatch(clearExportId())
  }, [])

  React.useEffect(() => {
    setPage(pagination?.page)
    setTotalItems(pagination?.total)
    settotalPages(pagination?.totalPages)
  }, [pagination])

  React.useEffect(() => {
    console.log('tableData', tableData);

    setTableConfig(tableData.tableConfig);
    setClients(tableData.data);

  }, [tableData])


  React.useEffect(() => {
    setPageSize(query?.limit)
    setSortDirection(query?.sortOrder)
  }, [query])


  const handleAddClient = () => {
    console.log('title', title)
    if (title == 'Organizations') {
      router.push('/organizations/create');
    }
    else if (title === 'Payer') {
      router.push('/masters/payer/create');
    }
    else if (title === 'Action and Status') {
      router.push('/masters/action-status/create');
    }
    else if (title === 'CPT Dictionary') {
      router.push('/masters/cpt-code/create');
    }
    else if (title === 'Provider') {
      router.push(`/organizations/${id}/subOrganization/provider/create`);
    }
    else if (title === 'Provider') {
      router.push('/masters/action-status/create');
    }
    else if (title === 'Provider') {
      router.push('/masters/action-status/create');
    }
    else if (title === 'Exception') {
      router.push('/masters/exception/create');
    }
    else {
      router.push(`/organizations/${id}/subOrganization/create`);
    }
  };

  const handleCloseDelete = () => {
    setOpenDelete(false)
  }
  console.log('tableConfig', tableConfig);
  const handlePageChange = (newPage: number) => {
    console.log('totalSize', totalItems, totalPages, pageSize, pagination)
    if (newPage < 1 || newPage > totalPages) return;
    setPage(newPage);
    setQuery((prev: any) => ({
      ...prev,
      page: newPage,
    }));
  };

  const handlePageSizeChange = (newSize: number) => {
    console.log('totalSize', totalItems, totalPages, pageSize, pagination)
    setPageSize(newSize);
    setPage(1); // reset to page 1 when page size changes
    setQuery((prev: any) => ({
      ...prev,
      limit: newSize,
      page:1
    }));
  };

  React.useEffect(() => {
    setSortColumn(query.sortBy)
    setSortDirection(query.sortOrder)
  }, [query])

  const handleToggleSelectAll = () => {
    if (!tableConfig?.settings?.selectable) return;

    const pageClientIds = filteredAndSortedClients.map(
      (client: any) => client._id
    );

    const allOnPageSelected = pageClientIds.every((id: any) =>
      ids.includes(id)
    );

    let newIds: string[];

    if (allOnPageSelected) {
      newIds = ids.filter((id) => !pageClientIds.includes(id));
    } else {
      newIds = [...new Set([...ids, ...pageClientIds])];
    }
    setIds(newIds);
    dispatch(setExportId(newIds));
  };

  const handleToggleSelect = (id: string) => {
    handleCheck(id)
  };

  const handleCheck = (id: string) => {
    let newData: string[];

    if (ids.includes(id)) {
      // Remove the id
      newData = ids.filter(item => item !== id);
    } else {
      // Add the id
      newData = [...ids, id];
    }

    setIds(newData);
    dispatch(setExportId(newData));
  };

  const handleEdit = (id: string) => {
    setId(id);
    console.log("Edit client:", id);
    if (title === 'Payer') {
      getPayer({ id: id }).then((res) => {
        console.log(res.payer.data.payer);
        dispatch(setSingleClient(res.payer.data.payer));
        router.push(`/masters/payer/${id}`);
      }).catch((err) => {
        console.error(err);
      })
    }
    if (title === 'Provider') {
      getProvider({ id: id }).then((res) => {
        console.log(res.provider.data.provider);
        dispatch(setSingleClient(res.provider.data.provider));
        router.push(`${id}`);
      }).catch((err) => {
        console.error(err);
      })
    }
    else if (title === `CPT Dictionary`) {
      router.push(`/masters/cpt-code/${id}/edit`);
    }
    else if (title === `Action and Status`) {
      router.push(`/masters/action-status/${id}/edit`);
    }
    else if (title === `Exception`) {
      router.push(`/masters/exception/${id}/edit`);
    }
    else if (title === `Expiration`) {
      setOpenEdit(id)
    }
    else {
      getByOrganization({ clientId: id }).then((res) => {
        console.log(res.getByClient.data.client);
        dispatch(setSingleClient(res.getByClient.data.client));
        router.push(`/organizations/${id}/info`);
      }).catch((err) => {
        console.error(err);
      })
    }

  };

  const handleDelete = (id: string) => {
    setId(id);
    console.log("Delete client:", filteredAndSortedClients?.map((client: any) => ({
      ...client,
      isSelected: ids.includes(client.id),
    })), page);
    setOpenDelete(true);
  };

  const handleSave = (id: string) => {
    setLoading(true)
    console.log('changedValue', changedValue, id);
     if(changedValue.value!==null)
   {
    const payload = {
      id: id,
      input: {
        [changedValue.key]: parseInt(changedValue.value),
      }
    }
    updateExpiration(payload).then(async (res) => {
      console.log(res);
      await handleGetApi();
      setOpenEdit('')
    })
    }
    else{
      setOpenEdit('')
    }

  }
  const handleChange = (id: string, item: string, value: string) => {
    console.log(id, item, value, 'id,item,value', clients);
    setChangedValue({ key: item, value: value })
    setClients((prev: any) => {
      const updated = prev.map((log: any) =>
        log._id === id ? { ...log, [item]: value } : log
      );
      return updated
    });
  }
  const submitDelete = async () => {
    console.log('Deleting ID:', _id);

    try {
      let response;
      if (title === 'CPT Dictionary') {
        response = await deleteCpt({ id: _id });
        showToast.success(response?.deleteCpt?.data?.message || 'CPT Dictionary deleted successfully.');
      }
      else if (title === 'Payer') {
        response = await deletePayer({ id: _id });
        showToast.success(response?.deletePayor?.data?.message || 'Payer deleted successfully.');
      }
      else if (title === 'Provider') {
        response = await deleteProvider({ id: _id });
        showToast.success(response?.deletePayor?.data?.message || 'Provider deleted successfully.');
      }
      else if (title === 'Action and Status') {
        response = await deleteActionStatus({ id: _id });
        showToast.success(response?.deletePayor?.data?.message || 'Action and Status deleted successfully.');
      }
      else if (title === 'Exception') {
        response = await deleteException({ id: _id });
        showToast.success(response?.deletePayor?.data?.message || 'Exception Type deleted successfully.');
      }
      // else {
      //   response = await deleteUser({ input: { id: _id } });
      //   showToast.success(response?.deleteClient?.data?.message || 'User deleted successfully.');
      // }


      console.log('filteredAndSortedClients', filteredAndSortedClients, page)
      if ((filteredAndSortedClients.length === 1 || filteredAndSortedClients.length === 1) && page != 1) {
        handlePageChange(page - 1)
        await handleGetApi();
      } else {
        await handleGetApi();
      }
      setOpenDelete(false);
    } catch (error) {
      console.error('Delete error:', error);
      showToast.error('Something went wrong while deleting. Please try again.');
    }
  };


  const handleFilterChange = (column: string, values: string[]) => {
    setQuery((prev: any) => {
      console.log('filters', prev.filters)
      const currentFilters =
        typeof prev.filters === "string" ? JSON.parse(prev.filters) : prev.filters || {};

      const updatedFilters = {
        ...currentFilters,
        [column]: values[0],
      };

      return {
        ...prev,
        filters: JSON.stringify(updatedFilters),
        page: 1
      };
    });

    console.log('filter updated:', column, values[0]);
  };
  const handleSort = (column: string) => {
    setQuery((prev: any) => ({
      ...prev,
      sortBy: column,
      sortOrder: prev.sortOrder === 'asc' ? 'desc' : 'asc'
    }))
    setSortColumn(column);
    setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc')

  };
  const filteredAndSortedClients = React.useMemo(() => {
    let filtered = clients;

    // Apply filters
    if (tableConfig?.settings?.filterable) {
      Object.entries(filters).forEach(([column, values]) => {
        if (values.length > 0) {
          const columnConfig = tableConfig?.columns.find((col: { title: string }) => col.title === column);
          if (columnConfig) {
            filtered = filtered.filter((client) =>
              values.includes(String(client[columnConfig.id as keyof Record<string, string>]))
            );
          }
        }
      });
    }

    // Apply sorting
    if (sortColumn && sortDirection) {
      const columnConfig = tableConfig?.columns.find((col: { title: string }) => col.title === sortColumn);
      if (columnConfig?.sortable) {
        filtered = [...filtered].sort((a, b) => {
          const aVal = String(a[columnConfig.id as keyof Record<string, string>]).toLowerCase();
          const bVal = String(b[columnConfig.id as keyof Record<string, string>]).toLowerCase();

          if (sortDirection === "asc") {
            return aVal.localeCompare(bVal);
          } else {
            return bVal.localeCompare(aVal);
          }
        });
      }
    }
    return filtered;

  }, [clients, filters, sortColumn, sortDirection, tableConfig]);

  const allSelected =
    tableConfig?.settings?.selectable &&
    filteredAndSortedClients?.length > 0 &&
    filteredAndSortedClients?.every((client: any) => ids.includes(client._id));

  console.log('filteredAndSortedClients', filteredAndSortedClients, clients);

  return (
    <main className="overflow-hidden mx-auto my-0 w-full bg-white rounded-xl max-w-[1900px] shadow-[0_1px_3px_rgba(0,0,0,0.1)]">

      <hr className="border-slate-100" />
      <div className="border border-slate-100 rounded-lg">
        <p className="px-4 mt-2 text-md font-semibold text-[#1465AB] flex justify-between items-center">{title}  {title !== 'Sub Organizations' && title !== 'Expiration' && <span>
          {role
            .filter((p: { isEnabled: boolean }) => p.isEnabled)
            .map((p: { displayName: string }) => p.displayName).includes('Add') && <Button className="!h-[40px] rounded-[3px] !m-0 !bg-teal-500 p-2 w-[auto] !inset-shadow-none" onClick={handleAddClient}><span className="text-[14px] mx-2">Add New {title} </span></Button>}
        </span>}</p>
        <hr className="mx-0 mt-2 border-slate-100" />
        {tableConfig?.columns?.length !== 0 ?
          <>
            <TableHeader
              title={title}
              page={page}
              totalPages={totalPages}
              pageSize={pageSize}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              handleGetApi={handleGetApi}
              query={query}
              totalItems={totalItems} />
            <DataTable
              tableConfig={{
                ...tableConfig,
                settings: {
                  ...tableConfig?.settings,
                  defaultSortDirection: query.sortOrder as "asc" | "desc"
                }
              }}
              clients={filteredAndSortedClients?.map((client: any) => ({
                ...client,
                isSelected: ids.includes(client.id),
              }))}
              onToggleSelectAll={handleToggleSelectAll}
              onToggleSelect={handleToggleSelect}
              onEdit={handleEdit}
              handleCheck={handleCheck}
              onDelete={handleDelete}
              allSelected={allSelected}
              filters={filters}
              title={title}
              onFilterChange={handleFilterChange}
              onSort={handleSort}
              sortColumn={sortColumn}
              sortDirection={sortDirection}
              role={role}
              openEdit={openEdit}
              setOpenEdit={handleSave}
              handleChange={handleChange}
            /></>
          :
          <h5 className="px-4 my-4 text-md font-semibold text-center">No Records found</h5>}
        {/* <Pagination
          page={page}
          totalPages={totalPages}
          pageSize={pageSize}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
        /> */}
      </div>
      {openDelete && <DeleteModal isOpen={openDelete} onClose={handleCloseDelete} onDelete={submitDelete} />}
    </main>
  );
}

export default ClientDataTable;
