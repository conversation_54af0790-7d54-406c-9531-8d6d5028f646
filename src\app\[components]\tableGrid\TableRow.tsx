/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import Image from "next/image";
import * as React from "react";
import { editIcon } from "../editIcon";
import { deleteIcon } from "../deleteIcon";
import { capitalizeFirstLetter, fetchImageUrl } from "@/utils/generic";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { Edit, Eye, Save, Trash } from "lucide-react";

interface TableRowProps {
  client: Record<string, string>;
  onToggleSelect?: (id: string) => void;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  title: string | null;
  handleCheck: (id: string) => void;
  visibleColumns?: string[];
  columnName: string[];
  showSelect?: boolean;
  role: any;
  handlePrint?: (fileName: string) => void;
  openEdit?: string;
  setOpenEdit?: (id: string) => void;
  handleChange?: (id: string, item: string, value: string) => void;
}

export function TableRow({
  client,
  onEdit,
  onDelete,
  title,
  columnName,
  handleCheck,
  showSelect = true,
  role,
  handlePrint,
  openEdit,
  setOpenEdit,
  handleChange,
}: TableRowProps) {
  console.log("client", client);
  console.log(client, "columnName11");
  const exportId = useSelector((state: RootState) => state.exportId.exportId);
  console.log("exportId--", exportId);
  const userId = useSelector((state: RootState) => state.user.id);

  // const editableFields = ['importUrlTime', 'backupUrlTime', 'auditUrlTime', 'exportUrlTime'];

  // const renderValue = (item: string, client: any) => {
  //   const isEditing = openEdit === client._id && editableFields.includes(item);

  //   if (title === 'Expiration' && client?.isImport) {

  //     if (isEditing && item === 'moduleName' || item === 'exportUrlTime' &&client._id === openEdit) {
  //       return (
  //         <input
  //           type="text"
  //           value={String(client[item] ?? '')}
  //           onChange={(e) => handleChange && handleChange?.(client.id ?? client._id, item, e.target.value)}
  //           className="border px-1 py-0.5 rounded w-full"
  //         />
  //       );
  //     }

  //     return client[item] !== undefined && client[item] !== null && client[item] !== ''
  //       ? client[item]
  //       : '-';
  //   }

  //   return client[item] ?? '-';
  // };

  const renderValue = (item: string, client: any) => {
    const editableFields = [
      "importUrlTime",
      "backupUrlTime",
      "auditUrlTime",
      "exportUrlTime",
    ];
    const isEditing = client._id === openEdit;
    const isEditableField = editableFields.includes(item);
    const showInput =
      title === "Expiration" &&
      isEditing &&
      // If isImport true, allow all editable fields
      ((client.isImport && isEditableField) ||
        // If isImport false, allow only exportUrlTime
        (!client.isImport && item === "exportUrlTime"));
    console.log(
      "showInput && client._id == openEdit",
      showInput && client._id == openEdit
    );

    // Return input if condition is satisfied
    if (showInput && client._id == openEdit) {
      return (
        <input
          type="text"
          value={String(client[item] ?? "")}
          onChange={(e) => {
            handleChange?.(client._id, item, e.target.value);
          }}
          className="border px-1 py-0.5 rounded w-full"
        />
      );
    }

    // If not editable, fallback to display value or "-"
    // Also keep "-" for other fields if isImport is false
    if (title === "Expiration" && !client.isImport && client._id !== openEdit) {
      return item === "moduleName" || item === "exportUrlTime"
        ? (client[item] ?? "-")
        : "-";
    }
    console.log("client[item]", client, item, client[item]);

    return title == "File Transfer" && item == "type"
      ? capitalizeFirstLetter(client[item])
      : (client[item] ?? "-");
  };

  // const renderValue = (item: string, client: any) => {
  //   const editableFields = ['importUrlTime', 'backupUrlTime', 'auditUrlTime', 'exportUrlTime'];

  //   // Show only moduleName and exportUrlTime when isImport is false
  //   if (title === 'Expiration' && !client?.isImport) {
  //     return (item === 'moduleName' || item === 'exportUrlTime')
  //       ? client[item] ?? '-'
  //       : '-';
  //   }

  //   else if(title === 'Expiration'){
  //      if (editableFields.includes(item) && client._id === openEdit) {
  //  <input
  //         type="text"
  //         value={String(client[item] ?? '')}
  //         onChange={(e) =>  handleChange && handleChange?.(client.id ?? client._id, item, e.target.value)}
  //         className="border px-1 py-0.5 rounded w-full"
  //       />
  //      }
  //   }
  //   // Only show input when it's an editable field and edit is enabled for this row
  //   if (editableFields.includes(item) && client._id === openEdit) {
  //     return (
  //       <input
  //         type="text"
  //         value={String(client[item] ?? '')}
  //         onChange={(e) =>  handleChange && handleChange?.(client.id ?? client._id, item, e.target.value)}
  //         className="border px-1 py-0.5 rounded w-full"
  //       />
  //     );
  //   }

  //   // Default display
  //   return client[item] ?? '-';
  // };

  return (
    <tr className="table-row hover:bg-gray-50 transition-colors duration-150">
      {showSelect && title !== "File Transfer" && title !== "Expiration" && (
        <td className="sticky left-0 z-10 p-2.5 pl-8 align-middle bg-white border-r border-b border-solid !border-b-slate-100 !border-r-slate-100 w-[84px] max-sm:px-1.5 max-sm:py-2 max-sm:pl-4">
          <input
            type="checkbox"
            checked={exportId.includes(client._id)}
            onChange={() => handleCheck(client._id)}
            className="w-4 h-4 rounded border border-solid cursor-pointer border-zinc-200"
          />
        </td>
      )}

      {columnName?.map(
        (item, i) =>
          // visibleColumns.includes(`${item}`) &&
          item !== "isImport" && (
            <td
              key={i}
              className="text-sm tracking-tight pl-2 leading-6 text-gray-700 align-middle bg-white border-r border-b border-solid !border-b-slate-100 !border-r-slate-100 max-sm:px-1.5 max-sm:py-2 max-sm:text-xs"
            >
              {item === "profileImage" ? (
                <div className="flex justify-center items-center">
                  <Image
                    alt={`${client.item} profile`}
                    src={client[item]}
                    width={30}
                    height={30}
                  />
                </div>
              ) : item === "actions" ? (
                <div className="flex gap-2.5 items-center opacity-0 group-hover:opacity-100 transition-opacity">
                  {(client._id && client._id !== undefined
                    ? client._id
                    : client.id) !== userId && (
                    <button
                      onClick={() =>
                        onEdit?.(
                          client._id && client._id !== undefined
                            ? client._id
                            : client.id
                        )
                      }
                      className="p-2 rounded hover:bg-blue-50 transition-colors"
                    >
                      {editIcon}
                    </button>
                  )}

                  <button
                    onClick={() =>
                      onDelete?.(
                        client._id && client._id !== undefined
                          ? client._id
                          : client.id
                      )
                    }
                    aria-label={`Delete ${client.businessName}`}
                    className="p-2 rounded hover:bg-red-50 transition-colors"
                  >
                    {deleteIcon}
                  </button>
                </div>
              ) : (
                <div className="p-2 text-sm truncate max-w-[400px]">
                  {item.includes("download") &&
                  client.type !== "import" &&
                  client.fileType !== "json" ? (
                    <button
                      disabled={client.status !== "COMPLETED"}
                      className={`truncate ${client.status !== "COMPLETED" ? "text-gray-500 !cursor-not-allowed" : "text-blue-500 cursor-pointer"} w-full`}
                      onClick={() => fetchImageUrl(client[item])}
                    >
                      Download
                    </button>
                  ) : item.includes("download") &&
                    client.type == "import" &&
                    client.fileType !== "json" ? (
                    <p className="text-center">-</p>
                  ) : item.includes("download") && client.fileType == "json" ? (
                    <button
                      onClick={() =>
                        handlePrint !== undefined && handlePrint(client[item])
                      }
                      className={`truncate ${client.status !== "COMPLETED" ? "text-gray-500 !cursor-not-allowed" : "text-blue-500 cursor-pointer"} w-full`}
                    >
                      Print
                    </button>
                  ) : (
                    renderValue(item, client)
                  )}
                </div>
              )}
            </td>
          )
      )}
      <td className="sticky right-0 z-10 align-middle bg-white border-r border-b border-solid !border-b-slate-100 !border-r-slate-100 w-[84px] max-sm:px-1.5 max-sm:py-2 max-sm:pl-4">
        <div className="flex gap-2.5 justify-center items-center w-full">
          {/* Save Icon: Only show for the row being edited */}
          {title === "Expiration" && openEdit === (client._id ?? client.id) && (
            <button
              onClick={() =>
                setOpenEdit && setOpenEdit(client._id ?? client.id)
              }
              // disabled
              className="p-1 text-cyan-600 hover:text-cyan-800"
            >
              {/* <Delete className="w-4 h-4" /> */}
              <Save className="w-4 h-4" />
            </button>
          )}

          {/* Edit or View Icon */}
          {(client._id ?? client.id) !== userId && (
            <button
              onClick={() =>
                onEdit?.(
                  title === "File Transfer"
                    ? client.taskId
                    : (client._id ?? client.id)
                )
              }
              className="p-1 text-blue-600 hover:text-blue-800"
            >
              {role?.some(
                (p: { isEnabled: boolean; displayName: string }) =>
                  p.isEnabled &&
                  (p.displayName === "Edit" || p.displayName === "Update")
              ) || title === "File Transfer" ? (
                <Edit className="w-4 h-4" />
              ) : (
                <Eye className="w-4 h-4" />
              )}
            </button>
          )}

          {/* Delete Icon */}
          {role?.some(
            (p: { isEnabled: boolean; displayName: string }) =>
              p.isEnabled && p.displayName === "Delete"
          ) &&
            title !== "Roles" &&
            title !== "Expiration" && (
              <button
                onClick={() =>
                  onDelete?.(
                    title === "File Transfer" ? client.taskId : client._id
                  )
                }
                aria-label={`Delete ${client.businessName}`}
                className="p-1 text-red-600 hover:text-red-800"
              >
                <Trash className="w-4 h-4" />
              </button>
            )}
        </div>
      </td>
    </tr>
  );
}
