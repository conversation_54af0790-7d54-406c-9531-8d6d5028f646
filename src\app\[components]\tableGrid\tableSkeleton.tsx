const TableSkeleton = ({ rows = 5, columns = 4 }) => {
  return (
    <table className="w-full border border-gray-200">
      <thead>
        <tr>
          {Array.from({ length: columns }).map((_, i) => (
            <th key={i} className="px-4 py-2 text-left bg-gray-100 text-sm text-gray-500">
              <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse" />
            </th>
          ))}
        </tr>
      </thead>
      <tbody>
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <tr key={rowIndex} className="border-b">
            {Array.from({ length: columns }).map((_, colIndex) => (
              <td key={colIndex} className="px-4 py-3">
                <div className="h-4 bg-gray-200 rounded animate-pulse w-full" />
              </td>
            ))}
          </tr>
        ))}
      </tbody>
    </table>
  );
};

export default TableSkeleton;