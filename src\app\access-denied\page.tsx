'use client'

import Image from 'next/image';
import lockIcon from '@/assests/lockIcon.svg'
import { useEffect, useState } from 'react';
import { selectHeaders } from '@/api/header/header';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import Loader from '@/components/loader/loader';
import { useRouter } from "next/navigation";

export default function AccessDenied() {
  const id = useSelector((state: RootState) => state.user.id);
  const [loading, setLoading] = useState(true);
    const router = useRouter();
  

  useEffect(() => {
      setLoading(true);
  
      selectHeaders({  name: "organisation",
    userId: id,})
        .then((res) => {
            if (res.selectHeader.length > 0) {
 router.push('/process')
            }

         
          setLoading(false);
  
        })
        .catch(() => {
          console.log("error");
          setLoading(false);
  
        });
    }, []);
  return (
    <div className="bg-white flex flex-col items-center justify-center px-4 text-center absolute top-[40%] left-[36%]">
     {loading && <Loader/>}
      {/* Company Logo */}
      {/* <div className="absolute top-6 left-6">
        <Image
          src="/assets/asp-rcm-logo.png" // update path if different
          alt="ASP-RCM Logo"
          width={120}
          height={40}
        />
      </div> */}

      {/* Lock Icon */}
      <div className="mb-6">
        <Image
          src={lockIcon}
          alt="Lock Icon"
          width={60}
          height={60}
        />
      </div>

      {/* Access Denied Text */}
      <h1 className="text-2xl font-semibold text-gray-800 mb-2">
        Access Denied
      </h1>
      <p className="text-gray-600 max-w-md">
        You do not have permission to view this page. Please contact your administrator for assistance.
      </p>
    </div>
  );
}
