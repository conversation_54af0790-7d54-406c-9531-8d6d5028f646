@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}
[data-nextjs-dev-overlay] {
  display: none !important;
}
button{
  cursor: pointer;
}

nextjs-portal {
  display: none !important;
}


.ProseMirror:focus {
  outline: none !important;
  box-shadow: none !important;
}

.ProseMirror p:focus {
  outline: none !important;
  box-shadow: none !important;
}

/* Chrome, Edge, Safari */
::-webkit-scrollbar {
  width: 3px;    /* vertical scrollbar */
  height: 3px;   /* horizontal scrollbar */
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(100, 100, 100, 0.4); /* adjust opacity/color */
  border-radius: 10px;
}

/* Firefox */
* {
  scrollbar-width: thin; /* options: auto, thin, none */
  scrollbar-color: rgba(100, 100, 100, 0.4) transparent;
}

