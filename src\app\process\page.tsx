/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { useEffect, useState } from "react";
import { Container, Box, FormControl, InputLabel, MenuItem, Select } from "@mui/material";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import Cookies from "js-cookie";

import { setProcess, setRole } from "@/features/headers/headersSlice";
import { setOrgList } from "@/features/client/clientSlice";
import { selectHeaders } from "@/api/header/header";
import Footer from "../signup/Footer";

import rcmlogo from "../../assests/rcm-logo.png";
import logoimgae from "../../assests/loginimage.png";

import { RootState } from "@/store/rootReducer";
import Loader from "@/components/loader/loader";

const Page = () => {
  const dispatch = useDispatch();
  const router = useRouter();

  const id = useSelector((state: RootState) => state.user.id);
  const token = Cookies.get("token");

  const [query, setQuery] = useState({
    name: "organisation",
    userId: id,
    organisationId: "",
    subOrganisationId: "",
  });

  const [select1, setSelect1] = useState([]);
  const [select2, setSelect2] = useState([]);
  const [select3, setSelect3] = useState([]);
const [loading, setLoading] = useState(true);
  useEffect(() => {
    console.log('token',token);
    
    setLoading(true);
if(token){
selectHeaders(query)
      .then((res) => {
        if (query.name === "organisation") {
          if (res.selectHeader.length > 0) {
            setSelect1(res.selectHeader);
            dispatch(setOrgList(res.selectHeader));
          }
          else{
            router.push('/access-denied')
          }
        } else if (query.name === "suborganisation") {
          setSelect2(res.selectHeader);
        } else {
          setSelect3(res.selectHeader);
        }
        setLoading(false);

      })
      .catch(() => {
        console.log("error");
        setLoading(false);

      });
}
else{
    router.push("/");
}
    
  }, [query, token]);


//   useEffect(() => {
//   if (!loading && !hasChecked) {
//     if (query.name === "organisation" && select1.length === 0) {
//       setHasChecked(true); // Avoid multiple redirects
//       router.push("/access-denied");
//     }
//   }
// }, [loading, hasChecked, select1, query.name, router]);
//   useEffect(()=>{
// if(select1.length==0){
// router.push('/access-denied')
// }
//   },[select1])

  return (
    <>
    {loading && <Loader />}
    <main className="bg-white overflow-hidden min-h-screen">
      <Container maxWidth="xl" className="!p-0 !max-w-full">
        <div className="flex flex-col md:flex-row h-screen">
          {/* Left Image */}
          <div className="hidden md:block md:w-1/2 h-screen">
            <Image src={logoimgae} alt="RCM Genie illustration" className="w-full h-full object-cover" />
          </div>

          {/* Right Side Content */}
          <div className="w-full md:w-1/2 flex items-center justify-center p-4">
            <div className="w-full max-w-md">
              <section className="flex flex-col justify-center items-center p-5 min-h-screen">
                <div className="p-8 bg-white rounded-3xl shadow-2xl border-[1.357px] border-slate-200 w-[545px] max-sm:w-full">
                  <header className="flex justify-flex-start mb-6">
                    <Image src={rcmlogo} alt="RCM Genie Logo" className="w-[151px] h-[77px]" />
                  </header>

                  <Box display="flex" flexDirection="column" gap={2} mt={1}>
                    {/* Organization */}
                    <FormControl fullWidth>
                      <InputLabel id="select1-label">Organization</InputLabel>
                      <Select
                        labelId="select1-label"
                        value={query.organisationId}
                        label="Organization"
                        onChange={(e) => {
                          setQuery({
                            ...query,
                            organisationId: e.target.value,
                            name: "suborganisation",
                          });
                          dispatch(setProcess({ organizationID: e.target.value }));
                        }}
                      >
                        {select1.map(
                          (
                            item: { id: string; value: string; organisationId: string },
                            i: number
                          ) => (
                            <MenuItem
                              onClick={() => {
                                Cookies.set("orgId", item.organisationId, { expires: 1 });
                              }}
                              value={item.id}
                              key={i}
                            >
                              {item.value}
                            </MenuItem>
                          )
                        )}
                      </Select>
                    </FormControl>

                    {/* Sub-Organization */}
                    <FormControl fullWidth>
                      <InputLabel id="select2-label">Sub-Organization</InputLabel>
                      <Select
                        labelId="select2-label"
                        value={query.subOrganisationId}
                        label="Sub-Organization"
                        disabled={query.organisationId === ""}
                        onChange={(e) => {
                          setQuery({
                            ...query,
                            subOrganisationId: e.target.value,
                            name: "process",
                          });
                          dispatch(setProcess({ subOrganizationId: e.target.value }));
                        }}
                      >
                        {select2.map((item: { id: string; value: string }, i: number) => (
                          <MenuItem
                            onClick={() => {
                              Cookies.set("subOrgId", item.id, { expires: 1 });
                            }}
                            value={item.id}
                            key={i}
                          >
                            {item.value}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>

                    {/* Process */}
                    <FormControl fullWidth>
                      <InputLabel id="select3-label">Process</InputLabel>
                      <Select
                        labelId="select3-label"
                        disabled={query.subOrganisationId === ""}
                        label="Process"
                        onChange={(e) => {
                          const selected: any = e.target.value;
                          dispatch(setRole(selected));
                          dispatch(setProcess({ processId: selected.id }));
                          document.cookie = `isAssigned=true; path=/; max-age=86400; samesite=lax`;
                          sessionStorage.setItem("isAssigned", "true");
                          router.push("/tickets");
                          setLoading(false)
                        }}
                      >
                        {select3.map((item: any, i: number) => (
                          <MenuItem
                            onClick={() => {
                              Cookies.set("processId", item.id, { expires: 1 });
                            }}
                            value={item}
                            key={i}
                          >
                            {item.value}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Box>
                </div>
                <Footer />
              </section>
            </div>
          </div>
        </div>
      </Container>
    </main>
    </>
  );
};

export default Page;
