'use client'

import React, { useEffect, useState } from 'react'
import Pages from './pages'
import { checkVaildToken, getProvider } from '@/api/masters/provider/provider';
import { useDispatch } from 'react-redux';
import { setSingleClient } from '@/features/client/clientByIdSlice';
import { useSearchParams } from 'next/navigation';
import Image from 'next/image';
import clockIcon from '../../assests/clock.png'
import Loader from '@/components/loader/loader';


const Page = () => {
  const searchParams = useSearchParams();
  const id = searchParams.get('id') || "";
  const token = searchParams.get('token') || "";
  const dispatch = useDispatch();
  const [screen, setscreen] = useState('edit')
const [loader, setLoader]= useState(true)
  useEffect(() => {
    const payload = {
      id: id,
      token: token
    }
    checkVaildToken(payload).then(async() => {
      await getProvider({ id: id }).then((res) => {
        console.log(res.provider.data.provider);
        sessionStorage.setItem('token', res.provider.data.provider.loginTokens?.[0].token)
        dispatch(setSingleClient(res.provider.data.provider));
        setLoader(false)
      }).catch((err) => {
        console.error(err);
      })
    }
    ).catch((err) => {
      console.log(err, 'err.errors');
       setLoader(false)
      if (err.message.includes("expired")) {
        setscreen('expire')
      }
    })
  }, [])

  return (
    <div>

      {loader ?
      <Loader/>: screen == 'edit' ? <Pages /> :

        <div className="bg-white flex flex-col items-center justify-center px-4 text-center absolute top-[40%] left-[40%]">
          <div className="mb-6">
            <Image
              src={clockIcon}
              alt="Lock Icon"
              width={60}
              height={60}
            />
          </div>

          <h1 className="text-2xl font-semibold text-gray-800 mb-2">
            This link has expired
          </h1>
          <p className="text-gray-600 max-w-md">
            Session expired. You’re unable to continue at this time.
          </p>
        </div>
      }

    </div>
  )
}

export default Page
