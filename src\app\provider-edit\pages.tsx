'use client'
// import { useSearchParams } from 'next/navigation';
// import React from 'react'

// const Page = () => {
//       const searchParams = useSearchParams();
//   const token = searchParams.get('token');
//   return (
//     <div>
//        Token: {token}
//     </div>
//   )
// }

// export default Page


import React, { useState } from 'react'
import CreateClientPage from "../(protected)/masters/Form";
import { getTemplates } from '@/api/templates/templates';
import Image from 'next/image';
import logo from '../../assests/rcm-logo.png'
// import { form } from "../../../organizations/create/form"
// import { Form as ClientForm } from "@/types/cl
const Pages = () => {

   const [form, setForm] = useState({});
   const [templateId, setTemplateId] = useState('')  
    const [flattedValues, setFlattedValues] = useState([]);
   
      React.useEffect(() => {
        getTemplates({ search: "", filters:{key:"provider", type:'Master',isActive:true}}).then((res)=>{
      const template = res.templates.data.templates[0];
       setFlattedValues(res.templates.data.templates[0]?.view_summary?.inGrid);
      console.log('template',JSON.parse(template.fields));
      if (template && template.fields) {
        const fieldsData = template.fields;
        if (typeof fieldsData === 'string') {
            try {
              const unescaped = fieldsData.replace(/^"|"$/g, '').replace(/\\"/g, '"');
  
  // Step 2: Parse it
  const parsedJSON = JSON.parse(unescaped)[0];
  
  console.log('parsedJSON',parsedJSON);
              // console.log('fieldsData',JSON.parse(JSON.parse(fieldsData)));
              
              // const parsedFields = JSON.parse(fieldsData);
              setTemplateId(template._id);
              setForm(parsedJSON);
               } catch (error) {
              console.error("Error parsing JSON:", error);
            }
            } else {
            setTemplateId(template._id);
            setForm(fieldsData);
          }
        } else {
          console.warn("Template or fields property is missing.");
        }
        }).catch((err)=>{
          console.error(err);
        })
      },[])
  
  return (
     <div>
        <div className='border border-gray-300 p-2'>
        <Image src={logo} alt="Logo" width={150} height={150}/>
        </div>
        
        <CreateClientPage formTemplate={form} type="edit"  clientTyoe={""} templateId={templateId} flattedValues={flattedValues} access={true}/>
      </div>
  )
}

export default Pages
