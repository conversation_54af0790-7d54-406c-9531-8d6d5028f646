/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { useEffect, useState } from "react";
import Footer from "./Footer";
import Image from "next/image";
import rcmlogo from "../../assests/rcm-logo.png";
import { useRouter, useSearchParams } from "next/navigation";
import { useMsal, useIsAuthenticated } from '@azure/msal-react';
import { useDispatch } from 'react-redux';
import { setUser, clearUser } from '../../features/users/userSlice';
import { persistor } from '../../store';
import { ssoLogin } from "@/api/login/login";
import { showToast } from "@/components/toaster/ToastProvider";
import Loader from "@/components/loader/loader";
import { Dialog, DialogContent } from "@mui/material";
import Cookies from "js-cookie";

const SSOLogin = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const searchParams = useSearchParams();
  const [loader, setLoader] = useState(false);
  const [operAlert, setOperAlert] = useState(false);
  const [error, setError] = useState('');
  const [isLoaded] = useState(false);
  const [user, setUsers] = useState('');
  const { instance, accounts } = useMsal();
  const isAuthenticated = useIsAuthenticated();
 const isAssigned =Cookies.get("isAssigned")
  const [query, setQuery] = useState({
    name: 'organisation',
    userId: user,
    organisationId: '',
    subOrganisationId: ''
  });

   const handleSubmit = async () => {
    if (typeof window !== 'undefined') {
      instance.loginRedirect();
    }
  };

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const redirectPath = searchParams.get("redirect") || '/tickets';
    if (isAuthenticated && sessionStorage.getItem("ms_access_token")) {
      router.replace(redirectPath);
      setLoader(false);
    }
  }, [isAuthenticated]);

  const isCryptoAvailable = typeof window !== 'undefined' && !!window.crypto?.subtle;

  useEffect(() => {
    if (!isCryptoAvailable) {
      console.warn("Crypto not available, skipping MSAL.");
      return;
    }

    if (accounts.length > 0) {
      const request = {
        scopes: ["User.Read"],
        account: accounts[0],
      };
setLoader(true);
      instance.acquireTokenSilent(request)
        .then(async (response) => {
          setLoader(true);
          await ssoLogin({ input: { 'token': response.accessToken } }).then((ssoResponse) => {
            if (ssoResponse.agentSSOLogin.data !== null) {
              const userData = ssoResponse.agentSSOLogin.data.agent;
              dispatch(setUser({
                id: userData?.id,
                name: userData?.name,
                email: userData?.email,
                role: userData?.role,
              }));
              setQuery({ ...query, ['userId']: userData?.id })
              setUsers(userData?.id)
              const userToken = ssoResponse.agentSSOLogin.data.token;
              sessionStorage.setItem("ms_access_token", userToken);

              // Set cookie for server-side
              document.cookie = `token=${userToken}; path=/; max-age=86400; samesite=lax`;
if(isAssigned!=='true')
             { router.push('/process')}
else{router.push('/tickets')}
              // setModalOpen(true);
              setLoader(false);
            } else {
              showToast.error("SSO login failed. No user data returned.");
              setLoader(false);
            }
          }).catch((err) => {
            console.log(err);
            setLoader(false);
            setOperAlert(true)
            setError(err.error.message)
            // instance.logout({ account: instance.getActiveAccount(), postLogoutRedirectUri: "/" });
            showToast.error(err.error.message);
          });
        })
        .catch((err) => {
          console.error("Token acquisition failed:", err);
          dispatch(clearUser());
          setLoader(false);
          showToast.error("Token acquisition failed. Please log in again.");
          persistor.purge();
        });
    }
  }, [accounts, instance]);

  useEffect(() => {
    // Check cookies for token and isAssigned
    if (typeof window !== 'undefined') {
      const cookies = document.cookie.split(';').reduce((acc, cookie) => {
        const [key, value] = cookie.trim().split('=');
        acc[key] = value;
        return acc;
      }, {} as Record<string, string>);

      const hasToken = !!cookies['token'];
      const isAssigned = cookies['isAssigned'] === 'true';

      // If logged in but not assigned, open the modal
      if (hasToken && !isAssigned) {
        router.push('/process')
      }
    }
  }, []);

  return (
    <>
      {loader && <Loader />}
      <section className="flex flex-col justify-center items-center p-5 min-h-screen">
        <div className="p-8 bg-white rounded-3xl shadow-2xl border-[1.357px] border-slate-200 w-[545px] max-sm:w-full">

          <header className="flex justify-flex-start mb-6">
            <Image src={rcmlogo} alt="RCM Genie Logo" className="w-[151px] h-[77px]" />
          </header>

          {/* {!modalOpen ? */}
            <>
              <h1 className="mb-1.5 text-3xl font-bold leading-8 text-left text-sky-700 max-sm:text-2xl">
                Microsoft SSO login
              </h1>

              <button
                disabled={isLoaded}
                onClick={handleSubmit}
                className="w-full text-md font-semibold bg-sky-700 rounded-md h-[55px] text-zinc-50 mt-5"
              >
                Login with Microsoft
              </button>
            </>
        </div>
        <Footer />
      </section>
      {operAlert && <Dialog open={operAlert} fullWidth maxWidth="xs">
        <DialogContent sx={{ position: "relative" }}>
          <div className="fixed inset-0 flex justify-center items-center p-5 bg-opacity-50 z-50">
            <div className="relative bg-white rounded-3xl shadow-2xl w-[545px]">
              <div className="flex  items-center px-9 py-6  rounded-t-3xl">

                <div className='w-full'>
                  <div >
                    <div className="text-xl font-bold text-neutral-800 !text-left">
                      Alert
                    </div>
                    <p className="text-lg text-gray-700 my-6">
                      {error}
                    </p>
                    <div className="w-full flex justify-end !mt-5">
                      <button
                        onClick={() => {
                          instance.logout({ account: instance.getActiveAccount(), postLogoutRedirectUri: "/" }); setOperAlert(false)
                          setError('')
                        }}
                        className={`w-[105px] px-3 text-md font-medium rounded-md h-[40px] text-zinc-50 bg-sky-700 hover:bg-sky-800}`}
                      >
                        ok
                      </button>
                    </div>

                  </div>
                </div>

              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>}
    </>
  );
};

export default SSOLogin;
