"use client";

import React from "react";
import { Container } from "@mui/material";
import SSOLogin from "./SSOLogin";
import Image from "next/image";
import logoimgae from "../../assests/loginimage.png";

const HomePage: React.FC = () => {
  return (
    <main className="bg-white overflow-hidden min-h-screen">
  <Container maxWidth="xl" className="!p-0 !max-w-full">
    <div className="flex flex-col md:flex-row h-screen">
      {/* Left-side image: hidden at ≤768px */}
      <div className="hidden md:block md:w-1/2 h-screen">
        <Image
          src={logoimgae}
          alt="RCM Genie illustration"
          className="w-full h-full object-cover"
        />
      </div>

      {/* OTP Form Section */}
      <div className="w-full md:w-1/2 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <SSOLogin />
        </div>
      </div>
    </div>
  </Container>
</main>
  );
};

export default HomePage;
