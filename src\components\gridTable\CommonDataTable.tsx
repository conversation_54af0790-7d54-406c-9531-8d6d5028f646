"use client";
import * as React from "react";
import { TableHeader } from "./TableHeader";
import { DataTable } from "./DataTable";
// import Pagination from "./PaginationControls";
import { Button } from "@mui/material";
import { useRouter } from "next/navigation";
import { showToast } from "@/components/toaster/ToastProvider";
import { TableData } from "@/types/user";
import DeleteModals from "@/app/[components]/deleteModel";
import { getSubModulePermissionCommon } from "@/utils/generic";

interface CommonDataTableProps {
  title: string;
  tableData: TableData;
  handleGetApi: (page?: number, pageSize?: number) => void;
  addButtonText?: string;
  addButtonPath?: string;
  onAddClick?: () => void;
  onDelete?: (id: string) => Promise<unknown>;
  onEdit?: (id: string) => void;
  pagination?: {
    page: number;
    total: number;
    totalPages: number;
    limit: number;
  } | null;
  setPage: (page: number) => void;
  setQuery: (
    query:
      | Record<string, unknown>
      | ((prev: Record<string, unknown>) => Record<string, unknown>)
  ) => void;
  setPageSize: (size: number) => void;
}

export function CommonDataTable({
  title,
  tableData,
  handleGetApi,
  addButtonText = "Add New",
  addButtonPath,
  onDelete,
  onEdit,
  pagination,
  setQuery,
  setPage,
  setPageSize,
}: CommonDataTableProps) {
  const router = useRouter();
  const { tableConfig, data: initialData } = tableData;
  const [clients, setClients] = React.useState<Record<string, unknown>[]>(
    initialData || []
  );
  const [filters, setFilters] = React.useState<Record<string, string[]>>({});
  const [sortColumn, setSortColumn] = React.useState<string>("");
  const [sortDirection, setSortDirection] = React.useState<
    "asc" | "desc" | null
  >("asc");
  const [openDelete, setOpenDelete] = React.useState(false);
  const [selectedId, setSelectedId] = React.useState<string>("");

  React.useEffect(() => {
    if (initialData) {
      setClients(
        initialData.map((client) => ({
          ...client,
          isSelected: false,
        }))
      );
    }
  }, [initialData]);

  const handleToggleSelectAll = React.useCallback(() => {
    setClients((prev) =>
      prev.map((client) => ({
        ...client,
        isSelected: !allSelected,
      }))
    );
  }, [clients]);

  const handleToggleSelect = React.useCallback((id: string) => {
    setClients((prev) =>
      prev.map((client) =>
        client.id === id
          ? { ...client, isSelected: !client.isSelected }
          : client
      )
    );
  }, []);

  const handleEdit = React.useCallback(
    (id: string) => {
      if (onEdit) {
        onEdit(id);
      } else if (addButtonPath) {
        router.push(`${addButtonPath}/${id}`);
      }
    },
    [onEdit, addButtonPath, router]
  );

  const handleDelete = React.useCallback((id: string) => {
    setSelectedId(id);
    setOpenDelete(true);
  }, []);

  const handleCloseDelete = React.useCallback(() => {
    setOpenDelete(false);
  }, []);

  const submitDelete = React.useCallback(async () => {
    if (onDelete && selectedId) {
      try {
        await onDelete(selectedId);
        showToast.success("Template deleted successfully");
        handleGetApi(pagination?.page, pagination?.limit);
      } catch (error) {
        showToast.error("Failed to delete template");
        console.error("Delete error:", error);
      } finally {
        setOpenDelete(false);
      }
    } else {
      setOpenDelete(false);
    }
  }, [onDelete, selectedId, handleGetApi, pagination?.page, pagination?.limit]);

  const handleAddNew = React.useCallback(() => {
    if (addButtonPath === "/masters/desform/create") {
    } else if (addButtonPath) {
      router.push(addButtonPath);
    }
  }, [addButtonPath, router]);

  const handleFilterChange = (column: string, values: string | string[]) => {
    const valueArray = Array.isArray(values) ? values : [values];
    setFilters((prev) => ({
      ...prev,
      [column]: valueArray,
    }));
    setQuery((prev: Record<string, unknown>) => ({
      ...prev,
      filters: {
        ...((prev.filters as Record<string, unknown>) || {}),
        [column]: valueArray,
      },
      page: 1,
    }));
  };

  const handleSort = React.useCallback(
    (columnId: string) => {
      setQuery((prev) => ({
        ...prev,
        sortBy: columnId,
        sortOrder: prev.sortOrder === "asc" ? "desc" : "asc",
        page: 1,
      }));
      setSortColumn(columnId);
      setSortDirection((prev) => (prev === "asc" ? "desc" : "asc"));
    },
    [setQuery]
  );

  const handlePageChange = React.useCallback(
    (newPage: number) => {
      setPage(newPage);
    },
    [setPage]
  );

  const handlePageSizeChange = React.useCallback(
    (newSize: number) => {
      setPageSize(newSize);
    },
    [setPageSize]
  );

  const allSelected =
    tableConfig?.settings.selectable &&
    clients?.length > 0 &&
    clients?.every((client) => client.isSelected);

  return (
    <main className="overflow-hidden mx-auto my-0 w-full bg-white rounded-xl max-w-[1900px] shadow-[0_1px_3px_rgba(0,0,0,0.1)]">
      <hr className="border-slate-100" />
      <div className="border border-slate-100 rounded-lg">
        <p className="px-4 mt-2 text-md font-semibold text-[#1465AB] justify-between flex items-center">
          {title}
          <span>
            {addButtonPath &&
              getSubModulePermissionCommon(
                "Masters",
                "Des Form",
                "Create Template"
              )?.isEnabled && (
                <Button
                  className="!h-[40px] rounded-[3px] !m-0 !bg-teal-500 p-2 w-auto !inset-shadow-none"
                  onClick={handleAddNew}
                >
                  <span className="text-[14px] mx-2">{addButtonText}</span>
                </Button>
              )}
          </span>
        </p>
        <hr className="mx-0 mt-2 border-slate-100" />
        <TableHeader
          page={pagination?.page || 1}
          totalPages={pagination?.totalPages || 1}
          pageSize={pagination?.limit || 10}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
        />
        <DataTable
          tableConfig={{
            ...tableConfig,
            settings: {
              ...tableConfig.settings,
              defaultSortDirection: tableConfig.settings
                .defaultSortDirection as "asc" | "desc",
            },
          }}
          clients={clients.map((client) => ({
            ...client,
            isSelected: String(client.isSelected),
          }))}
          onToggleSelectAll={handleToggleSelectAll}
          onToggleSelect={handleToggleSelect}
          onEdit={handleEdit}
          onDelete={handleDelete}
          allSelected={allSelected}
          filters={filters}
          onFilterChange={handleFilterChange}
          onSort={handleSort}
          sortColumn={sortColumn}
          sortDirection={sortDirection}
        />
        {/* {pagination && (
          <Pagination
            page={pagination.page}
            totalPages={pagination.totalPages}
            pageSize={pagination.limit}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
          />
        )} */}
      </div>
      {openDelete && (
        <DeleteModals
          isOpen={openDelete}
          onClose={handleCloseDelete}
          onDelete={submitDelete}
          message={"Are you sure you want to delete?"}
          title={"Delete"}
          action={"Delete"}
        />
      )}
    </main>
  );
}

export default CommonDataTable;
