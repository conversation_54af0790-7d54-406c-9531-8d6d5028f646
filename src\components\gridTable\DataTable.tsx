"use client";
import * as React from "react";
import { TableColumnHeader } from "./TableColumnHeader";
import { TableRow } from "./TableRow";
import { TableConfig } from "@/types/user";

interface DataTableProps {
  tableConfig: TableConfig;
  clients: Array<Record<string, unknown>>;
  onToggleSelectAll?: () => void;
  onToggleSelect?: (id: string) => void;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  allSelected?: boolean;
  filters?: Record<string, string[]>;
  onFilterChange?: (column: string, values: string[]) => void;
  onSort?: (column: string) => void;
  sortColumn?: string;
  sortDirection?: "asc" | "desc" | null;
}

export function DataTable({
  tableConfig,
  clients,
  onToggleSelectAll,
  onToggleSelect,
  onEdit,
  onDelete,
  allSelected,
  filters = {},
  onFilterChange,
  onSort,
  // sortColumn,
  sortDirection,
}: DataTableProps) {
  return (
    <div
      className={`overflow-x-auto ${tableConfig?.settings?.responsive ? "responsive" : ""}`}
      style={{ maxHeight: "calc(100vh - 42vh)", position: "relative",minHeight: "calc(100vh - 42vh)",  }}
    >
      <table className="w-full bg-white border-collapse max-sm:text-xs">
        <thead
          style={{ position: "sticky", top: 0, zIndex: 20, background: "#fff" }}
        >
          <tr>
            {tableConfig?.settings?.selectable && (
              <th className="sticky left-0 z-20 p-0 pl-8 font-medium text-left bg-teal-50 border-r border-b border-solid !border-b-slate-100 !border-r-slate-100 w-[84px] max-sm:px-1.5 max-sm:py-2">
                <div className="w-[84px] max-sm:pl-4">
                  <input
                    type="checkbox"
                    checked={allSelected}
                    onChange={onToggleSelectAll}
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                  />
                </div>
              </th>
            )}
            {tableConfig?.columns
              ?.filter((column) => column.visible)
              .map((column) => (
                <TableColumnHeader
                  key={column.id}
                  column={column}
                  selectedFilters={filters[column.id] || []}
                  onFilterChange={
                    onFilterChange
                      ? (col, val) =>
                          onFilterChange(col, Array.isArray(val) ? val : [val])
                      : undefined
                  }
                  onSort={onSort}
                  sortDirection={sortDirection}
                />
              ))}
            <th className="sticky right-0 z-20 p-0 pl-8 font-medium text-left bg-teal-50 border-r border-b border-solid !border-b-slate-100 !border-r-slate-100 w-[84px] max-sm:px-1.5 max-sm:py-2">
              <div className="w-[84px] max-sm:pl-4">
                <p className="gap-1.5 p-2.5 h-10 text-sm font-medium tracking-normal text-blue-900 max-sm:px-1.5 max-sm:py-2 max-sm:text-xs">
                  Action
                </p>
              </div>
            </th>
          </tr>
        </thead>
        <tbody>
          {clients?.map((client) => (
            <TableRow
              key={String(client.id ?? client._id)}
              client={client}
              visibleColumns={tableConfig?.columns
                ?.filter((column) => column.visible)
                .map((column) => column.id)}
              onToggleSelect={
                tableConfig?.settings?.selectable ? onToggleSelect : undefined
              }
              onEdit={onEdit}
              onDelete={onDelete}
            />
          ))}
        </tbody>
      </table>
    </div>
  );
}
