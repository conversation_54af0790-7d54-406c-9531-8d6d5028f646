/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { Card } from "@/app/[components]/card";
import { RootState } from "@/store";
import {  Checkbox, FormControlLabel, FormGroup, Icon, InputLabel, MenuItem, Table, TableBody, TableCell, TableHead, TableRow, TextField, Typography } from "@mui/material";
import Image from "next/image";
import addIcon from '../../../assests/AddIcon.png'
import React, { useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { createUser } from "@/api/organizations/organizations";
import { showToast } from "@/components/toaster/ToastProvider";
import { useRouter } from "next/navigation";
import { getGlobalOptions } from "@/api/Globals/globals";



interface FormField {
  id: string;
  label: string;
  field_type: string;
  required?: boolean;
  placeholder?: string;
  logicConditions?: any[];
  logicJoinType?:string
  options?: { id: string; value: string }[];
  columns?: { id: string; name: string }[];
  rows?: Record<string, string>[];
}

interface SelectOption {
  id?: string;
  code?: string;
  name?: string;
  value?: string;
  [key: string]: any; // For additional properties
}

interface FormSection {
  id: string;
  name: string;
  fields: FormField[];
}

interface FormTemplate {
  sections: FormSection[];
}

const ClientOnboardingForm = ({ formTemplate }: { formTemplate: FormTemplate }) => {
  console.log('formTemplate',formTemplate);
  const collapsed = useSelector((state: RootState) => state.sideNav.sideNav);
  const id = useSelector((state: RootState) => state.user.id);
  const [formData, setFormData] = useState<Record<string, Record<string, string>>>({});
  const [openSelects, setOpenSelects] = useState<Record<string, boolean>>({});
  const [fetchedGlobals, setFetchedGlobals] = useState<Record<string, boolean>>({});
  const selectRef = useRef<HTMLInputElement>(null);
  const [globalOptions, setGlobalOptions] = useState<
      { id: string; value: string }[]
    >([]);
    const [isGlobalLoading, setIsGlobalLoading] = useState(false);
  // const [drawerOpen, setDrawerOpen] = React.useState(false);
  const isEditMode = !collapsed;
  const columnClasses = isEditMode
    ? "grid-cols-1 md:grid-cols-3"
    : "grid-cols-1 md:grid-cols-4";
  const router = useRouter();

interface ToCamelCase {
  (str: string): string;
}

const toCamelCase: ToCamelCase = (str) =>
  str.replace(/[^a-zA-Z0-9]+(.)/g, (_, chr) => chr.toUpperCase()).replace(/^([A-Z])/, (match) => match.toLowerCase());


  interface HandleFieldChange {
    (sectionName: string, fieldLabel: string, value: any): void;
  }

  const handleFieldChange: HandleFieldChange = (sectionName, fieldLabel, value) => {
    const section = toCamelCase(sectionName);
    const field = toCamelCase(fieldLabel);
    setFormData((prevData: Record<string, Record<string, string>>) => ({
      ...prevData,
      [section]: {
        ...prevData[section],
        [field]: value,
      },
    }));
  };

  // Removed redundant empty interface HandleSubmitEvent

  interface PayloadInput {
    name: string;
    email: string;
    templateId: string;
    values: string;
    type: string;
    created_by: string;
    flattenedValues: Record<string, unknown>;
  }

  interface Payload {
    input: PayloadInput;
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>): Promise<void> => {
    e.preventDefault();
    console.log("Form data payload:", formData);
    let clientName: string | null = null;
    let clientEmail: string | null = null;

    for (const section of formTemplate?.sections) {
      for (const field of section.fields) {
        if (field.label === "Legal Business Name") {
          clientName = formData[toCamelCase(section.name)][toCamelCase(field.label)];
          console.log(field.label, formData, toCamelCase(section.name));
        }
        if (field.label === "Client Email") {
          clientEmail = formData[toCamelCase(section.name)][toCamelCase(field.label)];
        }
      }
    }

    const payload: Payload = {
      input: {
        name: clientName ?? '',
        email: clientEmail ?? '',
        templateId: '6840290c79c2580f77a4643f',
        values: JSON.stringify(formTemplate) ?? '', // must be an object
        type: 'SUB_CLIENT',
        created_by: id,
        flattenedValues: {}, // Provide an appropriate value here, e.g., {} or your flattened data
      },
    };

    console.log(payload);

    await createUser(payload)
      .then((res: unknown) => {
        if (typeof res === "object" && res !== null && "code" in res) {
          showToast.success((res as { code: string }).code);
        }
        router.push('/subOrganizations');
      })
      .catch((err: unknown) => {
        if (typeof err === "object" && err !== null && "code" in err) {
          showToast.error((err as { code: string }).code);
        }
        console.error(err);
      });
  };

  const getDisplayValue = (val: string | SelectOption): string => {
    console.log('valueeeeeee', val)
    if (typeof val === 'object') {
      return val?.code || val?.name || val?.value || '';
    }
    return val;
  };

  function evaluateLogicConditions(
      conditions: any[],
      formData: any,
      logicJoinType: 'AND' | 'OR' = 'AND'
    ): boolean {
      const results = conditions.map((condition) => {
        const { fieldId, operator, value: expectedValue } = condition;
  
        let actualValue: any = undefined;
  
        // Step 1: Find the field label from formTemplate using fieldId
        let fieldLabel = '';
        for (const section of formTemplate.sections) {
          const found = section.fields.find((field: any) => field.id === fieldId);
          if (found) {
            fieldLabel = found.label;
            break;
          }
        }
  
        if (!fieldLabel) return false;
  
        const fieldKey = toCamelCase(fieldLabel);
  
        // Step 2: Find the value from formData
        for (const sectionKey in formData) {
          const section = formData[sectionKey];
          if (section && fieldKey in section) {
            const val = section[fieldKey];
            actualValue = typeof val === 'object'
              ? val?.value ?? val?.name ?? val?.code ?? val?.id
              : val;
            break;
          }
        }
  
        // Step 3: Apply logic
        switch (operator) {
          case '===':
            return actualValue === expectedValue;
          case '!==':
            return actualValue !== expectedValue;
          default:
            return false;
        }
      });
  
      return logicJoinType === 'AND' ? results.every(Boolean) : results.some(Boolean);
    }

    const fetchGlobalOptions = async (field: { label: string; }) => {
        if (!field.label) return;
        setIsGlobalLoading(true);
        console.log("Fetching global options for field:", field);
    
        try {
          const cleanedLabel = field.label.replace(/ field$/i, "").trim();
          const res = await getGlobalOptions({ name: cleanedLabel });
          console.log("Fetched global options:", res?.getGlobalByName);
    
          // const data = await res.json();
          const data = res?.getGlobalByName;
          if (Array.isArray(data)) {
            setGlobalOptions(data);
          } else {
            console.error("Unexpected global field data", data);
            setGlobalOptions([]);
          }
        } catch (err) {
          console.error("Error fetching global options:", err);
          setGlobalOptions([]);
        } finally {
          setIsGlobalLoading(false);
        }
      };

      useEffect(() => {
          if (!formTemplate?.sections) return;
      
          formTemplate.sections.forEach((section: any) => {
            section.fields.forEach((field: any) => {
              if (
                field.field_type === 'global_select' &&
                !fetchedGlobals[field.label]
              ) {
                fetchGlobalOptions(field);
                setFetchedGlobals(prev => ({ ...prev, [field.label]: true }));
              }
            });
          });
        }, [formTemplate]);

// interface RenderFieldProps {
//   field: FormField;
//   sectionName: string;
// }

const renderField = (field: FormField, sectionName: string): React.ReactNode => {
   const section = toCamelCase(sectionName);
      const fieldName = toCamelCase(field.label);
      // const fieldValue = field.label;
      const rawValue = formData?.[section]?.[fieldName] ?? "";
      const options =  field.options || [];
  
      const valueObj = typeof rawValue === 'object'
        ? rawValue
        : options.find(opt => opt.id === rawValue ||  opt.value === rawValue);
  
      const value: any = valueObj || rawValue;
      // const tableValue = formData?.[section];
  
      console.log('valueeeeeee', formData)

      if (field?.logicConditions && field?.logicConditions?.length > 0) {
        const isVisible = evaluateLogicConditions(
          field?.logicConditions,
          formData,
          field?.logicJoinType === 'AND' ? field?.logicJoinType : 'OR'
        );
        if (!isVisible) return null;
      }
      console.log('field.field_type', field.field_type);

  switch (field.field_type) {
    case "text":
    case "email":
    case "phone":
    case "number":
    case "date":
      return (
        <TextField
          fullWidth
          type={
            field.field_type === "phone"
              ? "tel"
              : field.field_type === "number"
              ? "number"
              : field.field_type === "date"
              ? "date"
              : "text"
          }
          inputProps={
            field.field_type === "number"
              ? { inputMode: "numeric", pattern: "[0-9]*" }
              : {}
          }
          placeholder={field.placeholder || field.label}
          value={value}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            handleFieldChange(sectionName, field.label, e.target.value)
          }
          required={field.required}
        />
      );
    case "select":
      return (
        <TextField
          select
          fullWidth
          value={value}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            handleFieldChange(sectionName, field.label, e.target.value)
          }
          required={field.required}
        >
          {field.options?.map((opt: { id: string; value: string }) => (
            <MenuItem key={opt.id} value={opt.value}>
              {opt.value}
            </MenuItem>
          ))}
        </TextField>
      );
       case "checkboxes":
              return (
                // <div className="flex gap-2 items-start">
                <FormGroup>
                  {field.options?.map((option) => (
                    <FormControlLabel
                      key={option.id}
                      control={
                        <Checkbox
                          checked={value?.includes(option.id)}
                          onChange={(e) => {
                            const newValue = e.target.checked
                              ? [...(value || []), option.id]
                              : (value || []).filter((v: string) => v !== option.id);
                            handleFieldChange(sectionName, field.label, newValue);
                          }}
                        />
                      }
                      label={option.value}
                    />
                  ))}
                </FormGroup>
              )
    case "grid":
      return (
        <div key={field.id} className="px-4 pb-4  overflow-x-scroll">
          <Table sx={{ border: "1px solid #E2E8F0" }}>
            <TableHead sx={{ backgroundColor: "#f0fdff" }}>
              <TableRow>
                {field.columns?.map((col: { id: string; name: string }) => (
                  <TableCell key={col.id} sx={{ color: "#1E40AF", fontWeight: 600 }}>
                    {col.name} 
                  </TableCell>
                ))}
              </TableRow>
              <TableRow>
                {field.columns?.map((col: { id: string; name: string }) => (
                  <TableCell key={col.id}>
                    <TextField
                      select
                      size="small"
                      fullWidth
                      SelectProps={{ native: true }}
                      sx={{ backgroundColor: "white" }}
                    >
                      <option value=""></option>
                    </TextField>
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {field.rows && field.rows.length > 0 ? (
                field.rows.map((row: Record<string, string>, rowIndex: number) => (
                  <TableRow key={rowIndex}>
                    {field.columns?.map((col: { id: string; name: string }) => (
                      <TableCell key={col.id}>{row[col.id] || ""}</TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={field.columns ? field.columns.length : 1}
                    align="center"
                    sx={{ color: "#64748B", fontSize: "14px" }}
                  >
                    No Data Found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      );
     case "global_select":
            console.log("global:", field);
    
            return (
              <>
                <InputLabel className="!text-[22px] font-medium text-gray-800" shrink>
                  {field.label}
                  {field?.required && <span style={{ color: "red" }}> *</span>}
                </InputLabel>
                <TextField
                  select
                  fullWidth
                  required={field.required}
                  value={getDisplayValue(value)}
                  inputRef={selectRef}
                  onChange={(e) => {
                    const selectedValue = e.target.value;
                    console.log('selectedValue', selectedValue)
                    const options =  field.options || [];
                    const fullOption = options.find(opt =>
                      opt.value === selectedValue
                    );
                    handleFieldChange(sectionName, field.label, fullOption || selectedValue);
                  }}
                  slotProps={{
                    select: {
                      open: openSelects[field.label] || false,
                      onOpen: () => setOpenSelects((prev) => ({ ...prev, [field.label]: true })),
                      onClose: () => setOpenSelects((prev) => ({ ...prev, [field.label]: false })),
                    },
                  }}
                >
                  <MenuItem disabled value="">
                    {field.placeholder || `Select ${field.label}`}
                  </MenuItem>
    
                  {isGlobalLoading ? (
                    <MenuItem disabled value="">
                      Loading options...
                    </MenuItem>
                  ) : globalOptions.length === 0 ? (
                    <MenuItem disabled value="">
                      No options found
                    </MenuItem>
                  ) : (
                    globalOptions.map((opt) => (
                      <MenuItem key={opt.id} value={opt.value}>
                        {opt.value}
                      </MenuItem>
                    ))
                  )}
                </TextField>
              </>
    
    
            );
    default:
      return <div>Unsupported field type</div>;
  }
};


  return (
    <div className="mt-4">
          <div className="space-y-6">
            <Card className="border border-gray-300 !p-0 !rounded-lg">
              
    <form onSubmit={handleSubmit} className="space-y-6">
      {formTemplate?.sections?.map((section) => (
        <div key={section.id}>
           <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2 bg-blue-50 px-2 py-1 pt-[10px]">
                          <Typography variant="h6">{section.name}</Typography>
          
                          {/* Check if any grid field exists */}
                          {section.fields.some((f) => f.field_type === "grid") && (
                            <button
    //  onClick={() =>{ 
    //   handleAddRowClick(section?.fields,section.name,)}}
      type="button"
      className="text-sm font-200 text-gray-700 bg-none px-4 py-2 rounded flex"
    >
                              <Icon><Image src={addIcon} alt="add" width={20} height={20} /></Icon>
                              <span>Add Row</span>
                            </button>
                          )}
                        </div>
              <div className={`grid ${columnClasses} gap-4 p-4`}>

          {section.fields.filter((field) => field.field_type !== "grid").map((field) => (
            <div key={field.id}>
              <InputLabel className="!text-[22px] font-medium text-gray-800" shrink>
                                     {field.label}
                                     {field?.required && <span style={{ color: "red" }}> *</span>}
                                   </InputLabel>
              {renderField(field, section.name)}
            </div>
            
          ))}
        </div>
         {section.fields.filter((field) => field.field_type === "grid").map((field) => (
            <div key={field.id}>
{renderField(field, section.name)}
            </div>
             ))}
        </div>
      ))}
      <div className="w-full flex items-end !justify-end mb-6 !pr-4">
 <button
  type="submit"
  className="bg-[#1969AE] hover:bg-[#155b96] text-white font-bold py-2 px-6 rounded "
  // onClick={()=>handleSaveAndClose()}
>
  Save &amp; Close
</button>
</div>
    </form>
     </Card>
          </div>
          {/* <CreateDrawer onSave={()=>console.log('form')} drawerOpen={drawerOpen} onClose={()=>{setDrawerOpen(false)
        console.log(form,'form')
      }} selectedRowData={selectedRowData} sectionName={sectionName} /> */}
          </div>
  );
};

export default ClientOnboardingForm;
