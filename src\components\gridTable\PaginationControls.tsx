import { ChevronLeft, ChevronRight } from 'lucide-react';

interface PaginationProps {
  page: number;
  totalPages: number;
  pageSize: number;
  pageSizes?: number[];
  onPageChange: (newPage: number) => void;
  onPageSizeChange: (newSize: number) => void;
}

export default function Pagination({
  page,
  totalPages,
  pageSize,
  pageSizes = [10, 25, 50, 100],
  onPageChange,
  onPageSizeChange,
}: PaginationProps) {
  return (
    <div className="flex items-center justify-end p-2 text-sm text-slate-400">
      {/* Per Page Dropdown */}
      <div className="flex items-center gap-2">
        <span>Per Page</span>
        <select
          value={pageSize}
          onChange={(e) => onPageSizeChange(Number(e.target.value))}
          className="border border-gray-300 rounded px-2 py-1 text-gray-600 focus:outline-none focus:ring-1 focus:ring-blue-400"
        >
          {pageSizes.map((size) => (
            <option key={size} value={size}>
              {size}
            </option>
          ))}
        </select>
      </div>

      {/* Pagination Controls */}
      <div className="flex items-center gap-2 ml-2">
        <span> Per Page</span>
        <button
          disabled={page <= 1}
          onClick={() => onPageChange(page - 1)}
          className="disabled:text-gray-300"
        >
          <ChevronLeft size={20} />
        </button>
        <input
          type="number"
          min={1}
          max={totalPages}
          value={page}
          onChange={(e) => onPageChange(Number(e.target.value))}
          className="w-10 text-center border border-gray-300 rounded px-1 py-0.5 text-gray-600 focus:outline-none"
        />
        <span>of {totalPages}</span>
        <button
          disabled={page >= totalPages}
          onClick={() => onPageChange(page + 1)}
          className="disabled:text-gray-300"
        >
          <ChevronRight size={20} />
        </button>
      </div>
    </div>
  );
}
