"use client";
import { toNormalCase } from "@/utils/generic";
import * as React from "react";
import debounce from "lodash/debounce";
import { useMemo } from "react";

interface Column {
  id: string;
  _id?: string;
  title: string;
  width?: string;
  hasFilter?: boolean;
  type: string;
  required?: boolean;
  visible?: boolean;
  sortable?: boolean;
  filterType?: string;
  placeholder?: string;
  options?: string[];
}
interface TableColumnHeaderProps {
  column: Column;
  className?: string;
  selectedFilters: string[];
  onFilterChange?: (column: string, values: string) => void;
  onSort?: (column: string) => void;
  sortDirection?: "asc" | "desc" | null;
}

export function TableColumnHeader({
  column,
  onSort,
  onFilterChange,
  sortDirection,
}: TableColumnHeaderProps) {
  const handleSort = () => {
    if (onSort) {
      onSort(column.id);
    }
  };
  const handleFilterChange = useMemo(
    () =>
      debounce((value: string) => {
        onFilterChange?.(column.id, value);
      }, 700),
    []
  );
  console.log("column", column);

  return (
    <th
       className={`sticky z-10 p-2.5 font-medium text-left bg-teal-50 border-r border-b border-solid !border-b-slate-100 !border-r-slate-100 ${
        column.id === "first-column-id" ? "left-0" : ""
      }`}
    >
      <div className="flex items-center justify-between flex-wrap gap-2">
        <div
          className={`text-sm font-medium text-blue-900 ${
            onSort ? "cursor-pointer select-none" : ""
          }`}
        >
          <span onClick={handleSort}>
                    {toNormalCase(column.title)}
                    {!column.title.includes("Image") && (
                      <span className="ml-1 text-[black]">
                        {sortDirection === "asc" ? "↓" : "↑"}
                      </span>
                    )}
                    </span>
        </div>
        {column.title === "Status" ? (
          <select
            onChange={(e) => onFilterChange?.(column.id, e.target.value)}
            className="border border-gray-300 bg-white rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-teal-500 w-full"
            defaultValue=""
          >
            <option value="true">Active</option>
            <option value="false">In-Active</option>
          </select>
        ) : column.title === "Created At" ? (
          <input
            type="date"
            onChange={(e) => onFilterChange?.(column.id, e.target.value)}
            className="border border-gray-300 bg-white rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-teal-500 w-full"
          />
        ) : (
          <input
            type="text"
            placeholder={column.placeholder}
            style={{ visibility: !column.hasFilter ? "hidden" : "visible" }}
            onChange={(e) => handleFilterChange(e.target.value)}
            className="border border-gray-300 bg-white rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-teal-500 w-full"
          />
        )}
      </div>
    </th>
  );
}
