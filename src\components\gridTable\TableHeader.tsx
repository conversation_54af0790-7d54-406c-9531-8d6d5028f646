"use client";
import * as React from "react";
import { DropdownButton } from "./DropdownButton";
import Pagination from "./PaginationControls";
// import Image from "next/image";
// import settings from "../../assests/settings.svg";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { setClientTable } from "@/features/client/clientSlice";
import { Column, TableData } from "@/types/user";
import tableConfig from "../../components/gridTable/clientData.json";

export function TableHeader({
  page,
  totalPages,
  pageSize,
  onPageChange,
  onPageSizeChange,
}: {
  page: number;
  totalPages: number;
  pageSize: number;
  onPageChange: (newPage: number) => void;
  onPageSizeChange: (newSize: number) => void;
}) {
  const dispatch = useDispatch();
  const tableConfigs = useSelector(
    (state: RootState) => state.client.tableConfig
  );

  const [showColumnSelector, setShowColumnSelector] = React.useState(false);
  const [columns, setColumns] = React.useState<Column[]>([]);
  const [, setUpdatedColumns] = React.useState<Column[]>([]);

  React.useEffect(() => {
    if (tableConfigs?.columns) {
      setColumns(tableConfigs.columns);
    }
  }, [tableConfigs]);

  /**
   * @param {string} id
   */
  const toggleColumnVisibility = (id: string) => {
    const updated = columns.map((col) =>
      col.id === id ? { ...col, visible: !col.visible } : col
    );
    setUpdatedColumns(updated);
    setColumns(updated);
  };

  const handleSave = () => {
    // dispatch(setClientTable({
    //   tableConfig: {
    //     ...tableConfigs,
    //     columns: updatedColumns
    //   }
    // }));
    setShowColumnSelector(false);
  };
  const handleReset = () => {
    dispatch(setClientTable(tableConfig as unknown as TableData));
    setShowColumnSelector(false);
  };

  const visibleCount = columns.filter((col) => col.visible).length;
  const totalCount = columns.length;

  // const viewIcon = (
  //   <svg
  //     className="view-icon"
  //     width="25"
  //     height="12"
  //     viewBox="0 0 25 12"
  //     fill="none"
  //     xmlns="http://www.w3.org/2000/svg"
  //     style={{ width: "25px", height: "12px" }}
  //   >
  //     <path
  //       fillRule="evenodd"
  //       clipRule="evenodd"
  //       d="M2.14709 6C2.8558 2.577 5.9443 0 9.6471 0C13.3498 0 16.4383 2.577 17.1471 6C16.4383 9.423 13.3498 12 9.6471 12C5.9443 12 2.8558 9.423 2.14709 6ZM9.6471 9.75C10.6417 9.75 11.5955 9.3549 12.2987 8.6517C13.002 7.9484 13.3971 6.9946 13.3971 6C13.3971 5.0054 13.002 4.0516 12.2987 3.3483C11.5955 2.6451 10.6417 2.25 9.6471 2.25C8.6525 2.25 7.6987 2.6451 6.9954 3.3483C6.2922 4.0516 5.8971 5.0054 5.8971 6C5.8971 6.9946 6.2922 7.9484 6.9954 8.6517C7.6987 9.3549 8.6525 9.75 9.6471 9.75ZM9.6471 8.25C10.2438 8.25 10.8161 8.0129 11.2381 7.591C11.66 7.169 11.8971 6.5967 11.8971 6C11.8971 5.4033 11.66 4.831 11.2381 4.409C10.8161 3.9871 10.2438 3.75 9.6471 3.75C9.0504 3.75 8.4781 3.9871 8.0561 4.409C7.6341 4.831 7.3971 5.4033 7.3971 6C7.3971 6.5967 7.6341 7.169 8.0561 7.591C8.4781 8.0129 9.0504 8.25 9.6471 8.25Z"
  //       fill="#CCCDCF"
  //     ></path>
  //   </svg>
  // );

  return (
    <header className="flex gap-5 justify-between items-center pl-4 py-2 bg-white border-b border-solid border-b-slate-100 max-md:flex-col max-md:gap-4 max-sm:px-4 max-sm:py-2.5">
      <div className="flex gap-5 items-center max-md:justify-between max-sm:flex-col max-sm:gap-2.5">
        <DropdownButton label="Actions" />
      </div>
      <div className="flex gap-5 items-center max-md:flex-wrap max-md:gap-2.5 max-md:justify-between max-sm:flex-col max-sm:gap-2.5">
        {/* <DropdownButton
          label="Default View"
          icon={viewIcon}
          className="min-w-[133px]"
        />
        <button
          className="flex gap-2.5 items-center px-4 py-2 bg-violet-50 rounded cursor-pointer"
          onClick={() => setShowColumnSelector((prev) => !prev)}
        >
          <Image src={settings} alt="settings" />
          <span className="text-xs font-medium text-gray-500">Columns</span>
        </button> */}
        <Pagination
          page={page}
          totalPages={totalPages}
          pageSize={pageSize}
          onPageChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
        />
      </div>

      {showColumnSelector && (
        <div className="absolute right-10 top-48 z-50 bg-white rounded shadow-lg p-4 w-[27%]">
          <div className="flex justify-between items-center mb-4">
            <p className="text-sm text-gray-500">
              {visibleCount} out of {totalCount} Selected
            </p>
            <button className="text-gray-700" onClick={handleReset}>
              Reset
            </button>
          </div>
          <hr className="border border-gray-100" />
          <div className="flex flex-wrap gap-2 my-4">
            {columns.map((col) => (
              <button
                key={col.id}
                className={`px-3 py-1 rounded-full text-sm ${
                  col.visible
                    ? "bg-teal-50 text-gray-500"
                    : "border border-gray-300 text-gray-600"
                }`}
                onClick={() => toggleColumnVisibility(col.id)}
              >
                {col.title}
              </button>
            ))}
          </div>
          <div className="flex justify-between">
            <button
              className="px-4 py-2 rounded bg-gray-400 text-white"
              onClick={() => {
                setColumns(tableConfigs.columns);
                setShowColumnSelector(false);
              }}
            >
              Cancel
            </button>
            <button
              className="px-4 py-2 rounded bg-teal-500 text-white"
              onClick={() => handleSave()}
            >
              Save & Close
            </button>
          </div>
        </div>
      )}
    </header>
  );
}
