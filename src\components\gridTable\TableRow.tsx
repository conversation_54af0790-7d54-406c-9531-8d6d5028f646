"use client";
import * as React from "react";
import Image from "next/image";
import { Edit, Trash, Eye } from "lucide-react";
import { getSubModulePermissionCommon } from "@/utils/generic";

interface Client {
  id?: string;
  _id?: string;
  isSelected?: boolean | string;
  profileImage?: string;
  [key: string]: unknown;
}

interface TableRowProps {
  client: Client;
  visibleColumns?: string[];
  onToggleSelect?: (id: string) => void;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
}

export function TableRow({
  client,
  visibleColumns = [],
  onToggleSelect,
  onEdit,
  onDelete,
}: TableRowProps) {
  // Get the client ID (support both id and _id fields)
  const clientId = client.id || client._id;
  
  const isSelected = client.isSelected === true || client.isSelected === "true";

  const handleToggleSelect = () => {
    if (onToggleSelect && clientId) {
      onToggleSelect(clientId);
    }
  };

  const handleEdit = () => {
    if (onEdit && clientId) {
      onEdit(clientId);
    }
  };
  
  const handleDelete = () => {
    if (onDelete && clientId) {
      onDelete(clientId);
    }
  };

  return (
    <tr className="border-b border-slate-100 hover:bg-slate-50">
      {onToggleSelect && (
        <td className="sticky left-0 z-10 p-0 pl-8 bg-white border-r border-solid border-r-slate-100 w-[84px] max-sm:px-1.5 max-sm:py-2">
          <div className="w-[84px] max-sm:pl-4">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={handleToggleSelect}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
            />
          </div>
        </td>
      )}
      {visibleColumns.map((columnId) => {
        const value = client[columnId];
        
        // Handle image type
        if (columnId === "profileImage" && value) {
          return (
            <td key={columnId} className="p-4 text-sm text-slate-500 max-sm:px-1.5 max-sm:py-2">
              <Image
                src={String(value)}
                alt="Profile"
                width={32}
                height={32}
                className="w-8 h-8 rounded-full object-cover"
              />
            </td>
          );
        }
        
        return (
          <td key={columnId} className="p-4 text-sm text-slate-500 max-sm:px-1.5 max-sm:py-2">
            {typeof value === "string" || typeof value === "number"
              ? value
              : value !== undefined && value !== null
              ? JSON.stringify(value)
              : ""}
          </td>
        );
      })}
      <td className="sticky right-0 z-10 p-0 pl-8 bg-white border-l border-solid border-l-slate-100 w-[84px] max-sm:px-1.5 max-sm:py-2">
        <div className="flex items-center space-x-2 w-[84px] max-sm:pl-4">
          {onEdit && (
            <button
              onClick={handleEdit}
              className="p-1 text-blue-600 hover:text-blue-800"
            >
              {getSubModulePermissionCommon('Masters','Des Form','Edit Template')?.isEnabled?<Edit className="w-4 h-4" />:
              <Eye className="w-4 h-4" />}
            </button>
          )}
          {onDelete && getSubModulePermissionCommon('Masters','Des Form','Delete Template')?.isEnabled &&(
            <button
              onClick={handleDelete}
              className="p-1 text-red-600 hover:text-red-800"
            >
              <Trash className="w-4 h-4" />
            </button>
          )}
        </div>
      </td>
    </tr>
  );
}
