{"tableConfig": {"columns": [{"id": "profileImage", "title": "Client Image", "width": "w-[127px]", "hasFilter": false, "type": "image", "visible": true}, {"id": "businessName", "title": "Legal Business Name", "width": "w-[218px]", "hasFilter": true, "sortable": true, "type": "text", "filterType": "text", "placeholder": "Search business name...", "visible": true}, {"id": "clientId", "title": "Client ID", "width": "w-[119px]", "hasFilter": true, "sortable": true, "type": "text", "filterType": "text", "placeholder": "Search client ID...", "visible": true}, {"id": "email", "title": "Client Email", "width": "w-[380px]", "hasFilter": true, "sortable": true, "type": "text", "filterType": "text", "placeholder": "Search email...", "visible": true}, {"id": "telephone", "title": "Telephone", "width": "w-[212px]", "hasFilter": true, "sortable": true, "type": "text", "filterType": "text", "placeholder": "Search phone...", "visible": true}, {"id": "fax", "title": "Fax", "width": "w-[172px]", "hasFilter": true, "sortable": true, "type": "text", "filterType": "text", "placeholder": "Search fax...", "visible": true}, {"id": "address", "title": "Address", "width": "w-[222px]", "hasFilter": true, "sortable": true, "type": "text", "filterType": "text", "placeholder": "Search address...", "visible": true}, {"id": "zipCode", "title": "Zip/Postal Code", "width": "w-[147px]", "hasFilter": true, "sortable": true, "type": "text", "filterType": "text", "placeholder": "Search zip code...", "visible": true}, {"id": "country", "title": "Country", "width": "w-[127px]", "hasFilter": true, "sortable": true, "type": "text", "filterType": "select", "options": ["California", "Texas", "New York", "Austin"], "visible": true}, {"id": "actions", "title": "Actions", "width": "w-[91px]", "hasFilter": false, "type": "actions", "visible": true}], "settings": {"defaultSortColumn": "businessName", "defaultSortDirection": "asc", "rowsPerPage": 10, "selectable": true, "filterable": true, "responsive": true}}, "data": [{"id": "1", "profileImage": "https://placehold.co/30x30/4a5568/4a5568", "businessName": "UnitedHealthcare", "clientId": "CLT-US-21....", "email": "<EMAIL>", "telephone": "+****************", "fax": "+****************", "address": "456 North Wabash Ave", "zipCode": "10178", "country": "California", "isSelected": false}, {"id": "2", "profileImage": "https://placehold.co/30x30/8b5a3c/8b5a3c", "businessName": "Central Reach", "clientId": "CLT-US-21....", "email": "<EMAIL>", "telephone": "+****************", "fax": "+****************", "address": "456 North Wabash Ave", "zipCode": "10178", "country": "Texas", "isSelected": false}, {"id": "3", "profileImage": "https://placehold.co/30x30/6b7280/6b7280", "businessName": "<PERSON><PERSON>", "clientId": "CLT-US-21....", "email": "<EMAIL>", "telephone": "+****************", "fax": "+****************", "address": "456 North Wabash Ave", "zipCode": "10178", "country": "Austin", "isSelected": false}, {"id": "4", "profileImage": "https://placehold.co/30x30/374151/374151", "businessName": "Artemis", "clientId": "CLT-US-21....", "email": "<EMAIL>", "telephone": "+****************", "fax": "+****************", "address": "456 North Wabash Ave", "zipCode": "10178", "country": "New York", "isSelected": false}, {"id": "5", "profileImage": "https://placehold.co/30x30/4a5568/4a5568", "businessName": "UnitedHealthcare", "clientId": "CLT-US-21....", "email": "<EMAIL>", "telephone": "+****************", "fax": "+****************", "address": "456 North Wabash Ave", "zipCode": "10178", "country": "California", "isSelected": false}]}