"use client";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>ield,
  Ty<PERSON><PERSON>,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Box,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useState, useEffect } from "react";
import {
  createTemplate,
  getTemplate,
  updateTemplate,
} from "@/api/dynamicTemplate/template";
import { showToast } from "@/components/toaster/ToastProvider";
import { useRouter } from "next/navigation";

interface Column {
  id: string;
  name: string;
  title?: string;
  type?: string;
  required?: boolean;
}

const CreateDrawer = ({
  title,
  open,
  onClose,
  onSave,
  initialData = {},
  isEdit = false,
}: {
  title: string;
  open: boolean;
  onClose: () => void;
  columns: Column[];
  onSave: (rowData: Record<string, string>) => void;
  initialData?: Record<string, string>;
  isEdit?: boolean;
}) => {
  const router = useRouter();
  const [rowData, setRowData] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    console.log("Initial Data:", initialData);

    if (open) {
      const mappedData = {
        ...initialData,
        templateType: initialData?.templateType || "",
        status: initialData?.isActive ? "Active" : "In-Active",
      };

      setRowData(mappedData);
    }
  }, [open, JSON.stringify(initialData)]);
  // console.log("Row Data:", rowData);

  const handleChange = (colId: string, value: string) => {
    setRowData((prev) => ({ ...prev, [colId]: value }));
  };

  const handleSave = async () => {
    try {
      setIsSubmitting(true);

      const requiredFields = ["templateName", "templateType", "status"];
      const missingFields = requiredFields.filter((field) => !rowData[field]);

      if (missingFields.length > 0) {
        showToast.error(
          `Please fill in all required fields: ${missingFields.join(", ")}`
        );
        setIsSubmitting(false);
        return;
      }

      const isEditOperation = isEdit || (initialData && initialData.id);

      let response;
      if (isEditOperation) {
        response = await updateTemplate({
          input: {
            id: initialData?.id,
            name: rowData?.templateName,
            status: rowData?.status === "Active" ? true : false,
            // type: rowData?.templateType || "MAIN_CLIENT",
            type: rowData?.templateType,
            description: rowData?.description || "",
          },
        });
        showToast.success("Template updated successfully");
        getTemplate({
          filters: {},
          sortBy: "",
          sortOrder: "",
          page: 1,
          limit: 10,
          search: "",
        });
        onSave(response?.updateTemplate?.data?.template || rowData);
        if (response?.updateTemplate?.data?.template?._id) {
          sessionStorage.setItem("expectFields", "true");
          router.push(
            `/masters/desform/create/basicinfo?id=${response?.updateTemplate?.data?.template?._id}`
          );
        }
      } else {
        const payload = {
          input: {
            name: rowData.templateName || "",
            status:
              rowData.status === "Active"
                ? true
                : rowData.status === "In-Active"
                  ? false
                  : false,
            // type:  rowData?.templateType || "MAIN_CLIENT",
            type: rowData?.templateType,
            description: rowData.description,
            // fields: {},
          },
        };

        response = await createTemplate(payload);
        showToast.success("Template created successfully");

        if (response?.createTemplate?.data?.template?._id) {
          router.push(
            `/masters/desform/create/basicinfo?id=${response?.createTemplate?.data?.template?._id}`
          );
        }
      }

      setRowData({});
      onClose();
    } catch (error) {
      console.error("Error saving template:", error);
      showToast.error("Failed to save template. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const requiredFields = [
    {
      id: "templateName",
      name: "Template Name",
      required: true,
      type: "text",
    },
    {
      id: "templateType",
      name: "Template Type",
      required: true,
      type: "select",
      options: [
        "Provider_Credentials",
        "Patient_Credentials",
        "VOB",
        "Medical_Coding",
        "Billing",
        "Rejections",
        "Payment_Posting",
        "Denial_Management",
        "Account_Recievable",
        "Patient_Invoicing",
        "Master",
        "Ticketing",
      ],
    },
    {
      id: "status",
      name: "Status",
      required: true,
      type: "select",
      options: ["Active", "In-Active"],
    },
  ];

  const renderField = (field: (typeof requiredFields)[0]) => {
    const value = rowData[field.id] || "";

    return (
      <Box key={field.id} className="flex flex-col mb-4">
        <InputLabel
          htmlFor={field.id}
          shrink
          className="text-lg font-semibold text-gray-800 mb-1 select-none"
        >
          {field.name}
          {field.required && <span className="text-red-500 ml-1">*</span>}
        </InputLabel>

        {field.type === "select" ? (
          <Select
            fullWidth
            id={field.id}
            value={value}
            onChange={(e: SelectChangeEvent) =>
              handleChange(field.id, e.target.value)
            }
            size="small"
            sx={{ borderRadius: "5px" }}
            required={field.required}
            displayEmpty={true}
          >
            <MenuItem disabled value="">
              Select {field.name}
            </MenuItem>
            {field.options?.map((option) => {
              const formattedLabel = option
                .replace(/_/g, " ")
                .toLowerCase()
                .replace(/\b\w/g, (c) => c.toUpperCase());
              return (
                <MenuItem key={option} value={option}>
                  {formattedLabel}
                </MenuItem>
              );
            })}
          </Select>
        ) : (
          <TextField
            fullWidth
            id={field.id}
            value={value}
            onChange={(e) => handleChange(field.id, e.target.value)}
            size="small"
            variant="outlined"
            placeholder={`Enter ${field.name}`}
            required={field.required}
            multiline={field.id === "description"}
            rows={field.id === "description" ? 3 : 1}
          />
        )}
      </Box>
    );
  };

  const drawerTitle = isEdit ? `Edit ${title}` : `Create ${title}`;

  return (
    <Drawer anchor="right" open={open} onClose={onClose}>
      <div className="w-[400px] p-6 flex flex-col h-full">
        <div className="flex justify-between items-center mb-4">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2 bg-blue-50 px-2 py-1 pt-[10px]">
            <Typography variant="h6" className="font-semibold text-gray-900">
              {drawerTitle}
            </Typography>
            <IconButton onClick={onClose} aria-label="close">
              <CloseIcon />
            </IconButton>
          </div>
        </div>

        <div className="flex-1 overflow-auto">
          {requiredFields.map(renderField)}
        </div>

        <button
          className={`text-white px-4 py-2 rounded mt-4 transition-colors ${
            isSubmitting ? "bg-gray-400 cursor-not-allowed" : ""
          }`}
          style={!isSubmitting ? { backgroundColor: "#1465ab" } : {}}
          onClick={handleSave}
          disabled={isSubmitting}
        >
          {isSubmitting ? "Saving..." : isEdit ? "Update" : "Save"}
        </button>
      </div>
    </Drawer>
  );
};

export default CreateDrawer;
