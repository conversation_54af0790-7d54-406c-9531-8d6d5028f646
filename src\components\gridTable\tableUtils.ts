import { Column, TableConfig, TableData } from "@/types/user";

/**
 * Converts raw data to table configuration
 * @param data Raw data array
 * @param idField Field to use as ID
 * @param excludeFields Fields to exclude from the table
 * @returns TableData object
 */
export function generateTableConfig(
  data: Record<string, unknown>[],
  idField: string = 'id',
  excludeFields: string[] = []
): TableData {
  if (!data || data.length === 0) {
    return {
      tableConfig: {
        columns: [],
        settings: {
          defaultSortColumn: "",
          defaultSortDirection: "asc",
          rowsPerPage: 10,
          selectable: true,
          filterable: true,
          responsive: true,
        }
      },
      data: []
    };
  }

  // Get all unique keys from all objects
  const allKeys = new Set<string>();
  data.forEach(item => {
    Object.keys(item).forEach(key => {
      if (!excludeFields.includes(key) && key !== idField) {
        allKeys.add(key);
      }
    });
  });

  // Create columns
  const columns: Column[] = Array.from(allKeys).map(key => ({
    id: key,
    title: formatColumnTitle(key),
    width: "w-[200px]",
    hasFilter: true,
    type: "text",
    visible: true,
    sortable: true,
    filterType: "text",
    placeholder: `Search ${formatColumnTitle(key).toLowerCase()}...`
  }));

  // Add default columns
  const defaultColumns: Column[] = [
    {
      id: "actions",
      title: "Actions",
      width: "w-[91px]",
      hasFilter: false,
      type: "actions",
      visible: true,
      sortable: false,
      filterType: "text",
      placeholder: "Search actions..."
    }
  ];

  const tableConfig: TableConfig = {  
    columns: [...columns, ...defaultColumns],
    settings: {
      defaultSortColumn: columns.length > 0 ? columns[0].id : "",
      defaultSortDirection: "asc",
      rowsPerPage: 10,
      selectable: true,
      filterable: true,
      responsive: true
    }
  };

  return {
    tableConfig,
    data: data.map(row => {
      const stringRow: Record<string, string> = {};
      Object.entries(row).forEach(([key, value]) => {
        stringRow[key] = value !== undefined && value !== null ? String(value) : "";
      });
      return stringRow;
    })
  };
}

/**
 * Formats a camelCase or snake_case string to Title Case
 * @param str String to format
 * @returns Formatted string
 */
export function formatColumnTitle(str: string): string {
  return str
    // Insert a space before all uppercase letters
    .replace(/([A-Z])/g, ' $1')
    // Replace underscores with spaces
    .replace(/_/g, ' ')
    // Capitalize the first letter of each word
    .replace(/\b\w/g, c => c.toUpperCase())
    // Trim any leading spaces
    .trim();
}

/**
 * Converts a string to camelCase
 * @param str String to convert
 * @returns camelCase string
 */
export function toCamelCase(str?: string): string {
  if (!str || typeof str !== "string") return "";

  return str
    .replace(/\s(.)/g, (_, group1) => group1.toUpperCase())
    .replace(/\s/g, "")
    .replace(/^(.)/, (_, group1) => group1.toLowerCase())
    .replace(/[^a-zA-Z0-9]/g, '');
}
/**
 * Applies filters to data
 * @param data Data to filter
 * @param filters Filter configuration
 * @param columns Column configuration
 * @returns Filtered data
 */
export function applyFilters(
  data: Record<string, unknown>[],
  filters: Record<string, string[]>,
  columns: Column[]
): Record<string, unknown>[] {
  if (!data || data.length === 0 || Object.keys(filters).length === 0) {
    return data;
  }

  return data.filter(item => {
    return Object.entries(filters).every(([column, values]) => {
      if (values.length === 0) return true;
      
      const columnConfig = columns.find(col => col.id === column);
      if (!columnConfig) return true;
      
      const cellValue = String(item[columnConfig.id] || '').toLowerCase();
      return values.some(value => cellValue.includes(value.toLowerCase()));
    });
  });
}

export function formatDate(
  date: string | number | Date,
  locale = "en-IN",
  options: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "short",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit"
  }
): string {
  if (!date) return "";
  try {
    return new Date(date).toLocaleString(locale, options);
  } catch {
    return "";
  }
}
