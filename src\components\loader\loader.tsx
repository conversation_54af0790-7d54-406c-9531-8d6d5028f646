import { CircularProgress, Box } from "@mui/material";
 
const Loader = () => {
  return (
    <Box
      sx={{
        position: "fixed",
        top: 0,
        left: 0,
        width: "100vw",
        height: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        bgcolor: "rgba(255, 255, 255, 0.6)",
        zIndex: (theme) => theme.zIndex.modal + 1, // ensure it's above modals
      }}
    >
      <CircularProgress color="primary" />
    </Box>
  );
};
 
export default Loader;