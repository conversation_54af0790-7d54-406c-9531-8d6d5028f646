import React from "react";
import { MenuItem, Select } from "@mui/material";
import { useTicketPermissions } from "@/hooks/useTicketPermissions";

interface PermissionBasedActionsProps {
  selectedAction: string;
  onActionChange: (value: string) => void;
  className?: string;
  style?: React.CSSProperties;
}

export const PermissionBasedActions: React.FC<PermissionBasedActionsProps> = ({
  selectedAction,
  onActionChange,
  className = "w-[150px] !text-xs !text-gray-500",
  style = { color: "#6B7280" },
}) => {
  const permissions = useTicketPermissions();

  return (
    <Select
      displayEmpty
      value={selectedAction}
      onChange={(e) => onActionChange(e.target.value)}
      sx={{
        "& .MuiSelect-select": {
          padding: "10px",
        },
      }}
      style={style}
      className={className}
    >
      <MenuItem value="" disabled className="!text-gray-500 !text-xs">
        Actions
      </MenuItem>

      {/* Ready For Allocation - Always show if any allocation permission exists */}
      {(permissions.canAllocate || permissions.canReallocate) && (
        <MenuItem
          value="ReadyForAllocation"
          className="!text-gray-500 !text-xs"
        >
          Ready For Allocation
        </MenuItem>
      )}

      {/* Allocate */}
      {permissions.canAllocate && (
        <MenuItem value="Allocate" className="!text-gray-500 !text-xs">
          Allocate
        </MenuItem>
      )}

      {/* Reallocate */}
      {permissions.canReallocate && (
        <MenuItem value="Reallocate" className="!text-gray-500 !text-xs">
          Reallocate
        </MenuItem>
      )}

      {/* Export CSV */}
      {permissions.canExport && (
        <MenuItem value="ExportCSV" className="!text-gray-500 !text-xs">
          Export as CSV
        </MenuItem>
      )}

      {/* Export Excel - use specific Excel permission if available, fallback to general Export */}
      {(permissions.canExportToExcel || permissions.canExport) && (
        <MenuItem value="ExportExcel" className="!text-gray-500 !text-xs">
          Export as Excel
        </MenuItem>
      )}

      {/* Self - if this permission exists */}
      {permissions.canSelf && (
        <MenuItem value="Self" className="!text-gray-500 !text-xs">
          Self
        </MenuItem>
      )}

      {/* Print */}
      {permissions.canPrint && (
        <MenuItem value="Print" className="!text-gray-500 !text-xs">
          Print
        </MenuItem>
      )}
    </Select>
  );
};

export default PermissionBasedActions;
