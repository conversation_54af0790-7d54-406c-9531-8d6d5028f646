import React from 'react';
import { usePathname } from 'next/navigation';
import { getSubModulePermissionCommon } from '@/utils/generic';

interface PermissionWrapperProps {
  children: React.ReactNode;
  moduleName: string;
  subModuleName?: string;
  permissionName: string;
  fallback?: React.ReactNode;
  autoDetectSubModule?: boolean; // For ticket components
}

export const PermissionWrapper: React.FC<PermissionWrapperProps> = ({
  children,
  moduleName,
  subModuleName,
  permissionName,
  fallback = null,
  autoDetectSubModule = false
}) => {
  const pathname = usePathname();

  // Auto-detect submodule for ticket components
  const detectedSubModule = React.useMemo(() => {
    if (!autoDetectSubModule || subModuleName) return subModuleName;
    
    if (pathname.includes('/Alltickets')) return 'All Ticket';
    if (pathname.includes('/Exceptions')) return 'Exception';
    if (pathname.includes('/Completed')) return 'Completed';
    if (pathname.includes('/Source')) return 'Source';
    if (pathname.includes('/import')) return 'Import';
    return 'All Ticket'; // Default fallback
  }, [pathname, subModuleName, autoDetectSubModule]);

  // Check permission
  const hasPermission = React.useMemo(() => {
    if (!detectedSubModule) return false;
    
    const permission = getSubModulePermissionCommon(
      moduleName, 
      detectedSubModule, 
      permissionName
    );
    
    return permission?.isEnabled ?? false;
  }, [moduleName, detectedSubModule, permissionName]);

  // Return children if has permission, otherwise return fallback
  return hasPermission ? <>{children}</> : <>{fallback}</>;
};

// Specific wrapper for ticket components
export const TicketPermissionWrapper: React.FC<Omit<PermissionWrapperProps, 'moduleName' | 'autoDetectSubModule'>> = ({
  children,
  subModuleName,
  permissionName,
  fallback = null
}) => {
  return (
    <PermissionWrapper
      moduleName="Tickets"
      subModuleName={subModuleName}
      permissionName={permissionName}
      fallback={fallback}
      autoDetectSubModule={true}
    >
      {children}
    </PermissionWrapper>
  );
};

export default PermissionWrapper;
