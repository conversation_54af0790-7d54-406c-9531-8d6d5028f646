"use client";

import React, { useState, useEffect, useRef } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Autocomplete,
  TextField,
  Typography,
  Box,
  CircularProgress,
  Alert,
  IconButton,
} from "@mui/material";
import Button from "@/app/[components]/Button1";
import { Close } from "@mui/icons-material";
import { useUserList, type User } from "../../hooks/useUserList";
import { allocateClient } from "../../api/ProviderCredentials/provider";
import { showToast } from "../toaster/ToastProvider";
interface AllocationPopupProps {
  isOpen: boolean;
  onClose: () => void;
  onAllocate: (userId: string, userName: string) => void;
  title: string;
  clientId: string;
  currentAssignee?: string;
  loading?: boolean;
}

export function AllocationPopup({
  isOpen,
  onClose,
  onAllocate,
  title,
  clientId,
  loading = false,
}: AllocationPopupProps) {
  const { users, loading: isLoading, error, refetch } = useUserList();
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [allocating, setAllocating] = useState(false);
  const hasRefetchedRef = useRef(false);

  useEffect(() => {
    if (isOpen && !hasRefetchedRef.current) {
      hasRefetchedRef.current = true;
      refetch();
    } else if (!isOpen) {
      hasRefetchedRef.current = false;
    }
  }, [isOpen, refetch]);

  const activeUsers = users.filter((user) => user.isActive);

  const handleAllocate = async () => {
    if (!selectedUser) {
      alert("Please select a user to allocate.");
      return;
    }

    setAllocating(true);

    try {
      const response = await allocateClient({
        input: {
          id: clientId,
          assignedTo: selectedUser.id,
        },
      });

      if (response) {
        showToast.success(`Successfully allocated to ${selectedUser.name}`);
        onAllocate(selectedUser.id, selectedUser.name);
        handleClose();
      }
    } catch (error: unknown) {
      console.error("Allocation failed:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to allocate. Please try again.";
      showToast.error(errorMessage);
    } finally {
      setAllocating(false);
    }
  };

  const handleClose = () => {
    setSelectedUser(null);
    onClose();
  };

  return (
    <Dialog
      open={isOpen}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      slotProps={{
        paper: {
          sx: { minHeight: "200px", maxHeight: "50vh", maxWidth: "500px" },
        },
      }}
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6" component="div" className="font-semibold ">
            {title}
          </Typography>
          <IconButton onClick={handleClose} size="small">
            <Close />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Allocate to
        </Typography> */}

        {isLoading ? (
          <Box
            display="flex"
            alignItems="center"
            justifyContent="center"
            py={2}
          >
            <CircularProgress size={20} sx={{ mr: 1 }} />
            <Typography variant="body2">Loading users...</Typography>
          </Box>
        ) : error ? (
          <Box>
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
            <Button
              onClick={refetch}
              text="Retry"
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded"
            />
          </Box>
        ) : (
          <Autocomplete
            options={activeUsers}
            getOptionLabel={(option) => `${option.name} (${option.email})`}
            value={selectedUser}
            onChange={(_, newValue) => setSelectedUser(newValue)}
            renderInput={(params) => (
              <TextField
                {...params}
                placeholder="Search and select a user..."
                variant="outlined"
                fullWidth
              />
            )}
            renderOption={(props, option) => (
              <Box component="li" {...props}>
                <Box>
                  <Typography variant="body2" fontWeight="medium">
                    {option.name}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {option.email} • {option.role}
                    {option.department && ` • ${option.department}`}
                  </Typography>
                </Box>
              </Box>
            )}
            noOptionsText="No users found"
            sx={{ m: 2 }}
          />
        )}
      </DialogContent>

      <DialogActions sx={{ justifyContent: "space-between", px: 3, py: 2 }}>
        <Button
          className="buttonstyle bg-[#05437A] w-30 hover:[#05437A] text-white rounded p-2"
          onClick={handleClose}
          variant="outlined"
          text="Cancel"
        >
          Cancel
        </Button>
        <Button
          onClick={handleAllocate}
          className="buttonstyle bg-teal-500 w-30 hover:bg-teal-600 text-white rounded p-2"
          disabled={!selectedUser || loading || allocating}
          variant="contained"
          text={allocating ? "Allocating..." : "Allocate"}
        >
          {allocating ? "Allocating..." : "Allocate"}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
