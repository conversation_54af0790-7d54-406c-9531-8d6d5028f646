/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import * as React from "react";
import { TableHeader } from "./TableHeader";
import { DataTable } from "./DataTable";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
// import Pagination from "./PaginationControls";
import { usePathname, useRouter } from "next/navigation";
import { deleteUser } from "@/api/organizations/organizations";
import { showToast } from "@/components/toaster/ToastProvider";
import DeleteModal from "@/components/deleteModel";
import Loader from "@/components/loader/loader";
import { setExportId } from "@/features/export/exportSlice";

export function ClientDataTable({
  title,
  handleGetApi,
  pagination,
  setQuery,
  query,
  fieldTypes,
  hasData = true,
}: {
  title: string;
  handleGetApi: () => void;
  pagination?: any;
  setQuery?: any;
  query?: any;
  tittle?: string;
  fieldTypes?: Record<string, string>;
  hasData?: boolean;
}) {
  const tableData: any = useSelector((state: RootState) => state?.client);
  const [tableConfig, setTableConfig] = React.useState<any>();
  const [clients, setClients] = React.useState<
    { [key: string]: string | boolean }[]
  >([]);
  const [filters] = React.useState<Record<string, string[]>>({});
  const [sortColumn, setSortColumn] = React.useState<string>("");
  const [sortDirection, setSortDirection] = React.useState<
    "asc" | "desc" | null
  >("asc");
  const [page, setPage] = React.useState(1);
  const [openDelete, setOpenDelete] = React.useState(false);
  const [_id, setId] = React.useState("");
  const [loader, setLoader] = React.useState(false);
  const [pageSize, setPageSize] = React.useState(10);
  const [totalItems, setTotalItems] = React.useState(0);
  const totalPages = Math.ceil(totalItems / pageSize);
  const router = useRouter();
  const pathName = usePathname();
  const dispatch = useDispatch();
  const [ids, setIds] = React.useState<string[]>([]);

  React.useEffect(() => {
    setPage(pagination?.page);
    const totalCount = pagination?.total || pagination?.totalItems || 0;
    setTotalItems(totalCount);
  }, [pagination]);

  React.useEffect(() => {
    setSortColumn(query.sortBy);
    setSortDirection(query.sortOrder);
  }, [query]);

  React.useEffect(() => {
    setTableConfig(tableData.tableConfig);
    setClients(tableData.data);
  }, [tableData]);

  // const handleAddClient = () => {
  //   if (title == "Organizations") {
  //     router.push("/organizations/create");
  //   } else {
  //     router.push(`/organizations/${id}/subOrganization/create`);
  //   }
  // };

  const handleCloseDelete = () => {
    setOpenDelete(false);
  };
  const handlePageChange = (newPage: number) => {
    if (newPage < 1 || newPage > totalPages) return;
    setPage(newPage);
    setQuery((prev: any) => ({
      ...prev,
      page: newPage,
    }));
  };

  const handlePageSizeChange = (newSize: number) => {
    setPageSize(newSize);
    setPage(1);
    setQuery((prev: any) => ({
      ...prev,
      limit: newSize,
      page:1
    }));
  };
  React.useEffect(() => {
    setSortColumn(query.sortBy);
    setSortDirection(query.sortOrder);
  }, [query]);
  const handleCheck = (id: string) => {
    let newData: string[];

    if (ids.includes(id)) {
      // Remove the id
      newData = ids.filter((item) => item !== id);
    } else {
      // Add the id
      newData = [...ids, id];
    }

    setIds(newData);
    dispatch(setExportId(newData));
  };
  const handleToggleSelectAll = () => {
    if (!tableConfig?.settings?.selectable) return;

    const pageClientIds = filteredAndSortedClients.map(
      (client: any) => client._id
    );

    const allOnPageSelected = pageClientIds.every((id: any) =>
      ids.includes(id)
    );

    let newIds: string[];

    if (allOnPageSelected) {
      newIds = ids.filter((id) => !pageClientIds.includes(id));
    } else {
      newIds = [...new Set([...ids, ...pageClientIds])];
    }
    setIds(newIds);
    dispatch(setExportId(newIds));
  };

  const handleToggleSelect = (id: string) => {
    handleCheck(id);
  };

  const handleEdit = (id: string, action: string) => {
    setId(id);
    setLoader(true);
    if (action === "process" || action === "view") {
      router.push(`/tickets/${id}`);
    } else if (action === `view&allocate` && pathName.includes(`/Source`)) {
      router.push(`/tickets/Source/${id}/view`);
    } else {
      router.push(`/tickets/create/${id}`);
    }
  };

  const handleDelete = (id: string) => {
    setId(id);
    console.log("Delete client:", id);
    setOpenDelete(true);
  };

  const submitDelete = () => {
    setLoader(true);
    deleteUser({ input: { id: _id } })
      .then(async (res) => {
        console.log(res);
        showToast.success(res.deleteClient.data.message);
        await handleGetApi();
        setOpenDelete(false);
        setLoader(false);
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const handleFilterChange = (column: string, values: string[]) => {
    setQuery((prev: any) => {
      const currentFilters =
        typeof prev.filters === "string"
          ? JSON.parse(prev.filters)
          : prev.filters || {};

      const updatedFilters = {
        ...currentFilters,
        [column]: values[0],
      };

      return {
        ...prev,
        filters: updatedFilters,
        page: 1,
      };
    });
  };
  const handleSort = (column: string) => {
    setQuery((prev: any) => ({
      ...prev,
      sortBy: column,
      sortOrder: prev.sortOrder === "asc" ? "desc" : "asc",
    }));
    setSortColumn(column);
    setSortDirection((prev) => (prev === "asc" ? "desc" : "asc"));
  };

  const filteredAndSortedClients = React.useMemo(() => {
    let filtered = clients;

    // Apply filters
    if (tableConfig?.settings?.filterable) {
      Object.entries(filters).forEach(([column, values]) => {
        if (values.length > 0) {
          const columnConfig = tableConfig?.columns?.find(
            (col: { title: string }) => col.title === column
          );
          if (columnConfig) {
            filtered = filtered.filter((client) =>
              values.includes(
                String(client[columnConfig.id as keyof Record<string, string>])
              )
            );
          }
        }
      });
    }
    // Apply sorting
    if (sortColumn && sortDirection) {
      const columnConfig = tableConfig?.columns?.find(
        (col: { title: string }) => col.title === sortColumn
      );
      if (columnConfig?.sortable) {
        filtered = [...filtered].sort((a, b) => {
          const aVal = String(
            a[columnConfig.id as keyof Record<string, string>]
          ).toLowerCase();
          const bVal = String(
            b[columnConfig.id as keyof Record<string, string>]
          ).toLowerCase();

          if (sortDirection === "asc") {
            return aVal.localeCompare(bVal);
          } else {
            return bVal.localeCompare(aVal);
          }
        });
      }
    }

    return filtered;
  }, [clients, filters, sortColumn, sortDirection, tableConfig]);

  const allSelected =
    tableConfig?.settings?.selectable &&
    filteredAndSortedClients?.length > 0 &&
    filteredAndSortedClients?.every((client: any) => ids.includes(client._id));

  return (
    <main className="overflow-hidden mx-auto my-0 w-full bg-white rounded-xl max-w-[1900px] shadow-[0_1px_3px_rgba(0,0,0,0.1)]">
      {/* <div className="flex justify-end px-4">
       
      </div> */}
      <hr className="border-slate-100" />
      <div className="border border-slate-100 rounded-lg">
        <p className="px-4 mt-2 my-4 text-md font-semibold text-[#1465AB] flex justify-between items-center">
          {title}{" "}
          {/* <span>
            {" "}
            <Button
              className="!h-[40px] rounded-[3px] !my-4 !bg-teal-500 p-2 w-[auto] !inset-shadow-none"
              onClick={handleAddClient}
            >
              <span className="text-[14px] mx-2">Add New {title} </span>
            </Button>
          </span> */}
        </p>

        <hr className="mx-0 mt-2 border-slate-100" />
        {tableConfig?.columns?.length !== 0 && hasData ? (
          <>
            <TableHeader
              title={title}
              page={page}
              totalPages={totalPages}
              pageSize={pageSize}
              totalItems={totalItems}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              handleGetApi={handleGetApi}
              query={query}
            />
            <DataTable
              tableConfig={{
                ...tableConfig,
                settings: {
                  ...tableConfig?.settings,
                  defaultSortDirection: query.sortOrder as "asc" | "desc",
                },
              }}
              clients={filteredAndSortedClients?.map((client: any) => ({
                ...client,
                isSelected: ids.includes(client.id),
              }))}
              onToggleSelectAll={handleToggleSelectAll}
              onToggleSelect={handleToggleSelect}
              onEdit={handleEdit}
              onDelete={handleDelete}
              allSelected={allSelected}
              filters={filters}
              onFilterChange={handleFilterChange}
              onSort={handleSort}
              sortColumn={sortColumn}
              sortDirection={sortDirection}
              title={title}
              fieldTypes={fieldTypes}
              handleGetApi={handleGetApi}
            />
          </>
        ) : (
          <div className="flex flex-col items-center justify-center py-12">
            <div className="text-gray-400 mb-4">
              <svg
                className="w-16 h-16 mx-auto"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <h5 className="text-lg font-semibold text-gray-600 mb-2">
              No Data Found
            </h5>
            <p className="text-sm text-gray-500 text-center max-w-md">
              {!hasData
                ? "There are currently no records available. Please check back later or contact your administrator."
                : "No records match your current search criteria. Try adjusting your filters or search terms."}
            </p>
          </div>
        )}
        {/* <Pagination
          page={page}
          totalPages={totalPages}
          pageSize={pageSize}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
        /> */}
      </div>
      {openDelete && (
        <DeleteModal
          isOpen={openDelete}
          onClose={handleCloseDelete}
          onDelete={submitDelete}
        />
      )}
      {loader && <Loader />}
    </main>
  );
}

export default ClientDataTable;
