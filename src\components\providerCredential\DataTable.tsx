"use client";
import * as React from "react";
import { TableColumnHeader } from "./TableColumnHeader";
import { TableRow } from "./TableRow";
import { TableConfig } from "@/types/user";

interface DataTableProps {
  tableConfig: TableConfig;
  clients: Record<string, string>[];
  onToggleSelectAll?: () => void;
  onToggleSelect?: (id: string) => void;
  onEdit?: (id: string, action: string) => void;
  onDelete?: (id: string) => void;
  allSelected?: boolean;
  filters?: Record<string, string[]>;
  onFilterChange?: (column: string, values: string[]) => void;
  onSort?: (column: string) => void;
  sortColumn?: string;
  sortDirection?: "asc" | "desc" | null;
  title?: string;
  fieldTypes?: Record<string, string>;
  handleGetApi?: () => void;
}

export function DataTable({
  title,
  tableConfig,
  clients,
  onToggleSelectAll,
  onToggleSelect,
  onEdit,
  onDelete,
  allSelected,
  filters = {},
  onFilterChange,
  onSort,
  sortColumn,
  sortDirection,
  fieldTypes,
  handleGetApi,
}: DataTableProps) {
  return (
    <div
      className={`overflow-x-auto ${tableConfig?.settings?.responsive ? "responsive" : ""}`}
      style={{
        maxHeight: "calc(100vh - 40vh)",
        position: "relative",
        minHeight: "calc(100vh - 40vh)",
      }}
    >
      <table className="small w-full bg-white border-collapse">
        <thead
          style={{ position: "sticky", top: 0, zIndex: 20, background: "#fff" }}
        >
          <tr>
            {/* {tableConfig?.settings?.selectable && ( */}
            <th className="sticky left-0 z-40 p-0 pl-8 font-medium text-left bg-teal-50 border-r border-b border-solid !border-b-slate-100 !border-r-slate-100 max-sm:px-1.5 max-sm:py-2">
              <input
                type="checkbox"
                checked={allSelected}
                onChange={onToggleSelectAll}
                className="w-4 h-4 rounded border border-solid cursor-pointer border-zinc-200"
                aria-label="Select all clients"
              />
              {/* </div> */}
            </th>
            {/* )} */}
            {tableConfig?.columns?.map((column) => (
              // column.visible &&
              <TableColumnHeader
                key={column.id}
                column={column}
                className={
                  column.type === "actions"
                    ? "gap-1.5 p-2.5 h-10 text-sm font-medium tracking-normal text-blue-900 max-sm:px-1.5 max-sm:py-2 max-sm:text-xs"
                    : ""
                }
                selectedFilters={filters[column.title] || []}
                onFilterChange={onFilterChange}
                onSort={onSort}
                sortDirection={
                  sortColumn === column.title ? sortDirection : null
                }
                fieldTypes={fieldTypes}
              />
            ))}
            <th className="sticky right-0 z-10 p-0 pl-8 font-medium align-start text-left bg-teal-50 border-r border-b border-solid !border-b-slate-100 !border-r-slate-100 w-[100px] max-sm:px-1.5 max-sm:py-2">
              <div className="w-[100px] max-sm:pl-4">
                <p className="gap-1.5 p-2.5 h-10 text-sm font-medium tracking-normal text-blue-900 max-sm:px-1.5 max-sm:py-2 max-sm:text-xs">
                  Action
                </p>
                <p style={{ visibility: "hidden" }}>Action</p>
              </div>
            </th>
          </tr>
        </thead>
        <tbody>
          {clients?.length === 0 ? (
            <tr>
              <td colSpan={9} className="truncate p-10 text-center">
                NO RECORDS FOUND
              </td>
            </tr>
          ) : (
            clients?.map((client) => (
              <TableRow
                title={title}
                key={client.id}
                client={client}
                // visibleColumns={tableConfig?.columns
                //  ?.filter((column) => column.visible)
                //   .map((column) => column.id)}
                columnName={tableConfig?.columns?.map((column) => {
                  console.log(column, "column");
                  return column.id;
                })}
                onToggleSelect={
                  tableConfig?.settings?.selectable ? onToggleSelect : undefined
                }
                onEdit={onEdit}
                onDelete={onDelete}
                showSelect={tableConfig?.settings.selectable}
                handleGetApi={handleGetApi}
              />
            ))
          )}
        </tbody>
      </table>
    </div>
  );
}
