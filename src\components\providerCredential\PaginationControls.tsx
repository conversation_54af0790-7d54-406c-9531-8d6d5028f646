"use client";
import { ChevronLeft, ChevronRight } from "lucide-react";
import React from "react";

interface PaginationProps {
  page: number;
  totalPages: number;
  pageSize: number;
  // totalItems?: number;
  pageSizes?: number[];
  onPageChange: (newPage: number) => void;
  onPageSizeChange: (newSize: number) => void;
}

export default function Pagination({
  page,
  totalPages,
  pageSize,
  // totalItems,
  pageSizes = [10, 25, 50, 100],
  onPageChange,
  onPageSizeChange,
}: PaginationProps) {
  const [inputValue, setInputValue] = React.useState(page);

  React.useEffect(() => {
    setInputValue(page); // keep input in sync with external changes
  }, [page]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Only allow changes via up/down arrows, not manual typing
    const newValue = Number(e.target.value);
    if (newValue >= 1 && newValue <= totalPages) {
      setInputValue(newValue);
      onPageChange(newValue);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Prevent manual typing, only allow arrow keys and navigation keys
    const allowedKeys = ["ArrowUp", "ArrowDown", "Tab", "Enter", "Escape"];
    if (!allowedKeys.includes(e.key)) {
      e.preventDefault();
    }
  };

  const handleInputBlur = () => {
    if (inputValue < 1 || inputValue > totalPages || isNaN(inputValue)) {
      setInputValue(page); // reset to current if invalid
    }
  };

  return (
    <div className="flex items-center justify-end p-2 text-sm text-slate-400">
      {/* Per Page Dropdown */}
      <div className="flex items-center gap-2">
        <span>Per Page</span>
        <select
          value={pageSize}
          onChange={(e) => onPageSizeChange(Number(e.target.value))}
          className="border border-gray-300 rounded px-2 py-1 text-gray-600 focus:outline-none focus:ring-1 focus:ring-blue-400"
        >
          {pageSizes.map((size) => (
            <option key={size} value={size}>
              {size}
            </option>
          ))}
        </select>
      </div>

      {/* Pagination Controls */}
      <div className="flex items-center gap-2 ml-4">
        <button
          disabled={page <= 1}
          onClick={() => onPageChange(page - 1)}
          className="disabled:text-gray-300"
          aria-label="Previous page"
        >
          <ChevronLeft size={20} />
        </button>

        <input
          type="number"
          min={1}
          max={totalPages}
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onBlur={handleInputBlur}
          className="w-12 text-center border border-gray-300 rounded px-1 py-0.5 text-gray-600 focus:outline-none"
          style={{
            MozAppearance: "textfield",
            WebkitAppearance: "none",
          }}
        />

        <span className="text-slate-500">of {totalPages}</span>

        <button
          disabled={page >= totalPages}
          onClick={() => onPageChange(page + 1)}
          className="disabled:text-gray-300"
          aria-label="Next page"
        >
          <ChevronRight size={20} />
        </button>
      </div>
    </div>
  );
}
