"use client";
import { toNormalCase } from "@/utils/generic";
import * as React from "react";
import debounce from "lodash/debounce";
import { useMemo, useState } from "react";

interface Column {
  id: string;
  title: string;
  width?: string;
  hasFilter?: boolean;
  type: string;
  filterType?: string;
  placeholder?: string;
  options?: string[];
}

interface TableColumnHeaderProps {
  column: Column;
  className?: string;
  selectedFilters: string[];
  onFilterChange?: (columnId: string, values: string[]) => void;
  onSort?: (columnId: string) => void;
  sortDirection?: "asc" | "desc" | null;
  fieldTypes?: Record<string, string>;
}

export function TableColumnHeader({
  column,
  onSort,
  onFilterChange,
  sortDirection,
  fieldTypes,
}: TableColumnHeaderProps) {
  const [dateValue, setDateValue] = useState("");
  const [selectValue, setSelectValue] = useState("");
  const handleSort = () => {
    if (onSort) {
      onSort(column.id);
    }
  };

  const handleTextFilterChange = useMemo(
    () =>
      debounce((value: string) => {
        onFilterChange?.(column.id, [value]);
      }, 700),
    [column.id, onFilterChange]
  );

  const handleDateChange = (value: string) => {
    setDateValue(value);
    onFilterChange?.(column.id, [value]);
  };

  const handleSelectChange = (value: string) => {
    setSelectValue(value);
    onFilterChange?.(column.id, [value]);
  };

  // Get field type from fieldTypes prop or fallback to detection logic
  const getFieldType = () => {
    if (fieldTypes) {
      // Try different variations of the column identifier
      const fieldType =
        fieldTypes[column.id] ||
        fieldTypes[column.title] ||
        fieldTypes[column.id.toLowerCase()] ||
        fieldTypes[column.title.toLowerCase()] ||
        fieldTypes[column.id.toLowerCase().replace(/\s+/g, "_")] ||
        fieldTypes[column.title.toLowerCase().replace(/\s+/g, "_")];

      if (fieldType) return fieldType;
    }

    // Fallback to detection logic
    if (
      column.id.toLowerCase().includes("date") ||
      column.id.toLowerCase().includes("created_at") ||
      column.id.toLowerCase().includes("updated_at")
    ) {
      return "date";
    }

    // Remove status field special handling - treat as text field
    // if (column.id.toLowerCase().includes("status")) {
    //   return "select";
    // }

    return "text";
  };

  const fieldType = getFieldType();
  const isDateField = fieldType === "date";
  const isStatusField = fieldType === "select"; // Remove status field special handling

  // Status options - can be expanded based on field configuration
  const getStatusOptions = () => {
    if (column.id.toLowerCase().includes("status")) {
      return [
        { value: "", label: "All" },
        { value: "0", label: "New" },
        { value: "1", label: "Allocated" },
        { value: "2", label: "Progress" },
        { value: "3", label: "Exception" },
        { value: "4", label: "Reallocate" },
        { value: "5", label: "Completed" },
      ];
    }

    if (column.id.toLowerCase().includes("priority")) {
      return [
        { value: "", label: "All" },
        { value: "high", label: "High" },
        { value: "medium", label: "Medium" },
        { value: "low", label: "Low" },
      ];
    }

    // Default options for other select fields
    return [
      { value: "", label: "All" },
      { value: "active", label: "Active" },
      { value: "inactive", label: "Inactive" },
    ];
  };

  const statusOptions = getStatusOptions();

  console.log("fieldType for", column.id, ":", fieldType);
  console.log("fieldTypes available:", fieldTypes);

  return (
    <th
      className={`sticky z-10 width-[20px] p-2.5 font-medium text-left bg-[#F0FCFC] border-r border-b border-solid !border-b-slate-100 !border-r-slate-100 ${
        column.id === "first-column-id" ? "left-0" : ""
      }`}
      style={{ width: column.width }}
    >
      <div className="flex flex-col gap-2" style={{ width: "10rem" }}>
        <div className="text-[15px] text-[#273B98] cursor-pointer select-none font-medium">
          <span>
            {toNormalCase(column.title)}
            {!column.title.includes("Image") && (
              <span className="ml-1 text-[black]" onClick={handleSort}>
                {sortDirection === "asc" ? "↓" : "↑"}
              </span>
            )}
          </span>
        </div>

        {isDateField ? (
          <input
            type="date"
            value={dateValue}
            onChange={(e) => handleDateChange(e.target.value)}
            className="border border-gray-300 bg-white rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-teal-500"
          />
        ) : isStatusField ? (
          <select
            value={selectValue}
            onChange={(e) => handleSelectChange(e.target.value)}
            className="border border-gray-300 bg-white rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-teal-500"
          >
            {statusOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        ) : (
          <input
            type="text"
            placeholder={column.placeholder}
            onChange={(e) => handleTextFilterChange(e.target.value)}
            className="border border-gray-300 bg-white rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-teal-500"
          />
        )}
      </div>
    </th>
  );
}
