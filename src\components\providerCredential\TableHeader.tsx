/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import * as React from "react";
import Pagination from "./PaginationControls";
import Image from "next/image";
import settings from "../../assests/settings.svg";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import {
  createView,
  deleteView,
  getAllView,
  getView,
  UpdateView,
} from "@/api/header/header";
import { setHeaders } from "@/features/headers/headersSlice";
import { toNormalCase } from "@/utils/generic";
import { MenuItem, Select } from "@mui/material";
import Loader from "@/components/loader/loader";
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd";
import { showToast } from "@/components/toaster/ToastProvider";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import { exportTableData } from "@/api/exportAction/export";
import { clearExportId } from "@/features/export/exportSlice";
import { allocateClient } from "@/api/ProviderCredentials/provider";
import { BulkAllocationPopup } from "./bulkAllocationPopup";
import PermissionBasedActions from "@/components/permissions/PermissionBasedActions";
const dummyUsers = [
  {
    id: "1",
    name: "Priya",
    totalTickets: 120,
    allocated: 70,
    available: 50,
    totalAllocatedBatches: 10,
  },
  {
    id: "2",
    name: "Ravi",
    totalTickets: 120,
    allocated: 50,
    available: 70,
    totalAllocatedBatches: 10,
  },
  {
    id: "3",
    name: "Uma",
    totalTickets: 100,
    allocated: 50,
    available: 50,
    totalAllocatedBatches: 5,
  },
  // Add more users as needed
];

import Cookies from "js-cookie";
export function TableHeader({
  page,
  totalPages,
  pageSize,
  totalItems,
  onPageChange,
  handleGetApi,
  onPageSizeChange,
  query,
  title,
}: {
  page: number;
  title: string;
  totalPages: number;
  pageSize: number;
  totalItems?: number;
  onPageChange: (newPage: number) => void;
  onPageSizeChange: (newSize: number) => void;
  query?: any;
  handleGetApi?: any;
  setPagination?: any;
}) {
  const dispatch = useDispatch();
  const header = useSelector((state: RootState) => state.headers);
  const headerDefault = useSelector((state: RootState) => state.headersDefault);
  const id = useSelector((state: RootState) => state.user.id);
  const [showColumnSelector, setShowColumnSelector] = React.useState(false);
  const [show, setShow] = React.useState(false);
  const [input, setInput] = React.useState("");
  const [loader, setLoader] = React.useState(false);
  const [visibleTitles, setVisibleTitles] = React.useState<string[]>([]);
  const [isEditing, setIsEditing] = React.useState(false);
  const [editingViewId, setEditingViewId] = React.useState<string | null>(null);
  const [selectedAction] = React.useState("");
  const exportId = useSelector((state: RootState) => state.exportId.exportId);
  const [isAllocationPopupOpen, setIsAllocationPopupOpen] =
    React.useState(false);
  const [allocationAction, setAllocationAction] = React.useState<
    "allocate" | "reallocate"
  >("allocate");
  const [, setBulkAllocating] = React.useState(false);
  const [updatedColumns, setUpdatedColumns] = React.useState<
    { title: string; visible: boolean }[]
  >([]);
  const [options, setOptions] = React.useState<
    { label: string; value: string; grid_fields?: any }[]
  >([]);
  const [view, setView] = React.useState("Default");
  const selectorRef = React.useRef<HTMLDivElement>(null);
  const orgId = Cookies.get("orgId");
  // const getCurrentVisibleTitles = () =>
  //   updatedColumns.filter((col) => col.visible).map((col) => col.title);
  const handleChangeView = (value: string) => {
    localStorage.setItem("lastSelectedView", value); // Save to localStorage
    if (value !== "Default" && value !== "add") {
      const payload = {
        id: value,
      };

      setLoader(true);
      getView(payload).then((res) => {
        console.log(
          "res.gridTemplate",
          res.gridTemplate.data.gridTemplate.grid_fields
        );
        dispatch(setHeaders(res.gridTemplate.data.gridTemplate.grid_fields));
        // dispatch(setTitles(res.gridTemplate.data.gridTemplate.grid_fields))
        handlegetData(false);
      });
    } else if (value == "add") {
      setShow(true);
      setShowColumnSelector(true);
    } else {
      handlegetData(true);
    }
    setLoader(false);
  };
  const getViewAll = () => {
    getAllView({
      type: title === "Organizations" ? "organizations" : "subOrganizations",
    })
      .then((res) => {
        setOptions(
          res.gridTemplates.data.data.gridTemplates.map((item: any) => {
            return {
              value: item._id,
              label: item.name,
              grid_fields: item.grid_fields,
            };
          })
        );
      })
      .catch((err) => {
        console.error(err);
      });
  };
  const handlegetData = (isDefault?: boolean) => {
    const payloads = {
      input: { ...query },
    };
    console.log("isDefault", isDefault);

    if (isDefault) {
      handleGetApi();
    } else {
      handleGetApi(payloads);
    }
  };
  function mergeHeaders(
    headerDefault: any[],
    header: any[]
  ): { title: string; visible: boolean }[] {
    const parseTitle = (item: any): string => {
      if (typeof item === "string") return item;
      if (typeof item === "object" && item !== null) {
        return Object.entries(item)
          .filter(([k]) => !isNaN(Number(k)))
          .sort(([a], [b]) => Number(a) - Number(b))
          .map(([, v]) => v)
          .join("");
      }
      return "";
    };

    const headerTitles = new Set(header.map(parseTitle));

    return headerDefault.map((item) => {
      const title = parseTitle(item);
      return {
        title,
        visible: headerTitles.has(title),
      };
    });
  }
  React.useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        selectorRef.current &&
        !selectorRef.current.contains(event.target as Node)
      ) {
        setShowColumnSelector(false);
      }
    }

    if (showColumnSelector) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showColumnSelector, setShowColumnSelector]);
  const handlePatched = () => {
    const merged = mergeHeaders(headerDefault, header);
    console.log("merged", merged);

    setUpdatedColumns(merged);
  };

  React.useEffect(() => {
    handlePatched();
  }, [headerDefault, header]);

  React.useEffect(() => {
    getViewAll();
  }, []);
  React.useEffect(() => {
    const lastView = localStorage.getItem("lastSelectedView");
    if (lastView && lastView !== view) {
      setView(lastView);
      handleChangeView(lastView);
    }
  }, []);
  const getVisibleColumnTitles = (input: any[]): string[] => {
    return input.reduce((acc: string[], item: any) => {
      if (typeof item === "string") {
        acc.push(item); // assume all strings are visible
      } else if (typeof item === "object" && item?.visible === true) {
        // Find the first key that is not 'visible'
        const key = Object.keys(item).find((k) => k !== "visible");
        if (key) acc.push(item[key]);
      }
      return acc;
    }, []);
  };
  const handleSubmit = () => {
    setVisibleTitles(getVisibleColumnTitles(updatedColumns));
    const visibleCount = getVisibleColumnTitles(updatedColumns);

    dispatch(setHeaders(visibleCount));
    // dispatch(setTitles(res.gridTemplate.data.gridTemplate.grid_fields))
    handlegetData(false);
    setShow(false);
    setShowColumnSelector(false);
  };
  /**
   * Toggle the visibility of the column with the given id.
   * @param {string} id - The id of the column to toggle.
   */ const toggleColumnVisibility = (index: number) => {
    const updated = updatedColumns.map((col, i) =>
      i === index ? { ...col, visible: !col.visible } : col
    );
    setUpdatedColumns(updated);
  };
  const visibleCount = updatedColumns.filter((col) => col.visible).length;
  const totalCount = updatedColumns.length;
  const handleSave = () => {
    const payload = {
      input: {
        name: input,
        grid_fields: visibleTitles,
        user_id: id,
        type: title === "Organizations" ? "organizations" : "subOrganizations",
      },
    };
    const updatepayload = {
      input: {
        id: editingViewId ?? "",
        name: input,
        // grid_fields: visibleTitles,
        user_id: id,
        type: title === "Organizations" ? "organizations" : "subOrganizations",
      },
    };
    setLoader(true);
    if (isEditing && editingViewId) {
      UpdateView(updatepayload)
        .then(async () => {
          setShow(false);
          setShowColumnSelector(false);
          setIsEditing(false);
          setEditingViewId(null);
          setInput("");
          await getViewAll();
          setLoader(false);
        })
        .catch((err) => {
          setLoader(false);
          showToast.error((err as { message: string }).message);
        });
    } else {
      createView(payload)
        .then(async (res) => {
          setShow(false);
          setShowColumnSelector(false);
          setIsEditing(false);
          setEditingViewId(null);
          setInput("");
          await getViewAll();
          setView(res.createGridTemplate.data.gridTemplate._id);
          setLoader(false);
        })
        .catch((err) => {
          setLoader(false);
          showToast.error((err as { message: string }).message);
        });
    }
    setShowColumnSelector(false);
  };
  const handleReset = () => {
    setUpdatedColumns(
      headerDefault.map((item: any) => {
        let title = "";
        if (typeof item === "string") {
          title = item;
        } else if (typeof item === "object") {
          title = Object.entries(item)
            .filter(([key]) => !isNaN(Number(key)))
            .sort(([a], [b]) => Number(a) - Number(b))
            .map(([, val]) => val)
            .join("");
        }
        return {
          title,
          visible: true,
        };
      })
    );
    setVisibleTitles(
      headerDefault.map((item: any) => {
        if (typeof item === "string") return item;
        if (typeof item === "object") {
          return Object.entries(item)
            .filter(([key]) => !isNaN(Number(key)))
            .sort(([a], [b]) => Number(a) - Number(b))
            .map(([, val]) => val)
            .join("");
        }
        return "";
      })
    );
    dispatch(
      setHeaders(
        headerDefault.map((item: any) => {
          if (typeof item === "string") return item;
          if (typeof item === "object") {
            return Object.entries(item)
              .filter(([key]) => !isNaN(Number(key)))
              .sort(([a], [b]) => Number(a) - Number(b))
              .map(([, val]) => val)
              .join("");
          }
          return "";
        })
      )
    );
  };
  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const items = Array.from(updatedColumns);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    setUpdatedColumns(items);
  };

  const handleEditView = (viewId: string) => {
    setView(viewId);
    setShow(true);
    setShowColumnSelector(true);
    setIsEditing(true);
    setEditingViewId(viewId);

    const viewToEdit = options.find((opt: any) => opt.value === viewId);
    setInput(viewToEdit ? viewToEdit.label : "");
  };
  type User = { id: string; name: string };

  const handleBulkAllocation = async (users: User[]) => {
    if (exportId.length === 0) {
      showToast.error("No items selected for allocation");
      return;
    }

    setBulkAllocating(true);
    let successCount = 0;
    let errorCount = 0;

    try {
      for (const user of users) {
        for (const clientId of exportId) {
          try {
            await allocateClient({
              input: {
                id: clientId,
                assignedTo: user.id,
              },
            });
            successCount++;
          } catch (error) {
            errorCount++;
            console.error(
              ` Failed to ${allocationAction} client ${clientId}:`,
              error
            );
          }
        }
        // Show results for each user
        if (successCount > 0 && errorCount === 0) {
          showToast.success(
            `Successfully ${allocationAction}d ${successCount} item${successCount > 1 ? "s" : ""} to ${user.name}`
          );
        } else if (successCount > 0 && errorCount > 0) {
          showToast.error(
            `${allocationAction}d ${successCount} item${successCount > 1 ? "s" : ""} successfully, ${errorCount} failed`
          );
        } else {
          showToast.error(`Failed to ${allocationAction} all selected items`);
        }
      }

      // Clear selections and close popup
      dispatch(clearExportId());
      setIsAllocationPopupOpen(false);

      // Refresh the data
      if (handleGetApi) {
        handleGetApi();
      }
    } catch (error) {
      console.error("Bulk allocation error:", error);
      showToast.error(`Failed to ${allocationAction} selected items`);
    } finally {
      setBulkAllocating(false);
    }
  };

  const handleChangeActions = async (value: string) => {
    // Handle allocation actions
    if (value === "Allocate") {
      if (exportId.length === 0) {
        showToast.error("Please select items to allocate");
        return;
      }
      setAllocationAction("allocate");
      setIsAllocationPopupOpen(true);
      return;
    }

    if (value === "Reallocate") {
      if (exportId.length === 0) {
        showToast.error("Please select items to reallocate");
        return;
      }
      setAllocationAction("reallocate");
      setIsAllocationPopupOpen(true);
      return;
    }

    // Handle export actions
    const payload = {
      input: {
        selectedRow: exportId,
        collection: "tickets",
        fields: headerDefault,
        filters: query.filters,
        createdBy: id,
        orgId: orgId,
        sortOrder: query.sortOrder,
        sortBy: query.sortBy,
        fileType: value.includes("CSV")
          ? "csv"
          : value.includes("Excel")
            ? "xlsx"
            : "json",
      },
    };
    await exportTableData(payload)
      .then((res) => {
        console.log(res);
        showToast.success(res.startExport.message);
        dispatch(clearExportId());
      })
      .catch((err) => {
        console.error(err);
      });
  };
  return (
    <header className="flex flex-col gap-2 bg-white border-b border-solid border-b-slate-100 px-4 py-1">
      {/* Tabs Row */}
      {/* Controls Row */}
      <div className="flex gap-5 items-center justify-between flex-wrap">
        <div className="flex gap-5 items-center max-md:justify-between max-sm:flex-col max-sm:gap-2.5">
          <PermissionBasedActions
            selectedAction={selectedAction}
            onActionChange={handleChangeActions}
          />
          {/* Total Items Display */}
          {totalItems && (
            <span className="text-slate-500 ml-2 text-sm">
              {totalItems} Records Found
            </span>
          )}
        </div>
        <div className="flex gap-5 items-center flex-wrap">
          <Select
            value={view}
            renderValue={(selected) => {
              if (selected === "Default") return "Default";
              const opt = options.find((o) => o.value === selected);
              return opt ? opt.label : selected;
            }}
            onChange={(e) => {
              const selectedValue = e.target.value;
              if (selectedValue === "__add_new_view__") {
                setShow(true);
                setShowColumnSelector(true);
                setIsEditing(false);
                setEditingViewId(null);
                setInput("");
                return;
              }
              setView(selectedValue);
              handleChangeView(selectedValue);
            }}
            sx={{
              "& .MuiSelect-select": {
                padding: "10px",
              },
            }}
            style={{ color: "#6B7280" }}
            className="w-[150px] !text-xs !text-gray-500"
          >
            <MenuItem value={"Default"} className="!text-gray-500 !text-xs">
              Default
            </MenuItem>
            {options.map((opt: { label: string; value: string }) => (
              <MenuItem
                key={opt.value}
                value={opt.value}
                className="!text-gray-500 !text-xs flex justify-between items-center"
                sx={{ justifyContent: "space-between" }}
              >
                <span>{opt.label}</span>
                <span
                  className="ml-2 cursor-pointer "
                  title="Edit View"
                  onClick={() => {
                    handleEditView(opt.value);
                  }}
                >
                  <EditIcon sx={{ fontSize: "15px" }} />
                </span>
              </MenuItem>
            ))}
            <MenuItem
              value="__add_new_view__"
              className="!font-bold !text-gray-700 !text-xs"
            >
              + Add View
            </MenuItem>
          </Select>
          <button
            className="flex gap-2.5 items-center px-4 py-2 bg-violet-50 rounded cursor-pointer"
            onClick={() => {
              setShowColumnSelector((prev) => !prev);
              setShow(false);
              setIsEditing(false);
              setEditingViewId(null);
              setInput("");
            }}
          >
            <Image src={settings} alt="settings" />
            <span className="text-xs font-medium !text-gray-500">Columns</span>
          </button>
          <Pagination
            page={page}
            totalPages={totalPages}
            pageSize={pageSize}
            // totalItems={totalItems}
            onPageChange={onPageChange}
            onPageSizeChange={onPageSizeChange}
          />
        </div>
      </div>

      {showColumnSelector && (
        <div
          ref={selectorRef}
          className="absolute right-[90px] top-[340px] z-50 bg-white rounded shadow-lg p-4 w-[300px] max-h-[500px] "
        >
          {show ? (
            <div className="!mb-4">
              <label className="block text-sm font-medium text-gray-700">
                View Name
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="text"
                  placeholder=""
                  className="px-4 py-2 border border-gray-300 rounded flex-1"
                  value={input}
                  onChange={(e) => {
                    setInput(e.target.value);
                  }}
                />
                {isEditing && (
                  <button
                    type="button"
                    className="ml-2 text-red-500"
                    title="Delete View"
                    onClick={async () => {
                      if (!editingViewId) return;
                      setLoader(true);
                      try {
                        await deleteView({ id: editingViewId });
                        setShow(false);
                        setShowColumnSelector(false);
                        setIsEditing(false);
                        setEditingViewId(null);
                        setInput("");
                        await getViewAll();
                        showToast.success("View deleted successfully");
                        // If the deleted view was the current view, switch to Default
                        if (view === editingViewId) {
                          setView("Default");
                          handleChangeView("Default");
                        }
                      } catch (err: any) {
                        showToast.error(
                          err?.message || "Failed to delete view"
                        );
                      }
                      setLoader(false);
                    }}
                  >
                    <DeleteIcon />
                  </button>
                )}
              </div>
              <div className="flex justify-between mt-4">
                <button
                  className="px-4 py-2 rounded bg-gray-400 text-white"
                  onClick={() => {
                    setShowColumnSelector(false);
                    setShow(false);
                    setInput("");
                    setIsEditing(false);
                    setEditingViewId(null);
                  }}
                >
                  Cancel
                </button>
                <button
                  className="px-4 py-2 rounded bg-teal-500 text-white"
                  onClick={() => handleSave()}
                  disabled={!input.trim()}
                >
                  {isEditing ? "Update" : "Create"}
                </button>
              </div>
            </div>
          ) : (
            <>
              <div className="flex justify-between items-center mb-4">
                <p className="text-sm text-gray-500">
                  {visibleCount} out of {totalCount} Selected
                </p>
                <button
                  type="button"
                  className="text-gray-700"
                  onClick={handleReset}
                >
                  Reset
                </button>
              </div>
              <div className="flex items-center mb-2">
                <input
                  type="checkbox"
                  checked={visibleCount === totalCount}
                  ref={(el) => {
                    if (el) {
                      el.indeterminate =
                        visibleCount > 0 && visibleCount < totalCount;
                    }
                  }}
                  onChange={(e) => {
                    const checked = e.target.checked;
                    setUpdatedColumns(
                      updatedColumns.map((col) => ({
                        ...col,
                        visible: checked,
                      }))
                    );
                  }}
                  className="mr-2"
                  id="select-all-columns"
                />
                <label
                  htmlFor="select-all-columns"
                  className="text-sm text-gray-700 font-medium"
                >
                  Select All
                </label>
              </div>
              {/* --- End Select All Checkbox --- */}
              <hr className="border border-gray-100" />

              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="columns">
                  {(provided) => (
                    <div
                      {...provided.droppableProps}
                      ref={provided.innerRef}
                      className="flex flex-col gap-2 my-4 overflow-y-auto max-h-[300px] overflow-y-auto"
                    >
                      {updatedColumns.map((col, i) => (
                        <Draggable
                          key={col.title}
                          draggableId={col.title}
                          index={i}
                        >
                          {(provided) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              // Remove dragHandleProps from the container
                              className={`px-3 py-2 rounded text-sm flex items-center justify-between ${
                                col.visible
                                  ? "bg-teal-50 text-gray-500"
                                  : "border border-gray-300 text-gray-600"
                              }`}
                            >
                              <div className="flex items-center">
                                {/* Drag handle icon */}
                                <span
                                  {...provided.dragHandleProps}
                                  className="cursor-grab mr-2 flex items-center"
                                  title="Drag to reorder"
                                >
                                  <svg
                                    width="18"
                                    height="18"
                                    viewBox="0 0 20 20"
                                    fill="none"
                                  >
                                    <rect
                                      x="3"
                                      y="6"
                                      width="14"
                                      height="2"
                                      rx="1"
                                      fill="#6B7280"
                                    />
                                    <rect
                                      x="3"
                                      y="9"
                                      width="14"
                                      height="2"
                                      rx="1"
                                      fill="#6B7280"
                                    />
                                    <rect
                                      x="3"
                                      y="12"
                                      width="14"
                                      height="2"
                                      rx="1"
                                      fill="#6B7280"
                                    />
                                  </svg>
                                </span>
                                {toNormalCase(col.title)}
                              </div>
                              <input
                                type="checkbox"
                                checked={col.visible}
                                onChange={() => toggleColumnVisibility(i)}
                                className="ml-2"
                                onClick={(e) => e.stopPropagation()}
                              />
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>

              <div className="flex justify-between">
                <button
                  className="px-4 py-2 rounded bg-gray-400 text-white"
                  onClick={() => {
                    setShowColumnSelector(false);
                  }}
                >
                  Cancel
                </button>
                <button
                  className="px-4 py-2 rounded bg-teal-500 text-white"
                  onClick={() => handleSubmit()}
                >
                  Save & Close
                </button>
              </div>
            </>
          )}
        </div>
      )}
      {loader && <Loader />}

      {/* Bulk Allocation Popup */}
      <BulkAllocationPopup
        isOpen={isAllocationPopupOpen}
        onClose={() => setIsAllocationPopupOpen(false)}
        onAllocate={handleBulkAllocation}
        title="Bulk Allocation"
        clientId="someClientId"
        users={dummyUsers} // Pass the users dynamically
      />
    </header>
  );
}
