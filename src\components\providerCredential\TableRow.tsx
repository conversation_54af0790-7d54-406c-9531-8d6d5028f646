"use client";

import Image from "next/image";
import * as React from "react";
import { AllocationPopup } from "./AllocationPopup";
import { useSelector } from "react-redux";
import type { RootState } from "../../store";
import { Box } from "@mui/material";
import { formatDateToDDMMYYYY } from "@/utils/generic";
import { TicketPermissionWrapper } from "@/components/permissions/PermissionWrapper";

interface TableRowProps {
  client: Record<string, string>;
  onToggleSelect?: (id: string) => void;
  onEdit?: (id: string, action: string) => void;
  onDelete?: (id: string) => void;
  visibleColumns?: string[];
  columnName: string[];
  showSelect?: boolean;
  title?: string;
  handleGetApi?: () => void;
}

export function TableRow({
  title,
  client,
  onToggleSelect,
  onEdit,
  columnName,
  showSelect = true,
  handleGetApi,
}: TableRowProps) {
  const [isAllocationPopupOpen, setIsAllocationPopupOpen] =
    React.useState(false);
  const [allocationAction, setAllocationAction] = React.useState<
    "allocate" | "re-allocate"
  >("allocate");
  const exportId = useSelector((state: RootState) => state.exportId.exportId);

  // Get current user info and role
  const roleacess = useSelector((state: RootState) => state as RootState);
  const currentUserid = roleacess?.user?.id || "";
  const userRole = roleacess?.roleProcess?.role?.name?.toLowerCase() || "";

  // Check if user is manager or supervisor
  const isManagerOrSupervisor =
    userRole.includes("manager") || userRole.includes("supervisor");
  console.log("client~~~~~~~~~~~~~~", client);

  // Get client allocation status and assigned email
  const isAllocated = client?.assign;
  const assignedToid = client.assignedTo || "";

  // Button visibility and state logic
  const canShowAllocateButtons = isManagerOrSupervisor;
  const canAllocate = !isAllocated;
  const canReallocate = isAllocated;
  const canProcess = assignedToid === currentUserid;

  const handleAllocationClick = (action: "allocate" | "re-allocate") => {
    setAllocationAction(action);
    setIsAllocationPopupOpen(true);
  };

  const typeLabels: Record<number, string> = {
    1: "Email",
    2: "Ticket",
    3: "Import",
  };
  const statusLabels: Record<number, string> = {
    0: "New",
    1: "Allocated",
    2: "Progress",
    3: "Exception",
    4: "Reallocate",
    5: "Completed",
  };
  const handleAllocation = (userId: string, userName: string) => {
    console.log(
      `${allocationAction} client ${client._id} to user ${userId} (${userName})`
    );

    setIsAllocationPopupOpen(false);
  };

  // Function to get priority color
  const getPriorityColor = (priority: string) => {
    if (!priority) return "#41C065";

    const priorityLower = priority.toLowerCase();
    switch (priorityLower) {
      case "high":
      case "urgent":
      case "critical":
        return "#CC3030";
      case "medium":
      case "normal":
      case "standard":
        return "#F4DE12";
      default:
        return "#41C065";
    }
  };

  // Function to render priority with color indicator
  const renderPriorityCell = (value: string) => {
    const color = getPriorityColor(value);

    // If no priority value, show only the color box (gray) without text
    if (!value) {
      return (
        <Box display="flex" alignItems="center" gap={1}>
          <Box
            sx={{
              width: 12,
              height: 12,
              backgroundColor: color,
              borderRadius: 1,
              flexShrink: 0,
            }}
          />
        </Box>
      );
    }

    // If priority value exists, show color box with text
    return (
      <Box display="flex" alignItems="center" gap={1}>
        <Box
          sx={{
            width: 12,
            height: 12,
            backgroundColor: color,
            borderRadius: 1,
            flexShrink: 0,
          }}
        />
        {/* <span>{value}</span> */}
      </Box>
    );
  };

  return (
    <tr className="table-row hover:bg-gray-50 transition-colors duration-150">
      {showSelect && (
        <td className="sticky left-0 z-15 p-2.5 font-medium text-left pl-8 align-middle bg-white border-r border-b border-solid !border-b-slate-100 !border-r-slate-100 w-[84px] max-sm:px-1.5 max-sm:py-2 max-sm:pl-4">
          <input
            type="checkbox"
            checked={exportId.includes(client._id)}
            onChange={() => onToggleSelect?.(client._id)}
            className="w-4 h-4 rounded border-2 border-solid cursor-pointer border-zinc-200"
          />
        </td>
      )}
      {columnName?.map((item, i) =>
        item === "actions" ? null : (
          <td
            key={i}
            className="sticky right-0 z-10 text-gray-700 align-middle bg-white border-r border-b border-solid !border-b-slate-100 !border-r-slate-100 max-sm:px-1.5 max-sm:py-2 max-sm:text-xs"
          >
            {item === "profileImage" ? (
              <div className="flex justify-center items-center">
                <Image
                  alt={`${client.item} profile`}
                  src={client[item]}
                  width={30}
                  height={30}
                />
              </div>
            ) : item === "type" ? (
              <div className="p-2 text-sm truncate max-w-[200px]">
                {typeLabels[Number(client[item])] || client[item]}
              </div>
            ) : item.toLowerCase() === "priority" ? (
              <div className="p-2 text-sm">
                {renderPriorityCell(client[item])}
              </div>
            ) : item.toLowerCase() === "status" ? (
              <div className="p-2 text-sm">
                {statusLabels[Number(client[item])] || client[item]}
              </div>
            ) : item === "updatedAt" ? (
              <div className="p-2 text-sm">
                {client[item] ? formatDateToDDMMYYYY(client[item], item) : "-"}
              </div>
            ) : item.toLowerCase().includes("date") ? (
              <div className="p-2 text-sm">
                {client[item] ? formatDateToDDMMYYYY(client[item]) : "-"}
                {/* gfgfg */}
              </div>
            ) : (
              <div className="p-2 text-sm truncate max-w-[200px]">
                {client[item] || "-"}
              </div>
            )}
          </td>
        )
      )}
      {/* Manually add the actions column at the end */}
      {title?.includes("Source") ? (
        <td className="  sticky right-0 z-10 text-gray-700 align-middle bg-white border-r border-b border-solid !border-b-slate-100 !border-r-slate-100 max-sm:px-1.5 max-sm:py-2 max-sm:text-xs">
          <div className="flex gap-2.5 items-center justify-center">
            {/* View & Allocate button - show based on View and Allocate permissions */}
            <TicketPermissionWrapper
              permissionName={
                client["status"] !== "Open" && client["status"] !== "Closed"
                  ? "Allocate"
                  : "View"
              }
            >
              <button
                onClick={
                  onEdit ? () => onEdit(client._id, "view&allocate") : undefined
                }
                className="bg-[#3B82F6] hover:bg-[#2563eb] text-white text-xs font-semibold rounded px-4 py-1.5 transition-colors min-w-[120px]"
              >
                {client["status"] !== "Open" && client["status"] !== "Closed"
                  ? "View & Allocate"
                  : "View"}
              </button>
            </TicketPermissionWrapper>
          </div>
        </td>
      ) : (
        <td className="sticky p-2 right-0 z-10 text-gray-700 align-middle bg-white border-r border-b border-solid !border-b-slate-100 !border-r-slate-100 max-sm:px-1.5 max-sm:py-2 max-sm:text-xs">
          <div className="flex gap-2.5 items-center justify-end">
            {/* Allocate button - only show for manager/supervisor, enable only if not allocated */}
            {canShowAllocateButtons && (
              <TicketPermissionWrapper permissionName="Allocate">
                <button
                  onClick={() => handleAllocationClick("allocate")}
                  disabled={!canAllocate}
                  className={`text-white text-xs font-semibold rounded px-4 py-1.5 transition-colors ${
                    canAllocate
                      ? "bg-[#1AC19D] hover:bg-[#17a885] cursor-pointer"
                      : "bg-gray-400 cursor-not-allowed opacity-50"
                  }`}
                  title={!canAllocate ? "Already allocated" : "Allocate"}
                >
                  Allocate
                </button>
              </TicketPermissionWrapper>
            )}

            {/* Reallocate button - only show for manager/supervisor, enable only if allocated */}
            {canShowAllocateButtons && (
              <TicketPermissionWrapper permissionName="Reallocate">
                <button
                  onClick={() => handleAllocationClick("re-allocate")}
                  disabled={!canReallocate}
                  className={`text-white text-xs font-semibold rounded px-4 py-1.5 transition-colors ${
                    canReallocate
                      ? "bg-[#FD5475] hover:bg-[#e13c5d] cursor-pointer"
                      : "bg-gray-400 cursor-not-allowed opacity-50"
                  }`}
                  title={!canReallocate ? "Not allocated yet" : "Reallocate"}
                >
                  Reallocate
                </button>
              </TicketPermissionWrapper>
            )}

            {/* Process button - only enable if assigned to current user */}
            <TicketPermissionWrapper permissionName="Process">
              <button
                onClick={
                  canProcess && onEdit
                    ? () => onEdit(client._id, "process")
                    : undefined
                }
                disabled={!canProcess}
                className={`text-white text-xs font-semibold rounded px-4 py-1.5 transition-colors ${
                  canProcess
                    ? "bg-[#5277F7] hover:bg-[#395fd6] cursor-pointer"
                    : "bg-gray-400 cursor-not-allowed opacity-50"
                }`}
                title={!canProcess ? "Not assigned to you" : "Process"}
              >
                Process
              </button>
            </TicketPermissionWrapper>

            {/* View button - always show if user has View permission */}
            <TicketPermissionWrapper permissionName="View">
              <button
                onClick={onEdit ? () => onEdit(client._id, "view") : undefined}
                className="bg-[#3B82F6] hover:bg-[#2563eb] text-white text-xs font-semibold rounded px-4 py-1.5 transition-colors"
              >
                View
              </button>
            </TicketPermissionWrapper>
          </div>
        </td>
      )}

      {/* Allocation Popup */}
      <AllocationPopup
        isOpen={isAllocationPopupOpen}
        onClose={() => setIsAllocationPopupOpen(false)}
        onAllocate={handleAllocation}
        title={
          allocationAction === "allocate"
            ? "Agents Allocation"
            : "Agents Reallocate"
        }
        clientId={client._id}
        // clientName={client.name || client.clientName || "Unknown Client"}
        currentAssignee={client.assignedTo || client.currentAssignee}
        handleGetApi={handleGetApi}
      />
    </tr>
  );
}
