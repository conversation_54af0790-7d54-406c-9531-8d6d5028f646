import React from "react";

interface TabItem {
  label: string;
  route?: string;
  originalIndex?: number;
  enabled?: boolean;
}

interface TicketTabsProps {
  activeTab: number;
  setActiveTab: (idx: number) => void;
  tabs?: TabItem[];
}

// Default tabs for backward compatibility
const defaultTabList = [
  { label: "All Tickets" },
  { label: "Exceptions" },
  { label: "Completed" },
  { label: "Source" },
  { label: "Import" },
];

const TicketTabs: React.FC<TicketTabsProps> = ({
  activeTab,
  setActiveTab,
  tabs,
}) => {
  const tabList = tabs || defaultTabList;

  return (
    <div
      className="flex items-center border-b border-[#E5EAF2] bg-[#F8FAFB]"
      style={{ minHeight: 48 }}
    >
      {tabList.map((tab, idx) => (
        <button
          key={tab.label}
          className={`px-6 py-2 text-[15px] font-medium focus:outline-none transition-colors duration-200 border-b-2 ${
            activeTab === idx
              ? "border-[#1B6DC1] text-[#1B6DC1] bg-white"
              : "border-transparent text-[#7A8CA3] bg-[#F8FAFB] hover:text-[#1B6DC1]"
          }`}
          style={{
            borderRadius: activeTab === idx ? "0 0 0 0" : "8px 8px 0 0",
            marginRight: 8,
            padding: "13px",
            minWidth: 160,
            boxShadow:
              activeTab === idx
                ? "0px -2px 8px 0 rgba(27,109,193,0.04)"
                : "none",
          }}
          onClick={() => setActiveTab(idx)}
        >
          {tab.label}
        </button>
      ))}
    </div>
  );
};

export default TicketTabs;
