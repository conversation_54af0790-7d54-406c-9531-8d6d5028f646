"use client";

import React, { useState } from "react";
import {
  <PERSON>alog,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Checkbox,
  Button as <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@mui/material";
import { allocateClient } from "../../api/ProviderCredentials/provider";
import { showToast } from "../toaster/ToastProvider";

interface User {
  id: string;
  name: string;
  totalTickets: number;
  allocated: number;
  available: number;
  totalAllocatedBatches: number;
  batchesToAllocate?: number;
}

interface BulkAllocationPopupProps {
  isOpen: boolean;
  onClose: () => void;
  onAllocate: (users: User[]) => void;
  title: string;
  clientId: string;
  users: User[];
}

export function BulkAllocationPopup({
  isOpen,
  onClose,
  onAllocate,
  title,
  clientId,
  users,
}: BulkAllocationPopupProps) {
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);

  const handleSelectUser = (user: User) => {
    setSelectedUsers((prevSelectedUsers) =>
      prevSelectedUsers.includes(user)
        ? prevSelectedUsers.filter((u) => u.id !== user.id)
        : [...prevSelectedUsers, user]
    );
  };

  const handleAllocateBatch = async () => {
    if (selectedUsers.length === 0) {
      showToast.error("Please select users for allocation.");
      return;
    }

    let successCount = 0;
    let errorCount = 0;

    try {
      // Loop through each selected user and their batches
      for (const user of selectedUsers) {
        for (let i = 0; i < (user.batchesToAllocate ?? 0); i++) {
          try {
            await allocateClient({
              input: {
                id: clientId,
                assignedTo: user.id,
              },
            });
            successCount++;
          } catch (error) {
            errorCount++;
            console.error(
              `Failed to allocate client ${clientId} to user ${user.id}:`,
              error
            );
          }
        }
      }

      // Show feedback
      if (successCount > 0 && errorCount === 0) {
        showToast.success(`Successfully allocated ${successCount} batch(es)`);
      } else if (successCount > 0 && errorCount > 0) {
        showToast.error(
          `Allocated ${successCount} batch(es) successfully, ${errorCount} failed`
        );
      } else {
        showToast.error("Failed to allocate batches.");
      }

      // Close the popup
      onAllocate(selectedUsers);
      onClose();
    } catch (error) {
      console.error("Allocation error:", error);
      showToast.error("Failed to allocate selected batches");
    }
  };

  return (
    <Dialog open={isOpen} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Typography variant="h6">{title}</Typography>
      </DialogTitle>

      <DialogContent>
        <table style={{ width: "100%", borderCollapse: "collapse" }}>
          <thead>
            <tr>
              <th>User</th>
              <th>Total Tickets (Target)</th>
              <th>Allocated</th>
              <th>Available</th>
              <th>Total Allocated Batches</th>
              <th>Batches to Allocate</th>
              <th>Select</th>
            </tr>
          </thead>
          <tbody>
            {users.map((user) => (
              <tr key={user.id}>
                <td>{user.name}</td>
                <td>{user.totalTickets}</td>
                <td>{user.allocated}</td>
                <td>{user.available}</td>
                <td>{user.totalAllocatedBatches}</td>
                <td>
                  <input
                    type="number"
                    value={user.batchesToAllocate || 0}
                    onChange={(e) => {
                      const batches = Number(e.target.value);
                      if (batches >= 0) {
                        // Update the user with the specified number of batches
                        user.batchesToAllocate = batches;
                      }
                    }}
                    min="0"
                    style={{
                      width: "60px",
                      padding: "5px",
                      textAlign: "center",
                    }}
                  />
                </td>
                <td>
                  <Checkbox
                    checked={selectedUsers.includes(user)}
                    onChange={() => handleSelectUser(user)}
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </DialogContent>

      <DialogActions>
        <MuiButton onClick={onClose} color="secondary">
          Cancel
        </MuiButton>
        <MuiButton
          onClick={handleAllocateBatch}
          color="primary"
          variant="contained"
        >
          Allocate
        </MuiButton>
      </DialogActions>
    </Dialog>
  );
}
