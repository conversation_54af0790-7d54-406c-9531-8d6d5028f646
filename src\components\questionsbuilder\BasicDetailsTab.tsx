import {
  <PERSON>F<PERSON>,
  FormControl<PERSON>abel,
  <PERSON>witch,
  Box,
  InputLabel,
} from "@mui/material";
import type { FieldType } from "./types";

interface BasicDetailsTabProps {
  editedField: FieldType;
  handleFieldChange: (
    key: keyof FieldType,
    value: FieldType[keyof FieldType]
  ) => void;
}

export const BasicDetailsTab: React.FC<BasicDetailsTabProps> = ({
  editedField,
  handleFieldChange,
}) => {
  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <Box>
        <InputLabel className="lable-color">Label</InputLabel>
        <TextField
          value={editedField.label}
          onChange={(e) => handleFieldChange("label", e.target.value)}
          fullWidth
        />
      </Box>

      <Box>
        <InputLabel className="lable-color">Placeholder</InputLabel>
        <TextField
          value={editedField.placeholder || ""}
          onChange={(e) => handleFieldChange("placeholder", e.target.value)}
          fullWidth
        />
      </Box>

      {/* {["text", "number", "email", "phone"].includes(
        editedField.field_type
      ) && (
        <Box>
          <InputLabel className="lable-color">Field Type</InputLabel>
          <TextField
            select
            value={editedField.field_type}
            onChange={(e) => handleFieldChange("field_type", e.target.value)}
            fullWidth
          >
            {[
              { value: "text", label: "Text" },
              { value: "number", label: "Number" },
              { value: "email", label: "Email" },
              { value: "phone", label: "Phone" },
            ].map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </TextField>
        </Box>
      )} */}
      {editedField.field_type !== "grid" && (
        <>
          <FormControlLabel
            control={
              <Switch
                checked={!!editedField.required}
                onChange={(e) =>
                  handleFieldChange("required", e.target.checked)
                }
              />
            }
            label="Required"
          />

          <Box>
            <FormControlLabel
              control={
                <Switch
                  checked={!!editedField.show_in_grid}
                  onChange={(e) => {
                    const checked = e.target.checked;
                    handleFieldChange("show_in_grid", checked);
                    // If Show In Grid is turned off, also turn off the dependent options
                    if (!checked) {
                      handleFieldChange("only_in_grid", false);
                      handleFieldChange("is_default", false);
                    }
                  }}
                />
              }
              label="Show In Grid"
            />

            {editedField.show_in_grid && (
              <Box sx={{ ml: 4, mt: 1 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={!!editedField.only_in_grid}
                      onChange={(e) => {
                        const checked = e.target.checked;
                        handleFieldChange("only_in_grid", checked);
                        if (checked) {
                          handleFieldChange("show_in_grid", true);
                        }
                      }}
                    />
                  }
                  label="Only in Grid"
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={!!editedField.is_default}
                      onChange={(e) => {
                        const checked = e.target.checked;
                        handleFieldChange("is_default", checked);
                        if (checked) {
                          handleFieldChange("show_in_grid", true);
                        }
                      }}
                    />
                  }
                  label="Is Default In Grid"
                />
              </Box>
            )}
          </Box>

          <FormControlLabel
            control={
              <Switch
                checked={!!editedField.is_import}
                onChange={(e) =>
                  handleFieldChange("is_import", e.target.checked)
                }
              />
            }
            label="Is Import"
          />
          <FormControlLabel
            control={
              <Switch
                checked={!!editedField.only_in_custom_form}
                onChange={(e) =>
                  handleFieldChange("only_in_custom_form", e.target.checked)
                }
              />
            }
            label="Only in Custom forms"
          />
        </>
      )}
    </Box>
  );
};
