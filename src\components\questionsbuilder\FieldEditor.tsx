import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
  Box,
  useMediaQuery,
} from "@mui/material";
import { useState, useEffect } from "react";
import Button from "@/app/[components]/Button1";
import { BasicDetailsTab } from "./BasicDetailsTab";
import { RulesTab } from "./RulesTab";
import type { FieldType, FieldEditorProps } from "./types";
import { createGlobals } from "@/api/Globals/globals";

export const FieldEditor: React.FC<FieldEditorProps> = ({
  open,
  field,
  onClose,
  onSave,
  allFields = [],
  setSteps,
  steps,
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [editedField, setEditedField] = useState<FieldType | null>(null);
  const isMobile = useMediaQuery("(max-width:600px)");

  useEffect(() => {
    if (field) {
      setEditedField({ ...field });
    }
  }, [field]);

  if (!editedField) return null;

  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleFieldChange = (key: keyof FieldType, value: unknown) => {
    setEditedField((prev) => (prev ? { ...prev, [key]: value } : null));
  };

  const handleSave = async () => {
    if (!editedField) return;
    try {
      if (editedField.global) {
        const payload = {
          input: {
            name: editedField.label,
            isTable: editedField.field_type === "grid",
            options: editedField.options || [],
          },
        };
        await createGlobals(payload);
      }

      onSave(editedField);
    } catch (error) {
      console.error("Failed to add global field:", error);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="sm" scroll="body">
      <Box sx={{ p: 0 }}>
        <DialogTitle sx={{ backgroundColor: "#f5f7fa", fontWeight: 600 }}>
          Edit Field
        </DialogTitle>

        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
          sx={{
            backgroundColor: "#f0f2f5",
            borderBottom: "1px solid #ccc",
          }}
        >
          <Tab sx={{ textTransform: "none" }} label="Basic Details" />
          <Tab sx={{ textTransform: "none" }} label="Rules" />
        </Tabs>

        <DialogContent sx={{ p: isMobile ? 2 : 3 }}>
          {activeTab === 0 && (
            <BasicDetailsTab
              editedField={editedField}
              handleFieldChange={handleFieldChange}
            />
          )}

          {activeTab === 1 && (
            <RulesTab
              editedField={editedField}
              allFields={allFields}
              handleFieldChange={handleFieldChange}
              steps={steps ?? []}
              setSteps={setSteps ?? (() => {})}
            />
          )}
        </DialogContent>

        <DialogActions sx={{ p: isMobile ? 2 : 3 }}>
          <Button
            variant="outlined"
            className="buttonstyle bg-cyan-950 hover:bg-cyan-950"
            onClick={onClose}
            text="Cancel"
          >
            Cancel
          </Button>
          <Button
            className="buttonstyle bg-teal-500 hover:bg-teal-600"
            onClick={handleSave}
            text="Save Changes"
          >
            Save Changes
          </Button>
        </DialogActions>
      </Box>
    </Dialog>
  );
};
