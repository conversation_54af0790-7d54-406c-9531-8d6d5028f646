// components/FieldRenderer.tsx
import {
  Text<PERSON>ield,
  MenuItem,
  Switch,
  FormControlLabel,
  Typography,
  InputLabel,
  Box,
  FormControl,
  Stack,
  IconButton,
  Menu,
} from "@mui/material";
import { DatePicker, TimePicker, DateTimePicker } from "@mui/x-date-pickers";
import { useState, useEffect } from "react";
import dayjs from "dayjs";
import type { Dayjs } from "dayjs";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import Checkbox from "@mui/material/Checkbox";
import DragIndicatorIcon from "@mui/icons-material/DragIndicator";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { GridTable } from "./gridtable";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import type { FieldOption } from "./tablefields";
import { getGlobalOptions } from "@/api/Globals/globals";
import Visibility from "@mui/icons-material/Visibility";
import VisibilityOff from "@mui/icons-material/VisibilityOff";
import { getSubModulePermissionCommon } from "@/utils/generic";

export const FieldRenderer = ({
  field,
  onEdit,
  value,
  onChange,
  setSelectedStateId,
  selectedStateId,
}: {
  field: FieldOption;
  onEdit?: () => void;
  setSelectedStateId: (id: string | null) => void;
  selectedStateId: string | null;
  value?: string | number | boolean | string[] | null;
  onChange?: (value: string | number | boolean | string[] | null) => void;
}) => {
  const [localValue, setLocalValue] = useState<
    string | number | boolean | string[] | null
  >(
    field.field_type === "multiselect"
      ? Array.isArray(value)
        ? value
        : []
      : value !== undefined && value !== null
        ? value
        : ""
  );
  const [dateValue, setDateValue] = useState<Dayjs | null>(dayjs());
  const [fileName, setFileName] = useState("");
  const [, setImagePreview] = useState("");
  const [toggleValue, setToggleValue] = useState(
    value !== undefined ? value : false
  );
  const [globalOptions, setGlobalOptions] = useState<
    { id: string; value: string }[]
  >([]);
  const [isGlobalLoading, setIsGlobalLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  useEffect(() => {
    if (value !== undefined) {
      if (field.field_type === "toggle") {
        setToggleValue(value);
      } else {
        setLocalValue(value);
      }
    }
  }, [value, field.field_type]);

  useEffect(() => {
    if (field.field_type === "global_select" && field.label) {
      const fetchGlobalOptions = async () => {
        setIsGlobalLoading(true);
        try {
          const params: { name: string; stateId?: string } = {
            name: field.globals_name ?? "",
          };
          if (field.globals_name?.toLowerCase() === "city" && selectedStateId) {
            params.stateId = selectedStateId;
          }
          const res = await getGlobalOptions(params);
          const data = res?.getGlobalByName;
          if (Array.isArray(data)) {
            setGlobalOptions(data);
          } else {
            setGlobalOptions([]);
          }
        } catch {
          setGlobalOptions([]);
        } finally {
          setIsGlobalLoading(false);
        }
      };

      fetchGlobalOptions();
    }
    // Add selectedStateId as dependency so city options update when state changes
  }, [field.label, field.field_type, selectedStateId]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const newValue = e.target.value;
    if (newValue === undefined || newValue === null) setLocalValue(newValue);
    onChange?.(newValue);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) setFileName(file.name);
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImagePreview(URL.createObjectURL(file));
      setFileName(file.name);
    }
  };

  const handleToggleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.checked;
    setToggleValue(newValue);
    onChange?.(newValue);
  };

  const FieldActionsMenu = ({
    onEdit,
    onDelete,
  }: {
    onEdit?: () => void;
    onDelete?: () => void;
  }) => {
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    return (
      <>
        {getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled &&<IconButton size="small" onClick={handleMenuClick}>
          <MoreVertIcon fontSize="small" />
        </IconButton>}
        <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
          <MenuItem
            onClick={() => {
              handleClose();
              if (onEdit) onEdit();
            }}
          >
            Edit
          </MenuItem>
          <MenuItem
            onClick={() => {
              handleClose();
              if (onDelete) onDelete();
            }}
          >
            Delete
          </MenuItem>
        </Menu>
      </>
    );
  };

  switch (field.field_type) {
    case "text":
    case "phone":
    case "email":
    case "number":
    case "password":
      return (
        <Box sx={{ position: "relative" }}>
          <InputLabel
            shrink
            sx={{ fontSize: "21px", fontWeight: 500, color: "#A3AED0" }}
          >
            {field.label}
            {field.required && <span style={{ color: "red" }}> *</span>}
          </InputLabel>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
           {getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled && <DragIndicatorIcon
              fontSize="small"
              sx={{ cursor: "grab", color: "action.active" }}
            />}
            <TextField
              fullWidth
              name={field.name}
              required={field.required}
              variant="outlined"
              value={localValue ?? ""}
              placeholder={
                field.placeholder
                  ? field.placeholder
                  : `Enter the ${field.label}`
              }
              onChange={handleChange}
              disabled={!getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled}
              type={
                field.field_type === "phone"
                  ? "tel"
                  : field.field_type === "password"
                    ? showPassword
                      ? "text"
                      : "password"
                    : field.field_type
              }
              inputProps={
                field.field_type === "number"
                  ? { inputMode: "numeric", pattern: "[0-9]*" }
                  : {}
              }
              InputProps={{
                endAdornment: (
                  <>
                    {field.field_type === "password" && (
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={() => setShowPassword((show) => !show)}
                        edge="end"
                        size="small"
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    )}
                   {getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled && <FieldActionsMenu
                      onEdit={onEdit}
                      onDelete={field.onDelete}
                    />}
                  </>
                ),
              }}
            />
          </div>
        </Box>
      );

    case "textarea":
      return (
        <Box sx={{ position: "relative" }}>
          <InputLabel
            shrink
            sx={{ fontSize: "21px", fontWeight: 500, color: "#A3AED0" }}
          >
            {field.label}
            {field.required && <span style={{ color: "red" }}> *</span>}
          </InputLabel>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            {" "}
            {getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled &&<DragIndicatorIcon
              fontSize="small"
              sx={{ cursor: "grab", color: "action.active" }}
            />}
            <TextField
              fullWidth
              name={field.name}
              required={field.required}
              variant="outlined"
              disabled={!getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled}
              multiline
              placeholder={`Enter the ${field.label}`}
              minRows={3}
              value={localValue}
              onChange={handleChange}
              InputProps={{
                endAdornment: (
                  getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled &&<FieldActionsMenu onEdit={onEdit} onDelete={field.onDelete} />
                ),
              }}
            />
          </div>
        </Box>
      );

    case "select":
      return (
        <Box sx={{ position: "relative" }}>
          <InputLabel
            shrink
            sx={{ fontSize: "21px", fontWeight: 500, color: "#A3AED0" }}
          >
            {field.label}
            {field.required && <span style={{ color: "red" }}> *</span>}
          </InputLabel>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            {getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled &&<DragIndicatorIcon
              fontSize="small"
              sx={{ cursor: "grab", color: "action.active" }}
            />}
            <TextField
              select
              fullWidth
              name={field.name}
              required={field.required}
              variant="outlined"
              value={localValue ?? ""}
              disabled={!getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled}
              onChange={handleChange}
              InputProps={{
                endAdornment: (
                  getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled && <FieldActionsMenu onEdit={onEdit} onDelete={field.onDelete} />
                ),
              }}
              SelectProps={{
                displayEmpty: true,
              }}
            >
              <MenuItem disabled value="">
                {field.placeholder || `Select ${field.label}`}
              </MenuItem>
              {field.options?.map(
                (option: string | { id: string; value: string }) =>
                  typeof option === "string" ? (
                    <MenuItem key={option} value={option}>
                      {option}
                    </MenuItem>
                  ) : (
                    <MenuItem key={option.id} value={option.value}>
                      {option.value}
                    </MenuItem>
                  )
              )}
            </TextField>
          </div>
        </Box>
      );
    case "global_select":
      return (
        <Box sx={{ position: "relative" }}>
          <InputLabel
            shrink
            sx={{ fontSize: "21px", fontWeight: 500, color: "#A3AED0" }}
          >
            {field.label
              ? field.label.charAt(0).toUpperCase() + field.label.slice(1)
              : ""}
            {field.required && <span style={{ color: "red" }}> *</span>}
          </InputLabel>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            {getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled &&<DragIndicatorIcon
              fontSize="small"
              sx={{ cursor: "grab", color: "action.active" }}
            />}
            <TextField
              select
              fullWidth
              name={field.name}
              required={field.required}
              variant="outlined"
              value={localValue ?? ""}
              disabled={!getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled}
              onChange={(e) => {
                const selectedValue = e.target.value;
                setLocalValue(selectedValue);
                onChange?.(selectedValue);
                // If this is the state field, store its ID (not value)
                if (
                  field.globals_name &&
                  typeof field.globals_name === "string" &&
                  field.globals_name.toLowerCase() === "state"
                ) {
                  const selectedOption = globalOptions.find(
                    (opt) => opt.value === selectedValue
                  );
                  setSelectedStateId(selectedOption ? selectedOption.id : null);
                }
              }}
              InputProps={{
                endAdornment: (
                  getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled &&<FieldActionsMenu onEdit={onEdit} onDelete={field.onDelete} />
                ),
              }}
              SelectProps={{
                displayEmpty: true,
              }}
            >
              <MenuItem disabled value="">
                {field.placeholder || `Select ${field.label}`}
              </MenuItem>

              {isGlobalLoading ? (
                <MenuItem disabled value="">
                  Loading options...
                </MenuItem>
              ) : globalOptions.length === 0 ? (
                <MenuItem disabled value="">
                  No options found
                </MenuItem>
              ) : (
                globalOptions.map((opt) => (
                  <MenuItem key={opt.id} value={opt.value}>
                    {opt.value}
                  </MenuItem>
                ))
              )}
            </TextField>
          </div>
        </Box>
      );
    case "multiselect":
      return (
        <Box sx={{ position: "relative" }}>
          <InputLabel
            shrink
            sx={{ fontSize: "21px", fontWeight: 500, color: "#A3AED0" }}
          >
            {field.label}
            {field.required && <span style={{ color: "red" }}> *</span>}
          </InputLabel>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
           {getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled && <DragIndicatorIcon
              fontSize="small"
              sx={{ cursor: "grab", color: "action.active" }}
            />}
            <TextField
              select
              fullWidth
              name={field.name}
              required={field.required}
              variant="outlined"
              disabled={!getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled}
              value={Array.isArray(localValue) ? localValue : []}
              onChange={(e) => {
                const newValue = e.target.value;
                setLocalValue(newValue);
                onChange?.(newValue);
              }}
              InputProps={{
                endAdornment: (
                 getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled && <FieldActionsMenu onEdit={onEdit} onDelete={field.onDelete} />
                ),
              }}
              SelectProps={{
                multiple: true,
                displayEmpty: true,
                renderValue: (selected) =>
                  Array.isArray(selected) && selected.length === 0
                    ? field.placeholder || `Select ${field.label}`
                    : (selected as string[]).join(", "),
              }}
            >
              {field.options?.map(
                (
                  option: string | { id: string; value: string; label?: string }
                ) =>
                  typeof option === "string" ? (
                    <MenuItem key={option} value={option}>
                      {option}
                    </MenuItem>
                  ) : (
                    <MenuItem key={option.id} value={option.value}>
                      {option.value}
                    </MenuItem>
                  )
              )}
            </TextField>
          </div>
        </Box>
      );

    case "toggle":
      return (
        <Box sx={{ position: "relative" }}>
          <InputLabel
            shrink
            sx={{ fontSize: "21px", fontWeight: 500, color: "#A3AED0" }}
          >
            {field.label}
            {field.required && <span style={{ color: "red" }}> *</span>}
          </InputLabel>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            {getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled &&<DragIndicatorIcon
              fontSize="small"
              sx={{ cursor: "grab", color: "action.active" }}
            />}
            <Stack direction="row" alignItems="center" spacing={1}>
              <FormControlLabel
                control={
                  <Switch
                    checked={!!toggleValue}
                    onChange={handleToggleChange}
                    name={field.name}
                    disabled={!getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled}
                  />
                }
                label={
                  <>
                    {field.label}
                    {field.required && <span style={{ color: "red" }}>*</span>}
                  </>
                }
              />
             { getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled && <FieldActionsMenu onEdit={onEdit} onDelete={field.onDelete} />}
            </Stack>
          </div>
        </Box>
      );

    case "checkboxes":
      return (
        <Box sx={{ position: "relative" }}>
          <InputLabel
            shrink
            sx={{ fontSize: "21px", fontWeight: 500, color: "#A3AED0" }}
          >
            {field.label}
            {field.required && <span style={{ color: "red" }}> *</span>}
          </InputLabel>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            {getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled &&<DragIndicatorIcon
              fontSize="small"
              sx={{ cursor: "grab", color: "action.active" }}
            />}
            <FormControl
              component="fieldset"
              required={field.required}
              fullWidth
            >
              <Box>
                {field.options?.map(
                  (
                    option:
                      | string
                      | { id: string; value: string; label?: string }
                  ) => {
                    const selectedValues = Array.isArray(localValue)
                      ? localValue
                      : typeof localValue === "string"
                        ? localValue.split(",").filter(Boolean)
                        : [];

                    const optionValue =
                      typeof option === "string" ? option : option.value;
                    const optionLabel =
                      typeof option === "string"
                        ? option
                        : option.label || option.value;

                    return (
                      <FormControlLabel
                        key={typeof option === "string" ? option : option.id}
                        control={
                          <Checkbox
                           disabled={!getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled}
                            checked={selectedValues.includes(optionValue)}
                            onChange={(e) => {
                              let newSelected: string[];
                              if (e.target.checked) {
                                newSelected = [...selectedValues, optionValue];
                              } else {
                                newSelected = selectedValues.filter(
                                  (v) => v !== optionValue
                                );
                              }
                              setLocalValue(newSelected);
                              onChange?.(newSelected);
                            }}
                          />
                        }
                        label={optionLabel}
                      />
                    );
                  }
                )}
              </Box>
            </FormControl>
            {getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled && <FieldActionsMenu onEdit={onEdit} onDelete={field.onDelete} />}
          </div>
        </Box>
      );

    case "date":
      return (
        <Box sx={{ position: "relative" }}>
          <InputLabel
            shrink
            sx={{ fontSize: "21px", fontWeight: 500, color: "#A3AED0" }}
          >
            {field.label}
            {field.required && <span style={{ color: "red" }}> *</span>}
          </InputLabel>

          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
           {getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled && <DragIndicatorIcon
              fontSize="small"
              sx={{ cursor: "grab", color: "action.active" }}
            />}
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DatePicker
                value={dateValue}
                 disabled={!getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled}
                onChange={(newValue) => {
                  setDateValue(newValue);
                  onChange?.(newValue ? newValue.format("YYYY-MM-DD") : null);
                }}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    required: field.required,
                    name: field.name,
                  },
                  day: {
                    sx: {
                      "&.Mui-selected": {
                        backgroundColor: "#27B8AF !important",
                        "&:hover": {
                          backgroundColor: "#27B8AF",
                        },
                      },
                    },
                  },
                }}
              />
            </LocalizationProvider>
          </div>
          <Box
            sx={{
              position: "absolute",
              top: 35,
              right: 0,
              zIndex: 1,
            }}
          >
           {getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled && <FieldActionsMenu onEdit={onEdit} onDelete={field.onDelete} />}
          </Box>
        </Box>
      );

    case "time":
      return (
        <Box sx={{ position: "relative" }}>
          <InputLabel
            shrink
            sx={{ fontSize: "21px", fontWeight: 500, color: "#A3AED0" }}
          >
            {field.label}
            {field.required && <span style={{ color: "red" }}> *</span>}
          </InputLabel>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
          {getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled &&  <DragIndicatorIcon
              fontSize="small"
              sx={{ cursor: "grab", color: "action.active" }}
            />}
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <TimePicker
                value={dateValue}
                 disabled={!getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled}
                onChange={(newValue) => {
                  setDateValue(newValue);
                  onChange?.(newValue ? newValue.format("HH:mm") : null);
                }}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    required: field.required,
                    name: field.name,
                    variant: "outlined",
                    size: "small",
                    sx: {
                      borderRadius: "6px",
                      backgroundColor: "#fff",
                      fontSize: "14px",
                      "& .MuiOutlinedInput-root": {
                        paddingRight: "10px",
                      },
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#cbd5e0",
                      },
                      "&:hover .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#3182ce",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#3182ce",
                      },
                    },
                  },
                  popper: {
                    sx: {
                      zIndex: 1400,
                      "& .MuiPaper-root": {
                        borderRadius: "8px",
                        boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
                        backgroundColor: "#fff",
                        paddingBottom: "0px",
                      },
                    },
                  },
                  layout: {
                    sx: {
                      padding: "0px !important",
                      "& .MuiPickersLayout-actionBar": {
                        justifyContent: "space-around",
                        backgroundColor: "#f9fafb",
                        padding: "8px",
                      },
                      "& .MuiMultiSectionDigitalClockSection-item.Mui-selected":
                        {
                          backgroundColor: "#27B8AF",
                        },
                    },
                  },
                  actionBar: {
                    actions: ["cancel", "accept"],
                    sx: {
                      "& button:first-of-type": {
                        color: "#fff",
                        fontWeight: 500,
                        borderRadius: "6px",
                        height: "31px",
                        boxShadow: "none",
                        fontSize: "15px",
                        marginTop: "5px",
                        marginRight: "8px",
                      },
                      "& button:last-of-type": {
                        backgroundColor: "#27B8AF",
                        color: "#fff",
                        fontWeight: 500,
                        borderRadius: "6px",
                        boxShadow: "none",
                        height: "31px",
                        fontSize: "15px",
                        marginTop: "5px",
                        "&:hover": {
                          backgroundColor: "#27B8AF",
                        },
                      },
                    },
                  },
                }}
              />
            </LocalizationProvider>
          </div>
          <Box
            sx={{
              position: "absolute",
              top: 35,
              right: 0,
              zIndex: 1,
            }}
          >
            {getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled &&<FieldActionsMenu onEdit={onEdit} onDelete={field.onDelete} />}
          </Box>
        </Box>
      );

    case "datetime":
      return (
        <Box sx={{ position: "relative" }}>
          <InputLabel
            shrink
            sx={{ fontSize: "21px", fontWeight: 500, color: "#A3AED0" }}
          >
            {field.label}
            {field.required && <span style={{ color: "red" }}> *</span>}
          </InputLabel>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            {getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled &&<DragIndicatorIcon
              fontSize="small"
              sx={{ cursor: "grab", color: "action.active" }}
            />}
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DateTimePicker
                value={dateValue}
                 disabled={!getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled}
                onChange={(newValue) => {
                  setDateValue(newValue);
                  onChange?.(
                    newValue ? newValue.format("YYYY-MM-DDTHH:mm:ss") : null
                  );
                }}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    required: field.required,
                    name: field.name,
                  },
                }}
              />
            </LocalizationProvider>
          </div>
          <Box
            sx={{
              position: "absolute",
              top: 35,
              right: 0,
              zIndex: 1,
            }}
          >
            {getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled &&<FieldActionsMenu onEdit={onEdit} onDelete={field.onDelete} />}
          </Box>
        </Box>
      );

    case "file_upload":
      return (
        <Box sx={{ position: "relative" }}>
          <InputLabel
            shrink
            sx={{ fontSize: "21px", fontWeight: 500, color: "#A3AED0" }}
          >
            {field.label}
            {field.required && <span style={{ color: "red" }}> *</span>}
          </InputLabel>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            {getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled && <DragIndicatorIcon
              fontSize="small"
              sx={{ cursor: "grab", color: "action.active" }}
            />}
            <FormControl fullWidth>
              <Box
                component="label"
                htmlFor={`file-upload-${field.name}`}
                sx={{
                  border: "1px solid #E0E0E0",
                  borderRadius: "5px",
                  height: 42,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  px: 2,
                  cursor: "pointer",
                  "&:hover": { borderColor: "#A3AED0" },
                }}
              >
                <Typography color="text.secondary">
                  {fileName || field.placeholder}
                </Typography>
                <CloudUploadIcon sx={{ color: "#B0B0B0", mr: 1 }} />
                <input
                  type="file"
                   disabled={!getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled}
                  accept=".pdf,.doc,.docx,.xls,.xlsx,.txt"
                  id={`file-upload-${field.name}`}
                  name={field.name}
                  hidden
                  required={field.required}
                  onChange={handleFileChange}
                />
              </Box>
              {/* {fileName && (
								<Typography variant="caption">{fileName}</Typography>
							)} */}
            </FormControl>
          </div>
          <Box
            sx={{
              position: "absolute",
              top: 35,
              right: 0,
              zIndex: 1,
            }}
          >
            {getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled &&<FieldActionsMenu onEdit={onEdit} onDelete={field.onDelete} />}
          </Box>
        </Box>
      );

    case "image":
      return (
        <Box sx={{ position: "relative" }}>
          <InputLabel
            shrink
            sx={{ fontSize: "21px", fontWeight: 500, color: "#A3AED0" }}
          >
            {field.label}
            {field.required && <span style={{ color: "red" }}> *</span>}
          </InputLabel>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            {getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled && <DragIndicatorIcon
              fontSize="small"
              sx={{ cursor: "grab", color: "action.active" }}
            />}
            <FormControl fullWidth>
              <Box
                component="label"
                htmlFor={`image-upload-${field.name}`}
                sx={{
                  border: "1px solid #E0E0E0",
                  borderRadius: "5px",
                  height: 42,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  px: 2,
                  cursor: "pointer",
                  "&:hover": { borderColor: "#A3AED0" },
                }}
              >
                <Typography color="text.secondary">
                  {fileName || field.placeholder}
                </Typography>
                <CloudUploadIcon sx={{ color: "#B0B0B0", mr: 1 }} />
                <input
                  type="file"
                  accept="image/*"
                  id={`image-upload-${field.name}`}
                  name={field.name}
                   disabled={!getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled}
                  hidden
                  required={field.required}
                  onChange={handleImageChange}
                />
              </Box>
              {/* {imagePreview && (
        <Box mt={1}>
          <img
            src={imagePreview}
            alt="Preview"
            style={{ maxWidth: "100%", borderRadius: "8px", marginTop: 8 }}
          />
        </Box>
      )} */}
            </FormControl>
          </div>
          <Box
            sx={{
              position: "absolute",
              top: 35,
              right: 0,
              zIndex: 1,
            }}
          >
            {getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled &&<FieldActionsMenu onEdit={onEdit} onDelete={field.onDelete} />}
          </Box>
        </Box>
      );
    case "grid":
      return (
        <Box sx={{ position: "relative", mb: 2 }}>
          <InputLabel
            shrink
            sx={{ fontSize: "21px", fontWeight: 500, color: "#A3AED0", mb: 1 }}
          >
            {field.label}
            {field.required && (
              <Box component="span" sx={{ color: "red" }}>
                {" "}
                *
              </Box>
            )}
          </InputLabel>

          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
           { getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled &&<DragIndicatorIcon
              fontSize="small"
              sx={{ cursor: "grab", color: "action.active" }}
            />}
            <Box sx={{ flexGrow: 1, width: "95%" }}>
              <GridTable
                field={field}
                onEdit={onEdit}
                onChange={(data) => {
                  if (field.onChange) field.onChange(data);
                  if (onChange) onChange(JSON.stringify(data));
                }}
              />
            </Box>
            {!getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled &&<FieldActionsMenu onEdit={onEdit} onDelete={field.onDelete} />}
          </Box>
        </Box>
      );
    default:
      return (
        <Typography variant="body2">
          Unsupported field type: {field.field_type}
        </Typography>
      );
  }
};
