// components/FieldRenderer.tsx
import {
  TextField,
  MenuItem,
  Switch,
  FormControlLabel,
  Typography,
  InputLabel,
  Box,
  FormControl,
  Stack,
  IconButton,
  Autocomplete,
} from "@mui/material";
import { DatePicker, TimePicker, DateTimePicker } from "@mui/x-date-pickers";
import { useState, useEffect } from "react";
import dayjs from "dayjs";
import type { Dayjs } from "dayjs";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import Checkbox from "@mui/material/Checkbox";
import { GridTable } from "./gridtable";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import type { FieldOption } from "./tablefields";
import { getGlobalOptions } from "@/api/Globals/globals";
import Visibility from "@mui/icons-material/Visibility";
import VisibilityOff from "@mui/icons-material/VisibilityOff";

export const FieldRenderer = ({
  field,
  onEdit,
  value,
  onChange,
  setSelectedStateId,
  selectedStateId,
  // allFields,
  // fieldValues,
}: {
  field: FieldOption;
  onEdit?: () => void;
  setSelectedStateId: (id: string | null) => void;
  selectedStateId: string | null;
  value?: string | number | boolean | string[] | null;
  onChange?: (value: string | number | boolean | string[] | null) => void;
  allFields?: FieldOption[];
  fieldValues?: Record<string, unknown>;
}) => {
  const [localValue, setLocalValue] = useState<
    string | number | boolean | string[] | null
  >(
    field.field_type === "multiselect"
      ? Array.isArray(value)
        ? value
        : []
      : value !== undefined && value !== null
        ? value
        : ""
  );
  const [dateValue, setDateValue] = useState<Dayjs | null>(dayjs());
  const [fileName, setFileName] = useState("");
  const [, setImagePreview] = useState("");
  const [toggleValue, setToggleValue] = useState(
    value !== undefined ? value : false
  );
  const [globalOptions, setGlobalOptions] = useState<
    { id: string; value: string }[]
  >([]);
  const [isGlobalLoading, setIsGlobalLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  useEffect(() => {
    if (value !== undefined) {
      if (field.field_type === "toggle") {
        setToggleValue(value);
      } else {
        setLocalValue(value);
      }
    }
  }, [value, field.field_type]);

  useEffect(() => {
    if (field.field_type === "global_select" && field.label) {
      const fetchGlobalOptions = async () => {
        setIsGlobalLoading(true);
        try {
          const params: { name: string; stateId?: string } = {
            name: field.globals_name ?? "",
          };
          if (field.globals_name?.toLowerCase() === "city" && selectedStateId) {
            params.stateId = selectedStateId;
          }
          const res = await getGlobalOptions(params);
          const data = res?.getGlobalByName;
          if (Array.isArray(data)) {
            setGlobalOptions(data);
          } else {
            setGlobalOptions([]);
          }
        } catch {
          setGlobalOptions([]);
        } finally {
          setIsGlobalLoading(false);
        }
      };

      fetchGlobalOptions();
    }
    // Add selectedStateId as dependency so city options update when state changes
  }, [field.label, field.field_type, selectedStateId]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const newValue = e.target.value;
    if (newValue === undefined || newValue === null) setLocalValue(newValue);
    onChange?.(newValue);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) setFileName(file.name);
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImagePreview(URL.createObjectURL(file));
      setFileName(file.name);
    }
  };

  const handleToggleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.checked;
    setToggleValue(newValue);
    onChange?.(newValue);
  };

  switch (field.field_type) {
    case "text":
    case "phone":
    case "email":
    case "number":
    case "password":
      return (
        <Box sx={{ position: "relative" }}>
          <InputLabel
            shrink
            sx={{ fontSize: "21px", fontWeight: 500, color: "#A3AED0" }}
          >
            {field.label}
            {field.required && <span style={{ color: "red" }}> *</span>}
          </InputLabel>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            <TextField
              fullWidth
              name={field.name}
              required={field.required}
              variant="outlined"
              value={localValue ?? ""}
              placeholder={
                field.placeholder
                  ? field.placeholder
                  : `Enter the ${field.label}`
              }
              onChange={handleChange}
              type={
                field.field_type === "phone"
                  ? "tel"
                  : field.field_type === "password"
                    ? showPassword
                      ? "text"
                      : "password"
                    : field.field_type
              }
              inputProps={
                field.field_type === "number"
                  ? { inputMode: "numeric", pattern: "[0-9]*" }
                  : {}
              }
              InputProps={{
                endAdornment: (
                  <>
                    {field.field_type === "password" && (
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={() => setShowPassword((show) => !show)}
                        edge="end"
                        size="small"
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    )}
                  </>
                ),
              }}
            />
          </div>
        </Box>
      );

    case "textarea":
      return (
        <Box sx={{ position: "relative" }}>
          <InputLabel
            shrink
            sx={{ fontSize: "21px", fontWeight: 500, color: "#A3AED0" }}
          >
            {field.label}
            {field.required && <span style={{ color: "red" }}> *</span>}
          </InputLabel>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            {" "}
            <TextField
              fullWidth
              name={field.name}
              required={field.required}
              variant="outlined"
              multiline
              placeholder={`Enter the ${field.label}`}
              minRows={3}
              value={localValue}
              onChange={handleChange}
              InputProps={{}}
            />
          </div>
        </Box>
      );

    case "select":
      return (
        <Box sx={{ position: "relative" }}>
          <InputLabel
            shrink
            sx={{ fontSize: "21px", fontWeight: 500, color: "#A3AED0" }}
          >
            {field.label}
            {field.required && <span style={{ color: "red" }}> *</span>}
          </InputLabel>
          <Autocomplete
            fullWidth
            options={
              field.options?.map((option) =>
                typeof option === "string" ? option : option.value
              ) || []
            }
            value={
              typeof localValue === "string" &&
              field.options?.some((opt) =>
                typeof opt === "string"
                  ? opt === localValue
                  : opt.value === localValue
              )
                ? localValue
                : null
            }
            onChange={(_, newValue) => {
              setLocalValue(newValue);
              onChange?.(newValue);
            }}
            renderInput={(params) => (
              <TextField
                {...params}
                placeholder={field.placeholder || `Select ${field.label}`}
                variant="outlined"
                required={field.required}
              />
            )}
            isOptionEqualToValue={(option, value) => option === value}
          />
        </Box>
      );

    case "global_select":
      return (
        <Box sx={{ position: "relative" }}>
          <InputLabel
            shrink
            sx={{ fontSize: "21px", fontWeight: 500, color: "#A3AED0" }}
          >
            {field.label}
            {field.required && <span style={{ color: "red" }}> *</span>}
          </InputLabel>
          <Autocomplete
            fullWidth
            disablePortal
            loading={isGlobalLoading}
            options={globalOptions.map((opt) => opt.value)}
            value={
              typeof localValue === "string" &&
              globalOptions.some((opt) => opt.value === localValue)
                ? localValue
                : null
            }
            onChange={(_, newValue) => {
              setLocalValue(newValue);
              onChange?.(newValue);

              // If the global field is "state", set selectedStateId
              if (
                field.globals_name?.toLowerCase() === "state" &&
                typeof newValue === "string"
              ) {
                const selectedOption = globalOptions.find(
                  (opt) => opt.value === newValue
                );
                setSelectedStateId(selectedOption?.id ?? null);
              }
            }}
            renderInput={(params) => (
              <TextField
                {...params}
                required={field.required}
                variant="outlined"
                placeholder={field.placeholder || `Select ${field.label}`}
              />
            )}
            isOptionEqualToValue={(option, value) => option === value}
            noOptionsText={
              isGlobalLoading ? "Loading options..." : "No options found"
            }
          />
        </Box>
      );
    case "multiselect":
      return (
        <Box sx={{ position: "relative" }}>
          <InputLabel
            shrink
            sx={{ fontSize: "21px", fontWeight: 500, color: "#A3AED0" }}
          >
            {field.label}
            {field.required && <span style={{ color: "red" }}> *</span>}
          </InputLabel>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            <TextField
              select
              fullWidth
              name={field.name}
              required={field.required}
              variant="outlined"
              value={Array.isArray(localValue) ? localValue : []}
              onChange={(e) => {
                const newValue = e.target.value;
                setLocalValue(newValue);
                onChange?.(newValue);
              }}
              InputProps={{}}
              SelectProps={{
                multiple: true,
                displayEmpty: true,
                renderValue: (selected) =>
                  Array.isArray(selected) && selected.length === 0
                    ? field.placeholder || `Select ${field.label}`
                    : (selected as string[]).join(", "),
              }}
            >
              {field.options?.map(
                (
                  option: string | { id: string; value: string; label?: string }
                ) =>
                  typeof option === "string" ? (
                    <MenuItem key={option} value={option}>
                      {option}
                    </MenuItem>
                  ) : (
                    <MenuItem key={option.id} value={option.value}>
                      {option.value}
                    </MenuItem>
                  )
              )}
            </TextField>
          </div>
        </Box>
      );

    case "toggle":
      return (
        <Box sx={{ position: "relative" }}>
          <InputLabel
            shrink
            sx={{ fontSize: "21px", fontWeight: 500, color: "#A3AED0" }}
          >
            {field.label}
            {field.required && <span style={{ color: "red" }}> *</span>}
          </InputLabel>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            <Stack direction="row" alignItems="center" spacing={1}>
              <FormControlLabel
                control={
                  <Switch
                    checked={!!toggleValue}
                    onChange={handleToggleChange}
                    name={field.name}
                  />
                }
                label={
                  <>
                    {field.label}
                    {field.required && <span style={{ color: "red" }}>*</span>}
                  </>
                }
              />
            </Stack>
          </div>
        </Box>
      );

    case "checkboxes":
      return (
        <Box sx={{ position: "relative" }}>
          <InputLabel
            shrink
            sx={{ fontSize: "21px", fontWeight: 500, color: "#A3AED0" }}
          >
            {field.label}
            {field.required && <span style={{ color: "red" }}> *</span>}
          </InputLabel>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            <FormControl
              component="fieldset"
              required={field.required}
              fullWidth
            >
              <Box>
                {field.options?.map(
                  (
                    option:
                      | string
                      | { id: string; value: string; label?: string }
                  ) => {
                    const selectedValues = Array.isArray(localValue)
                      ? localValue
                      : typeof localValue === "string"
                        ? localValue.split(",").filter(Boolean)
                        : [];

                    const optionValue =
                      typeof option === "string" ? option : option.value;
                    const optionLabel =
                      typeof option === "string"
                        ? option
                        : option.label || option.value;

                    return (
                      <FormControlLabel
                        key={typeof option === "string" ? option : option.id}
                        control={
                          <Checkbox
                            checked={selectedValues.includes(optionValue)}
                            onChange={(e) => {
                              let newSelected: string[];
                              if (e.target.checked) {
                                newSelected = [...selectedValues, optionValue];
                              } else {
                                newSelected = selectedValues.filter(
                                  (v) => v !== optionValue
                                );
                              }
                              setLocalValue(newSelected);
                              onChange?.(newSelected);
                            }}
                          />
                        }
                        label={optionLabel}
                      />
                    );
                  }
                )}
              </Box>
            </FormControl>
          </div>
        </Box>
      );

    case "date":
      return (
        <Box sx={{ position: "relative" }}>
          <InputLabel
            shrink
            sx={{ fontSize: "21px", fontWeight: 500, color: "#A3AED0" }}
          >
            {field.label}
            {field.required && <span style={{ color: "red" }}> *</span>}
          </InputLabel>

          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DatePicker
                value={dateValue}
                onChange={(newValue) => {
                  setDateValue(newValue);
                  onChange?.(newValue ? newValue.format("YYYY-MM-DD") : null);
                }}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    required: field.required,
                    name: field.name,
                  },
                  day: {
                    sx: {
                      "&.Mui-selected": {
                        backgroundColor: "#27B8AF !important",
                        "&:hover": {
                          backgroundColor: "#27B8AF",
                        },
                      },
                    },
                  },
                }}
              />
            </LocalizationProvider>
          </div>
          <Box
            sx={{
              position: "absolute",
              top: 35,
              right: 0,
              zIndex: 1,
            }}
          ></Box>
        </Box>
      );

    case "time":
      return (
        <Box sx={{ position: "relative" }}>
          <InputLabel
            shrink
            sx={{ fontSize: "21px", fontWeight: 500, color: "#A3AED0" }}
          >
            {field.label}
            {field.required && <span style={{ color: "red" }}> *</span>}
          </InputLabel>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <TimePicker
                value={dateValue}
                onChange={(newValue) => {
                  setDateValue(newValue);
                  onChange?.(newValue ? newValue.format("HH:mm") : null);
                }}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    required: field.required,
                    name: field.name,
                    variant: "outlined",
                    size: "small",
                    sx: {
                      borderRadius: "6px",
                      backgroundColor: "#fff",
                      fontSize: "14px",
                      "& .MuiOutlinedInput-root": {
                        paddingRight: "10px",
                      },
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#cbd5e0",
                      },
                      "&:hover .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#3182ce",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#3182ce",
                      },
                    },
                  },
                  popper: {
                    sx: {
                      zIndex: 1400,
                      "& .MuiPaper-root": {
                        borderRadius: "8px",
                        boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
                        backgroundColor: "#fff",
                        paddingBottom: "0px",
                      },
                    },
                  },
                  layout: {
                    sx: {
                      padding: "0px !important",
                      "& .MuiPickersLayout-actionBar": {
                        justifyContent: "space-around",
                        backgroundColor: "#f9fafb",
                        padding: "8px",
                      },
                      "& .MuiMultiSectionDigitalClockSection-item.Mui-selected":
                        {
                          backgroundColor: "#27B8AF",
                        },
                    },
                  },
                  actionBar: {
                    actions: ["cancel", "accept"],
                    sx: {
                      "& button:first-of-type": {
                        color: "#fff",
                        fontWeight: 500,
                        borderRadius: "6px",
                        height: "31px",
                        boxShadow: "none",
                        fontSize: "15px",
                        marginTop: "5px",
                        marginRight: "8px",
                      },
                      "& button:last-of-type": {
                        backgroundColor: "#27B8AF",
                        color: "#fff",
                        fontWeight: 500,
                        borderRadius: "6px",
                        boxShadow: "none",
                        height: "31px",
                        fontSize: "15px",
                        marginTop: "5px",
                        "&:hover": {
                          backgroundColor: "#27B8AF",
                        },
                      },
                    },
                  },
                }}
              />
            </LocalizationProvider>
          </div>
          <Box
            sx={{
              position: "absolute",
              top: 35,
              right: 0,
              zIndex: 1,
            }}
          ></Box>
        </Box>
      );

    case "datetime":
      return (
        <Box sx={{ position: "relative" }}>
          <InputLabel
            shrink
            sx={{ fontSize: "21px", fontWeight: 500, color: "#A3AED0" }}
          >
            {field.label}
            {field.required && <span style={{ color: "red" }}> *</span>}
          </InputLabel>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DateTimePicker
                value={dateValue}
                onChange={(newValue) => {
                  setDateValue(newValue);
                  onChange?.(
                    newValue ? newValue.format("YYYY-MM-DDTHH:mm:ss") : null
                  );
                }}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    required: field.required,
                    name: field.name,
                  },
                }}
              />
            </LocalizationProvider>
          </div>
          <Box
            sx={{
              position: "absolute",
              top: 35,
              right: 0,
              zIndex: 1,
            }}
          ></Box>
        </Box>
      );

    case "file_upload":
      return (
        <Box sx={{ position: "relative" }}>
          <InputLabel
            shrink
            sx={{ fontSize: "21px", fontWeight: 500, color: "#A3AED0" }}
          >
            {field.label}
            {field.required && <span style={{ color: "red" }}> *</span>}
          </InputLabel>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            <FormControl fullWidth>
              <Box
                component="label"
                htmlFor={`file-upload-${field.name}`}
                sx={{
                  border: "1px solid #E0E0E0",
                  borderRadius: "5px",
                  height: 42,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  px: 2,
                  cursor: "pointer",
                  "&:hover": { borderColor: "#A3AED0" },
                }}
              >
                <Typography color="text.secondary">
                  {fileName || field.placeholder}
                </Typography>
                <CloudUploadIcon sx={{ color: "#B0B0B0", mr: 1 }} />
                <input
                  type="file"
                  accept=".pdf,.doc,.docx,.xls,.xlsx,.txt"
                  id={`file-upload-${field.name}`}
                  name={field.name}
                  hidden
                  required={field.required}
                  onChange={handleFileChange}
                />
              </Box>
              {/* {fileName && (
                                <Typography variant="caption">{fileName}</Typography>
                            )} */}
            </FormControl>
          </div>
          <Box
            sx={{
              position: "absolute",
              top: 35,
              right: 0,
              zIndex: 1,
            }}
          ></Box>
        </Box>
      );

    case "image":
      return (
        <Box sx={{ position: "relative" }}>
          <InputLabel
            shrink
            sx={{ fontSize: "21px", fontWeight: 500, color: "#A3AED0" }}
          >
            {field.label}
            {field.required && <span style={{ color: "red" }}> *</span>}
          </InputLabel>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            <FormControl fullWidth>
              <Box
                component="label"
                htmlFor={`image-upload-${field.name}`}
                sx={{
                  border: "1px solid #E0E0E0",
                  borderRadius: "5px",
                  height: 42,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  px: 2,
                  cursor: "pointer",
                  "&:hover": { borderColor: "#A3AED0" },
                }}
              >
                <Typography color="text.secondary">
                  {fileName || field.placeholder}
                </Typography>
                <CloudUploadIcon sx={{ color: "#B0B0B0", mr: 1 }} />
                <input
                  type="file"
                  accept="image/*"
                  id={`image-upload-${field.name}`}
                  name={field.name}
                  hidden
                  required={field.required}
                  onChange={handleImageChange}
                />
              </Box>
              {/* {imagePreview && (
        <Box mt={1}>
          <img
            src={imagePreview}
            alt="Preview"
            style={{ maxWidth: "100%", borderRadius: "8px", marginTop: 8 }}
          />
        </Box>
      )} */}
            </FormControl>
          </div>
          <Box
            sx={{
              position: "absolute",
              top: 35,
              right: 0,
              zIndex: 1,
            }}
          ></Box>
        </Box>
      );
    case "grid":
      return (
        <Box sx={{ position: "relative", mb: 2 }}>
          <InputLabel
            shrink
            sx={{ fontSize: "21px", fontWeight: 500, color: "#A3AED0", mb: 1 }}
          >
            {field.label}
            {field.required && (
              <Box component="span" sx={{ color: "red" }}>
                {" "}
                *
              </Box>
            )}
          </InputLabel>

          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Box sx={{ flexGrow: 1, width: "95%" }}>
              <GridTable
                field={field}
                onEdit={onEdit}
                // allFields={allFields}
                // fieldValues={fieldValues}
                onChange={(data) => {
                  if (field.onChange) field.onChange(data);
                  if (onChange) onChange(JSON.stringify(data));
                }}
              />
            </Box>
          </Box>
        </Box>
      );
    default:
      return (
        <Typography variant="body2">
          Unsupported field type: {field.field_type}
        </Typography>
      );
  }
};
