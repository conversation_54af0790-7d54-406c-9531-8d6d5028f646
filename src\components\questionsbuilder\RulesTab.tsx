import {
  <PERSON>,
  Typo<PERSON>,
  IconButton,
  InputLabel,
  FormControl,
  Select,
  MenuItem,
  ListItemText,
  Checkbox,
  // FormGroup,
  TextField,
  FormControlLabel,
  Switch,
  ListSubheader,
} from "@mui/material";
import { Delete } from "@mui/icons-material";
import type { FieldType, LogicCondition } from "./types";
import Button from "@/app/[components]/Button1";
import { useEffect, useState } from "react";
import InfoIcon from "@/app/[components]/infoIcon";
import { getGlobalOptions } from "@/api/Globals/globals";
import type { StepType } from "./types";
import { getTemplate } from "@/api/dynamicTemplate/template";

interface RulesTabProps {
  editedField: FieldType;

  allFields: FieldType[];
  handleFieldChange: (key: keyof FieldType, value: unknown) => void;
  steps: StepType[];
  setSteps: (steps: StepType[]) => void;
}
export const RulesTab: React.FC<RulesTabProps> = ({
  editedField,
  allFields,
  handleFieldChange,
  steps,
  setSteps,
}) => {
  const [isDependent, setIsDependent] = useState(!!editedField.visibleIf);
  const [logicType, setLogicType] = useState<"AND" | "OR">(
    editedField.logicJoinType || "OR"
  );
  const [, setSelectedOptions] = useState<string[]>([]);
  const [prefillEnabled, setPrefillEnabled] = useState(() => {
    // Check multiple conditions to determine if prefill is enabled
    const hasBasicPrefill =
      editedField.prefillEnabled ||
      (editedField.prefillFieldIds && editedField.prefillFieldIds.length > 0) ||
      editedField.prefillIdentifier;

    const hasExtendedPrefill = !!(
      (
        editedField as {
          identifierType?: string;
          prefillFields?: unknown;
          prefilledFields?: unknown;
        }
      )?.identifierType ||
      (
        editedField as {
          identifierType?: string;
          prefillFields?: unknown;
          prefilledFields?: unknown;
        }
      )?.prefillFields ||
      (
        editedField as {
          identifierType?: string;
          prefillFields?: unknown;
          prefilledFields?: unknown;
        }
      )?.prefilledFields
    );

    return hasBasicPrefill || hasExtendedPrefill;
  });

  const [identifierType, setIdentifierType] = useState<string>(() => {
    // Check multiple possible property names for identifier type
    return (
      editedField.prefillIdentifier ||
      (editedField as { identifierType?: string })?.identifierType ||
      (editedField as { identifier?: string })?.identifier ||
      ""
    );
  });
  const [conditions, setConditions] = useState<LogicCondition[]>(
    editedField.logicConditions || []
  );
  // Extend FieldType to include optional cloneEnabled property
  type FieldTypeWithClone = FieldType & { cloneEnabled?: boolean };

  const [cloneEnabled, setCloneEnabled] = useState(
    (editedField as FieldTypeWithClone).cloneEnabled || false
  );
  const [selectedCloneStep, setSelectedCloneStep] = useState<string>("");
  const [selectedCloneSection, setSelectedCloneSection] = useState<string>("");
  const [selectedPrefillFields, setSelectedPrefillFields] = useState<string[]>(
    () => {
      // Check multiple possible property names for prefill field IDs
      const editedFieldTyped = editedField as Partial<FieldType> & {
        selectedPrefillFields?: string[];
        prefillFields?: string[];
      };
      return (
        editedField.prefillFieldIds ||
        editedFieldTyped.prefillFields ||
        editedFieldTyped.selectedPrefillFields ||
        []
      );
    }
  );
  const [optionSource] = useState<"custom" | "global">("custom");
  const [isGlobalField, setIsGlobalField] = useState(
    editedField.global ?? false
  );
  const [globalOptionsMap, setGlobalOptionsMap] = useState<
    Record<string, { id: string; value: string }[]>
  >({});
  const [prefillFields, setPrefillFields] = useState<FieldType[]>([]);

  // Interface for grouped fields by section
  interface GroupedField {
    stepName: string;
    stepId: string;
    sections: {
      sectionName: string;
      sectionId: string;
      fields: FieldType[];
    }[];
  }

  // Interface for grid column
  interface GridColumn {
    id: string;
    name: string;
    fieldType?: string;
    options?: { id: string; value: string }[];
    globals_name?: string;
    placeholder?: string;
    required?: boolean;
  }

  const [groupedPrefillFields, setGroupedPrefillFields] = useState<
    GroupedField[]
  >([]);
  const [loadingPrefillFields, setLoadingPrefillFields] =
    useState<boolean>(false);
  useEffect(() => {
    const fetchOptionsForGlobalSelects = async () => {
      const fieldsToFetch = conditions
        .map((cond) => allFields.find((f) => f.id === cond.fieldId))
        .filter(
          (f): f is FieldType =>
            !!f && f.field_type === "global_select" && !globalOptionsMap[f.id]
        );

      for (const field of fieldsToFetch) {
        try {
          const res = await getGlobalOptions({
            name: field.globals_name ?? "",
          });
          const fetchedOptions = res?.getGlobalByName ?? [];
          setGlobalOptionsMap((prev) => ({
            ...prev,
            [field.id]: fetchedOptions.map(
              (opt: { id?: string; value?: string }) => ({
                id: opt.id,
                value: opt.value,
              })
            ),
          }));
        } catch (err) {
          console.error("Failed to fetch global options for", field.label, err);
        }
      }
    };

    fetchOptionsForGlobalSelects();
  }, [conditions, allFields]);

  useEffect(() => {
    if (editedField.visibleIf) {
      setIsDependent(true);

      if (editedField.visibleIf.includes(" or ")) {
        setLogicType("OR");
      } else if (editedField.visibleIf.includes(" and ")) {
        setLogicType("AND");
      }

      const optionPattern = /'([^']+)'/g;
      const extractedOptions: string[] = [];
      let match = optionPattern.exec(editedField.visibleIf);
      while (match !== null) {
        extractedOptions.push(match[1]);
        match = optionPattern.exec(editedField.visibleIf);
      }
      setSelectedOptions(extractedOptions);

      if (
        editedField.logicConditions &&
        editedField.logicConditions.length > 0
      ) {
        setConditions(editedField.logicConditions);
      } else {
        try {
          const parsedConditions: LogicCondition[] = [];
          const conditionParts = editedField.visibleIf.split(
            logicType === "OR" ? " or " : " and "
          );

          conditionParts.forEach((part) => {
            const fieldMatch = part.match(/\{([^}]+)\}/);
            if (fieldMatch) {
              const fieldId = fieldMatch[1];
              const operatorMatch = part.match(/[=><]+/);
              const valueMatch = part.match(/'([^']+)'/);

              if (fieldId && operatorMatch && valueMatch) {
                parsedConditions.push({
                  fieldId,
                  operator:
                    operatorMatch[0] === "="
                      ? "==="
                      : operatorMatch[0] === "!="
                        ? "!=="
                        : operatorMatch[0] === ">"
                          ? ">"
                          : operatorMatch[0] === "<"
                            ? "<"
                            : operatorMatch[0] === ">="
                              ? ">="
                              : operatorMatch[0] === "<="
                                ? "<="
                                : "===",
                  value: valueMatch[1],
                  multiSelectLogic: "OR",
                });
              }
            }
          });

          if (parsedConditions.length > 0) {
            setConditions(parsedConditions);
          }
        } catch (error) {
          console.error("Error parsing conditions:", error);
        }
      }
    } else {
      setIsDependent(false);
      setSelectedOptions([]);
      setConditions([]);
    }
  }, [editedField.visibleIf, editedField.logicConditions]);

  // Initialize prefill and clone settings from editedField
  useEffect(() => {
    console.log("Initializing prefill settings from editedField:", editedField);

    // Initialize prefill settings
    if (editedField.prefillFieldIds && editedField.prefillFieldIds.length > 0) {
      console.log(
        "Found existing prefillFieldIds:",
        editedField.prefillFieldIds
      );
      setPrefillEnabled(true);
      setSelectedPrefillFields(editedField.prefillFieldIds);
    } else {
      setPrefillEnabled(editedField.prefillEnabled || false);
    }

    // Check if field has prefill identifier
    if (editedField.prefillIdentifier) {
      console.log(
        "Found existing prefillIdentifier:",
        editedField.prefillIdentifier
      );
      setIdentifierType(editedField.prefillIdentifier);
    }

    // Initialize clone settings
    setCloneEnabled((editedField as FieldTypeWithClone).cloneEnabled || false);
  }, [editedField]);

  // Auto-load prefill fields when editing existing field with prefill data
  useEffect(() => {
    if (prefillEnabled && identifierType && !prefillFields.length) {
      console.log(
        "Auto-loading prefill fields for identifier:",
        identifierType
      );
      // Call the function directly to avoid dependency issues
      const loadFields = async () => {
        try {
          // Use preserveSelection=true to keep existing selected fields during edit flow
          await handleIdentifierChange(identifierType, true);
        } catch (error) {
          console.error("Failed to auto-load prefill fields:", error);
        }
      };
      loadFields();
    }
  }, [prefillEnabled, identifierType, prefillFields.length]); // Run when these values change

  const handleIdentifierChange = async (
    Value: string,
    preserveSelection = false
  ) => {
    setIdentifierType(Value);
    handleFieldChange("prefillIdentifier", Value);
    setPrefillFields([]);

    // Only clear selected fields if not preserving selection (i.e., when user manually changes identifier)
    if (!preserveSelection) {
      setSelectedPrefillFields([]);
    }

    setLoadingPrefillFields(true);

    // Extract all fields from template based on identifier type
    const extractedFields: FieldType[] = [];
    const groupedFields: GroupedField[] = [];

    try {
      // Map identifier types to template IDs

      if (!Value) {
        console.warn(`No template ID found for identifier type: ${Value}`);
        return;
      }

      // Fetch the template based on identifier type
      const response = await getTemplate({
        filters: { key: Value, isActive: true },
        sortBy: "",
        sortOrder: "",
        search: "",
      });
      const template = response?.templates?.data?.templates[0];

      if (!template) {
        console.error("Invalid template response:", response);
        return;
      }

      // Parse template fields
      let templateSteps = [];
      const rawFields = template.fields;

      if (typeof rawFields === "string" && rawFields.trim()) {
        templateSteps = JSON.parse(rawFields);
      } else if (Array.isArray(rawFields)) {
        templateSteps = rawFields;
      } else {
        console.warn("No valid fields found in template");
        return;
      }

      // Define an interface for template steps
      interface TemplateStep {
        id: string;
        name?: string;
        sections?: {
          id: string;
          name?: string;
          fields?: FieldType[];
        }[];
      }

      // Extract fields from template steps
      (templateSteps as TemplateStep[]).forEach((step) => {
        if (step.sections) {
          const stepGroup: GroupedField = {
            stepName: step.name || `Step ${step.id}`,
            stepId: step.id,
            sections: [],
          };

          step.sections.forEach(
            (section: { id: string; name?: string; fields?: FieldType[] }) => {
              if (section.fields) {
                const sectionFields: FieldType[] = [];

                section.fields.forEach((field: FieldType) => {
                  // Include ALL fields except HTML and grid headers
                  if (
                    field.field_type !== "html" &&
                    field.field_type !== "grid"
                  ) {
                    const fieldData = {
                      id: field.id,
                      label: field.label,
                      field_type: field.field_type,
                      options: field.options || [],
                      stepId: step.id,
                      sourceFieldId: editedField.id,
                      globals_name: field.globals_name,
                      placeholder: field.placeholder,
                      required: field.required,
                      global: field.global,
                      sectionId: section.id,
                      stepName: step.name || `Step ${step.id}`,
                      sectionName: section.name || `Section ${section.id}`,
                    };

                    extractedFields.push(fieldData);
                    sectionFields.push(fieldData);
                  }

                  // Also include grid/table column fields
                  if (field.field_type === "grid" && field.columns) {
                    field.columns.forEach((column: GridColumn) => {
                      const columnField = {
                        id: `${field.id}_${column.id}`,
                        label: `${field.label} - ${column.name}`,
                        field_type: (column.fieldType ||
                          "text") as FieldType["field_type"],
                        options: column.options || [],
                        stepId: step.id,
                        sourceFieldId: editedField.id,
                        globals_name: column.globals_name || "",
                        placeholder: column.placeholder || "",
                        required: column.required || false,
                        global: false,
                        sectionId: section.id,
                        stepName: step.name || `Step ${step.id}`,
                        sectionName: section.name || `Section ${section.id}`,
                        parentGridId: field.id,
                        columnId: column.id,
                        isGridColumn: true,
                      };

                      extractedFields.push(columnField);
                      sectionFields.push(columnField);
                    });
                  }
                });

                if (sectionFields.length > 0) {
                  stepGroup.sections.push({
                    sectionName: section.name || `Section ${section.id}`,
                    sectionId: section.id,
                    fields: sectionFields,
                  });
                }
              }
            }
          );

          if (stepGroup.sections.length > 0) {
            groupedFields.push(stepGroup);
          }
        }
      });

      setPrefillFields(extractedFields);
      setGroupedPrefillFields(groupedFields);
    } catch (error) {
      console.error(`Error fetching ${Value} template:`, error);
      setPrefillFields([]);
      setGroupedPrefillFields([]);
    } finally {
      setLoadingPrefillFields(false);
    }
  };

  // function field(value: FieldType): value is FieldType {
  // 	return Array.isArray(value.options) && value.options.length > 0;
  // }

  const handleAddOption = () => {
    const newOptions = [...(editedField.options || [])];
    newOptions.push({
      id: `option_${Date.now()}`,
      value: `Option ${newOptions.length + 1}`,
    });
    handleFieldChange("options", newOptions);
  };

  const handleOptionChange = (index: number, value: string) => {
    const newOptions = [...(editedField.options || [])];
    newOptions[index] = { ...newOptions[index], value };
    handleFieldChange("options", newOptions);
  };

  const handleDeleteOption = (index: number) => {
    const newOptions = [...(editedField.options || [])];
    newOptions.splice(index, 1);
    handleFieldChange("options", newOptions);
  };

  const updateVisibleIfFromConditions = (
    updatedConditions: LogicCondition[]
  ) => {
    if (updatedConditions.length === 0) {
      handleFieldChange("visibleIf", "");
      handleFieldChange("logicConditions", []);
      handleFieldChange("logicJoinType", undefined);
      return;
    }

    handleFieldChange("logicConditions", updatedConditions);
    handleFieldChange("logicJoinType", logicType);
    const conditionStrings = updatedConditions.map((cond) => {
      const fieldRef = `{${cond.fieldId}}`;
      const selectedField = allFields.find((f) => f.id === cond.fieldId);

      if (
        selectedField &&
        (selectedField.field_type === "multiselect" ||
          selectedField.field_type === "checkboxes")
      ) {
        if (cond.value && cond.value.includes(",")) {
          const values = cond.value.split(",");
          const valueConditions = values.map(
            (val) => `${fieldRef} contains '${val}'`
          );
          const joinOperator =
            cond.multiSelectLogic === "AND" ? " and " : " or ";
          return `(${valueConditions.join(joinOperator)})`;
        } else {
          return `${fieldRef} contains '${cond.value}'`;
        }
      } else {
        const operator = cond.operator === "===" ? "=" : cond.operator;
        return `${fieldRef} ${operator} '${cond.value}'`;
      }
    });

    const joinOperator = logicType === "OR" ? " or " : " and ";
    const visibleIfString = conditionStrings.join(joinOperator);

    handleFieldChange("visibleIf", visibleIfString);
  };

  // function setSteps(updatedSteps: any) {
  //   throw new Error("Function not implemented.");
  // }

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      {(editedField.field_type === "select" ||
        editedField.field_type === "multiselect" ||
        editedField.field_type === "checkboxes") && (
        <Box>
          {/* <FormGroup row sx={{ mb: 2 }}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={optionSource === "custom"}
                  onChange={() => {
                    setOptionSource("custom");
                  }}
                />
              }
              label="Custom"
            />
          
          </FormGroup> */}

          {
            optionSource === "custom" ? (
              <>
                <Box
                  display="flex"
                  justifyContent="space-between"
                  alignItems="center"
                  mb={1}
                >
                  <Typography fontSize={14} fontWeight={600}>
                    Options
                  </Typography>
                  <Button
                    text="Add Option"
                    className="buttonstyle bg-teal-500 hover:bg-teal-600"
                    onClick={handleAddOption}
                  >
                    Add Option
                  </Button>
                </Box>

                <Box display="flex" flexDirection="column" gap={1}>
                  {editedField.options?.map((option, index) => (
                    <Box
                      key={option.id}
                      display="flex"
                      gap={1}
                      flexWrap="nowrap"
                    >
                      <TextField
                        fullWidth
                        size="small"
                        value={option.value}
                        onChange={(e) =>
                          handleOptionChange(index, e.target.value)
                        }
                      />
                      <IconButton
                        color="error"
                        size="small"
                        onClick={() => handleDeleteOption(index)}
                      >
                        <Delete fontSize="small" />
                      </IconButton>
                    </Box>
                  ))}
                </Box>
                {editedField.field_type === "select" && (
                  <FormControlLabel
                    className="pt-5"
                    control={
                      <Checkbox
                        checked={isGlobalField}
                        onChange={(e) => {
                          const checked = e.target.checked;
                          setIsGlobalField(checked);
                          handleFieldChange("global", checked);
                        }}
                      />
                    }
                    label={
                      <span
                        style={{
                          display: "flex",
                          alignItems: "center",
                          gap: 4,
                        }}
                      >
                        Add To Global Fields
                        <InfoIcon title="Mark this field as global so it can be reused across forms." />
                      </span>
                    }
                  />
                )}
              </>
            ) : null
            // 			 <Box>
            //   <InputLabel className="lable-color">
            //     Select Global Field
            //   </InputLabel>
            //   <TextField
            //     select
            //     fullWidth
            //     value={selectedGlobalFieldId || ""}
            //     onChange={(e) => {
            //       const selectedId = e.target.value;
            //       setSelectedGlobalFieldId(selectedId);
            //       const selectedField = globalFields.find(
            //         (field) => field.id === selectedId,
            //       );
            //       if (selectedField?.options) {
            //         handleFieldChange("options", selectedField.options);
            //       }
            //     }}
            //     sx={{ mb: 2 }}
            //   >
            //     {globalFieldsx
            //       .filter(field)
            //       .map((field) => (
            //         <MenuItem key={field.id} value={field.id}>
            //           {field.label}
            //         </MenuItem>
            //       ))}
            //   </TextField>

            //   <Box display="flex" flexDirection="column" gap={1}>
            //     {editedField.options?.map((option) => (
            //       <Typography key={option.id}>• {option.value}</Typography>
            //     ))}
            //   </Box>
            // </Box>
          }
        </Box>
      )}

      <FormControlLabel
        control={
          <Switch
            checked={isDependent}
            onChange={(e) => {
              const checked = e.target.checked;
              setIsDependent(checked);
              if (!checked) {
                handleFieldChange("visibleIf", "");
                handleFieldChange("logicConditions", []);
                handleFieldChange("logicJoinType", undefined);
                setConditions([]);
              } else {
                handleFieldChange("visibleIf", `{placeholder} = 'value'`);
              }
            }}
          />
        }
        label={
          <span style={{ display: "flex", alignItems: "center", gap: 4 }}>
            Field Dependency
            <InfoIcon title="Show this field based on a condition" />
          </span>
        }
      />

      {isDependent && (
        <>
          <Box>
            <InputLabel className="lable-color">Condition Logic</InputLabel>
            <TextField
              select
              onChange={(e) => {
                const newLogicType = e.target.value as "AND" | "OR";
                setLogicType(newLogicType);

                if (conditions.length > 1) {
                  updateVisibleIfFromConditions(conditions);
                }
              }}
              value={logicType}
              fullWidth
              sx={{ mb: 2 }}
            >
              <MenuItem value="OR">Any condition matches (OR)</MenuItem>
              <MenuItem value="AND">All conditions match (AND)</MenuItem>
            </TextField>
          </Box>

          <Box display="flex" justifyContent="flex-end" alignItems="center">
            <Button
              text="Add Condition"
              className="buttonstyle bg-teal-500 hover:bg-teal-600"
              onClick={() => {
                const newCondition: LogicCondition = {
                  fieldId: "",
                  operator: "===",
                  value: "",
                  multiSelectLogic: "OR",
                };
                const updatedConditions = [...conditions, newCondition];
                setConditions(updatedConditions);
              }}
            >
              Add Condition
            </Button>
          </Box>

          {conditions.map((cond, idx) => (
            <Box key={idx} display="flex" gap={2} alignItems="center" mb={2}>
              <Box sx={{ flexGrow: 1 }}>
                <InputLabel className="lable-color">Field</InputLabel>
                <TextField
                  select
                  value={cond.fieldId}
                  onChange={(e) => {
                    const fieldId = e.target.value;
                    const updatedConditions = [...conditions];
                    updatedConditions[idx].fieldId = fieldId;

                    const selectedField = allFields.find(
                      (f) => f.id === fieldId
                    );
                    if (selectedField) {
                      if (selectedField.field_type === "number") {
                        updatedConditions[idx].operator = ">";
                        updatedConditions[idx].value = "0";
                      } else if (
                        selectedField.field_type === "select" ||
                        selectedField.field_type === "multiselect" ||
                        selectedField.field_type === "checkboxes" ||
                        selectedField.field_type === "global_select"
                      ) {
                        updatedConditions[idx].operator = "===";
                        updatedConditions[idx].value =
                          selectedField.options?.[0]?.value || "";

                        if (
                          selectedField.field_type === "multiselect" ||
                          selectedField.field_type === "checkboxes"
                        ) {
                          updatedConditions[idx].multiSelectLogic = "OR";
                        }
                      } else {
                        updatedConditions[idx].operator = "===";
                        updatedConditions[idx].value = "value";
                      }
                    }

                    setConditions(updatedConditions);
                    updateVisibleIfFromConditions(updatedConditions);
                  }}
                  sx={{
                    minWidth: 180,
                    maxWidth: 200,

                    "& .MuiSelect-icon": {
                      right: "10px !important",
                    },
                  }}
                  SelectProps={{
                    MenuProps: {
                      PaperProps: {
                        sx: {
                          maxHeight: "12rem",
                          overflowY: "auto",
                        },
                      },
                    },
                  }}
                >
                  {allFields
                    .filter((f) => f.id !== editedField.id)
                    .map((f) => (
                      <MenuItem key={f.id} value={f.id}>
                        {f.label}
                      </MenuItem>
                    ))}
                </TextField>
              </Box>

              {(() => {
                const selectedField = allFields.find(
                  (f) => f.id === cond.fieldId
                );

                if (
                  selectedField &&
                  (selectedField.field_type === "multiselect" ||
                    selectedField.field_type === "checkboxes")
                ) {
                  return (
                    <Box sx={{ flexGrow: 1, minWidth: 120 }}>
                      <InputLabel className="lable-color">Logic</InputLabel>
                      <TextField
                        select
                        value={cond.multiSelectLogic || "OR"}
                        onChange={(e) => {
                          const updatedConditions = [...conditions];
                          updatedConditions[idx].multiSelectLogic = e.target
                            .value as "AND" | "OR";
                          setConditions(updatedConditions);
                          updateVisibleIfFromConditions(updatedConditions);
                        }}
                        sx={{
                          minWidth: 120,
                          "& .MuiSelect-icon": {
                            right: "10px !important",
                          },
                        }}
                      >
                        <MenuItem value="OR">Any (OR)</MenuItem>
                        <MenuItem value="AND">All (AND)</MenuItem>
                      </TextField>
                    </Box>
                  );
                }

                if (!selectedField) {
                  return (
                    <Box sx={{ flexGrow: 1, minWidth: 120 }}>
                      <InputLabel className="lable-color">Operator</InputLabel>
                      <TextField
                        select
                        value={cond.operator}
                        onChange={(e) => {
                          const updatedConditions = [...conditions];
                          updatedConditions[idx].operator = e.target.value as
                            | "==="
                            | "!=="
                            | ">"
                            | "<"
                            | ">="
                            | "<=";
                          setConditions(updatedConditions);
                          updateVisibleIfFromConditions(updatedConditions);
                        }}
                        sx={{
                          minWidth: 120,
                          "& .MuiSelect-icon": {
                            right: "10px !important",
                          },
                        }}
                      >
                        <MenuItem value="===">=</MenuItem>
                        <MenuItem value="!==">!=</MenuItem>
                      </TextField>
                    </Box>
                  );
                }

                if (selectedField.field_type === "number") {
                  return (
                    <Box sx={{ flexGrow: 1, minWidth: 120 }}>
                      <InputLabel className="lable-color">Operator</InputLabel>
                      <TextField
                        select
                        value={cond.operator}
                        onChange={(e) => {
                          const updatedConditions = [...conditions];
                          updatedConditions[idx].operator = e.target.value as
                            | "==="
                            | "!=="
                            | ">"
                            | "<"
                            | ">="
                            | "<=";
                          setConditions(updatedConditions);
                          updateVisibleIfFromConditions(updatedConditions);
                        }}
                        sx={{
                          minWidth: 120,
                          "& .MuiSelect-icon": {
                            right: "10px !important",
                          },
                        }}
                      >
                        <MenuItem value=">">&gt;</MenuItem>
                        <MenuItem value="<">&lt;</MenuItem>
                        <MenuItem value="===">=</MenuItem>
                        <MenuItem value="!==">!=</MenuItem>
                        <MenuItem value=">=">&gt;=</MenuItem>
                        <MenuItem value="<=">&lt;=</MenuItem>
                      </TextField>
                    </Box>
                  );
                }

                if (selectedField.field_type === "select") {
                  return (
                    <Box sx={{ flexGrow: 1, minWidth: 120 }}>
                      <InputLabel className="lable-color">Operator</InputLabel>
                      <TextField
                        select
                        value={cond.operator}
                        onChange={(e) => {
                          const updatedConditions = [...conditions];
                          updatedConditions[idx].operator = e.target.value as
                            | "==="
                            | "!=="
                            | ">"
                            | "<"
                            | ">="
                            | "<=";
                          setConditions(updatedConditions);
                          updateVisibleIfFromConditions(updatedConditions);
                        }}
                        sx={{
                          minWidth: 120,
                          "& .MuiSelect-icon": {
                            right: "10px !important",
                          },
                        }}
                      >
                        <MenuItem value="===">=</MenuItem>
                        <MenuItem value="!==">!=</MenuItem>
                      </TextField>
                    </Box>
                  );
                }

                return (
                  <Box sx={{ flexGrow: 1, minWidth: 120 }}>
                    <InputLabel className="lable-color">Operator</InputLabel>
                    <TextField
                      select
                      value={cond.operator}
                      onChange={(e) => {
                        const updatedConditions = [...conditions];
                        updatedConditions[idx].operator = e.target.value as
                          | "==="
                          | "!=="
                          | ">"
                          | "<"
                          | ">="
                          | "<=";
                        setConditions(updatedConditions);
                        updateVisibleIfFromConditions(updatedConditions);
                      }}
                      sx={{
                        minWidth: 120,
                        "& .MuiSelect-icon": {
                          right: "10px !important",
                        },
                      }}
                    >
                      <MenuItem value="===">=</MenuItem>
                      <MenuItem value="!==">!=</MenuItem>
                    </TextField>
                  </Box>
                );
              })()}

              {/* Value Input */}
              {/* {(() => {
                const selectedField = allFields.find(
                  (f) => f.id === cond.fieldId
                );

                if (!selectedField) {
                  return (
                    <Box sx={{ flexGrow: 1, minWidth: 120 }}>
                      <InputLabel className="lable-color">Value</InputLabel>
                      <TextField
                        value={cond.value}
                        onChange={(e) => {
                          const updatedConditions = [...conditions];
                          updatedConditions[idx].value = e.target.value;
                          setConditions(updatedConditions);
                          updateVisibleIfFromConditions(updatedConditions);
                        }}
                        sx={{ flexGrow: 1, minWidth: 120 }}
                      />
                    </Box>
                  );
                }

                if (selectedField.field_type === "  ") {
                  return (
                    <Box sx={{ flexGrow: 1, minWidth: 120 }}>
                      <InputLabel className="lable-color">Value</InputLabel>
                      <TextField
                        label="Value"
                        type="number"
                        value={cond.value}
                        onChange={(e) => {
                          const updatedConditions = [...conditions];
                          updatedConditions[idx].value = e.target.value;
                          setConditions(updatedConditions);
                          updateVisibleIfFromConditions(updatedConditions);
                        }}
                        sx={{ flexGrow: 1, minWidth: 120 }}
                      />
                    </Box>
                  );
                }

                if (
                  selectedField.field_type === "select" ||
                  selectedField.field_type === "multiselect" ||
                  selectedField.field_type === "checkboxes" ||
                  selectedField.field_type === "global_select"
                ) {
                  if (
                    selectedField.field_type === "multiselect" ||
                    selectedField.field_type === "checkboxes"
                  ) {
                    const selectedField = allFields.find(
                      (f) => f.id === cond.fieldId
                    );

                    console.log("globalOptionsMap", globalOptionsMap);

                    const effectiveOptions =
                      selectedField?.field_type === "global_select"
                        ? (globalOptionsMap[selectedField.id] ?? [])
                        : (selectedField?.options ?? []);
                    return (
                      <Box sx={{ flexGrow: 1, minWidth: 120 }}>
                        <InputLabel className="lable-color">Value</InputLabel>
                        <TextField
                          select
                          value={cond.value ? cond.value.split(",") : []}
                          onChange={(e) => {
                            const updatedConditions = [...conditions];
                            const selectedValues = Array.isArray(e.target.value)
                              ? e.target.value.join(",")
                              : e.target.value;
                            updatedConditions[idx].value = selectedValues;
                            setConditions(updatedConditions);
                            updateVisibleIfFromConditions(updatedConditions);
                          }}
                          sx={{
                            flexGrow: 1,
                            minWidth: 120,
                            "& .MuiSelect-icon": {
                              right: "10px !important",
                            },
                          }}
                          SelectProps={{
                            multiple: true,
                            renderValue: (selected) =>
                              (Array.isArray(selected)
                                ? selected.join(", ")
                                : selected) as React.ReactNode,
                          }}
                        >
                          {effectiveOptions?.map((option) => (
                            <MenuItem
                              className="multiselect-Style"
                              key={option.id}
                              value={option.value}
                            >
                              <Checkbox
                                checked={
                                  cond.value
                                    ? cond.value
                                        .split(",")
                                        .includes(option.value)
                                    : false
                                }
                              />
                              <ListItemText primary={option.value} />
                            </MenuItem>
                          ))}
                        </TextField>
                      </Box>
                    );
                  }

                  return (
                    <Box sx={{ flexGrow: 1, minWidth: 120 }}>
                      <InputLabel className="lable-color">Value</InputLabel>
                      <TextField
                        select
                        value={cond.value}
                        onChange={(e) => {
                          const updatedConditions = [...conditions];
                          updatedConditions[idx].value = e.target.value;
                          setConditions(updatedConditions);
                          updateVisibleIfFromConditions(updatedConditions);
                        }}
                        sx={{
                          flexGrow: 1,
                          minWidth: 120,
                          "& .MuiSelect-icon": {
                            right: "10px !important",
                          },
                        }}
                      >
                        {selectedField.options?.map((option) => (
                          <MenuItem key={option.id} value={option.value}>
                            {option.value}
                          </MenuItem>
                        ))}
                      </TextField>
                    </Box>
                  );
                }

                return (
                  <Box sx={{ flexGrow: 1, minWidth: 120 }}>
                    <InputLabel className="lable-color">Value</InputLabel>
                    <TextField
                      value={cond.value}
                      onChange={(e) => {
                        const updatedConditions = [...conditions];
                        updatedConditions[idx].value = e.target.value;
                        setConditions(updatedConditions);
                        updateVisibleIfFromConditions(updatedConditions);
                      }}
                      sx={{ flexGrow: 1, minWidth: 120 }}
                    />
                  </Box>
                );
              })()} */}

              {(() => {
                const selectedField = allFields.find(
                  (f) => f.id === cond.fieldId
                );

                if (!selectedField) {
                  return (
                    <Box sx={{ flexGrow: 1, minWidth: 120 }}>
                      <InputLabel className="lable-color">Value</InputLabel>
                      <TextField
                        value={cond.value}
                        onChange={(e) => {
                          const updatedConditions = [...conditions];
                          updatedConditions[idx].value = e.target.value;
                          setConditions(updatedConditions);
                          updateVisibleIfFromConditions(updatedConditions);
                        }}
                        sx={{ flexGrow: 1, minWidth: 120 }}
                      />
                    </Box>
                  );
                }

                if (selectedField.field_type === "number") {
                  return (
                    <Box sx={{ flexGrow: 1, minWidth: 120 }}>
                      <InputLabel className="lable-color">Value</InputLabel>
                      <TextField
                        label="Value"
                        type="number"
                        value={cond.value}
                        onChange={(e) => {
                          const updatedConditions = [...conditions];
                          updatedConditions[idx].value = e.target.value;
                          setConditions(updatedConditions);
                          updateVisibleIfFromConditions(updatedConditions);
                        }}
                        sx={{ flexGrow: 1, minWidth: 120 }}
                      />
                    </Box>
                  );
                }

                if (
                  selectedField.field_type === "select" ||
                  selectedField.field_type === "multiselect" ||
                  selectedField.field_type === "checkboxes" ||
                  selectedField.field_type === "global_select"
                ) {
                  if (
                    selectedField.field_type === "multiselect" ||
                    selectedField.field_type === "checkboxes"
                  ) {
                    const effectiveOptions = selectedField.options ?? [];
                    return (
                      <Box sx={{ flexGrow: 1, minWidth: 120 }}>
                        <InputLabel className="lable-color">Value</InputLabel>
                        <TextField
                          select
                          value={cond.value ? cond.value.split(",") : []}
                          onChange={(e) => {
                            const updatedConditions = [...conditions];
                            const selectedValues = Array.isArray(e.target.value)
                              ? e.target.value.join(",")
                              : e.target.value;
                            updatedConditions[idx].value = selectedValues;
                            setConditions(updatedConditions);
                            updateVisibleIfFromConditions(updatedConditions);
                          }}
                          sx={{
                            flexGrow: 1,
                            minWidth: 120,
                            "& .MuiSelect-icon": {
                              right: "10px !important",
                            },
                          }}
                          SelectProps={{
                            multiple: true,
                            renderValue: (selected) =>
                              (Array.isArray(selected)
                                ? selected.join(", ")
                                : selected) as React.ReactNode,
                            MenuProps: {
                              PaperProps: {
                                sx: {
                                  maxHeight: "12rem",
                                  overflowY: "auto",
                                },
                              },
                            },
                          }}
                        >
                          {effectiveOptions?.map((option) => (
                            <MenuItem
                              className="multiselect-Style"
                              key={option.id}
                              value={option.value}
                            >
                              <Checkbox
                                checked={
                                  cond.value
                                    ? cond.value
                                        .split(",")
                                        .includes(option.value)
                                    : false
                                }
                              />
                              <ListItemText primary={option.value} />
                            </MenuItem>
                          ))}
                        </TextField>
                      </Box>
                    );
                  }

                  // This is the part that handles global_select (single select)
                  const effectiveOptions =
                    selectedField.field_type === "global_select"
                      ? (globalOptionsMap[selectedField.id] ?? [])
                      : (selectedField.options ?? []);

                  return (
                    <Box sx={{ flexGrow: 1, minWidth: 180 }}>
                      <InputLabel className="lable-color">Value</InputLabel>
                      <TextField
                        select
                        value={cond.value}
                        onChange={(e) => {
                          const updatedConditions = [...conditions];
                          updatedConditions[idx].value = e.target.value;
                          setConditions(updatedConditions);
                          updateVisibleIfFromConditions(updatedConditions);
                        }}
                        sx={{
                          flexGrow: 1,
                          minWidth: 180,
                          "& .MuiSelect-icon": {
                            right: "10px !important",
                          },
                        }}
                        SelectProps={{
                          MenuProps: {
                            PaperProps: {
                              sx: {
                                maxHeight: "12rem",
                                overflowY: "auto",
                              },
                            },
                          },
                        }}
                      >
                        {effectiveOptions?.map((option) => (
                          <MenuItem key={option.id} value={option.value}>
                            {option.value}
                          </MenuItem>
                        ))}
                      </TextField>
                    </Box>
                  );
                }

                return (
                  <Box sx={{ flexGrow: 1, minWidth: 120 }}>
                    <InputLabel className="lable-color">Value</InputLabel>
                    <TextField
                      value={cond.value}
                      onChange={(e) => {
                        const updatedConditions = [...conditions];
                        updatedConditions[idx].value = e.target.value;
                        setConditions(updatedConditions);
                        updateVisibleIfFromConditions(updatedConditions);
                      }}
                      sx={{ flexGrow: 1, minWidth: 120 }}
                    />
                  </Box>
                );
              })()}

              <Box
                display="flex"
                alignItems="end"
                justifyContent="center"
                height={"4rem"}
              >
                <IconButton
                  color="error"
                  onClick={() => {
                    const updatedConditions = conditions.filter(
                      (_, i) => i !== idx
                    );
                    setConditions(updatedConditions);
                    updateVisibleIfFromConditions(updatedConditions);
                  }}
                >
                  <Delete />
                </IconButton>
              </Box>
            </Box>
          ))}

          <Typography variant="caption" color="textSecondary">
            {editedField.visibleIf ? (
              <>
                Current condition: <code>{editedField.visibleIf}</code>
              </>
            ) : (
              "Add conditions to create dependency rules"
            )}
          </Typography>
        </>
      )}
      {editedField.field_type !== "grid" && (
        <FormControlLabel
          control={
            <Switch
              checked={!!prefillEnabled}
              onChange={(e) => {
                const checked = e.target.checked;
                setPrefillEnabled(checked);
                handleFieldChange("prefillEnabled", checked);
                if (!checked) {
                  // Clear prefill data when disabled
                  setIdentifierType("");
                  setSelectedPrefillFields([]);
                  handleFieldChange("prefillIdentifier", "");
                  handleFieldChange("prefillFieldIds", []);
                }
              }}
            />
          }
          label={
            <span style={{ display: "flex", alignItems: "center", gap: 4 }}>
              Data Prefill
              <InfoIcon title="Automatically populate this field using selected data" />
            </span>
          }
        />
      )}

      {prefillEnabled && (
        <Box display="flex" flexDirection="column" gap={2}>
          <InputLabel className="customdropdown">Identifier</InputLabel>
          <FormControl fullWidth>
            <Select
              value={identifierType || ""}
              onChange={(e) => handleIdentifierChange(e.target.value)}
            >
              <MenuItem value="provider">Provider ID</MenuItem>
              <MenuItem value="sub-organization">Group ID</MenuItem>
              <MenuItem value="payer">Payer ID</MenuItem>
            </Select>
          </FormControl>

          {(groupedPrefillFields.length > 0 || loadingPrefillFields) && (
            <Box sx={{ minWidth: 180 }}>
              <InputLabel>Select Fields to Prefill</InputLabel>
              <Select
                multiple
                fullWidth
                value={selectedPrefillFields}
                onChange={(e) => {
                  const selected = e.target.value as string[];
                  setSelectedPrefillFields(selected);

                  // Save the selected field IDs to the field
                  handleFieldChange("prefillFieldIds", selected);

                  // Get the selected fields with their complete data
                  const selectedFieldsData = prefillFields
                    .filter((field) => selected.includes(field.id))
                    .map((field) => ({
                      field_type: field.field_type,
                      id: field.id,
                      identifierType: identifierType,
                      label: field.label,
                      options: field.options || [],
                      prefilled: true,
                      sectionId: field.sectionId,
                      stepId: field.stepId,
                      sourceFieldId: editedField.id,
                      globals_name: field.globals_name
                        ? field.globals_name
                        : "",
                      placeholder: field.placeholder,
                      required: field.required,
                      global: field.global,
                    }));

                  if (steps && setSteps) {
                    const currentStepIndex = steps.findIndex((step) =>
                      step.sections.some((section) =>
                        section.fields.some(
                          (field) => field.id === editedField.id
                        )
                      )
                    );

                    if (currentStepIndex >= 0) {
                      const step = steps[currentStepIndex];
                      const sectionIndex = step.sections.findIndex((section) =>
                        section.fields.some(
                          (field) => field.id === editedField.id
                        )
                      );

                      if (sectionIndex >= 0) {
                        const newSteps = [...steps];

                        // Get non-prefilled fields and the current field
                        const regularFields = newSteps[
                          currentStepIndex
                        ].sections[sectionIndex].fields.filter(
                          (field) =>
                            !field.prefilled || field.id === editedField.id
                        );

                        // Get prefilled fields from other source fields (not from the current field)
                        const otherPrefillFields = newSteps[
                          currentStepIndex
                        ].sections[sectionIndex].fields.filter(
                          (field) =>
                            field.prefilled &&
                            field.sourceFieldId !== editedField.id
                        );

                        // Combine regular fields, other prefill fields, and new prefill fields
                        newSteps[currentStepIndex].sections[
                          sectionIndex
                        ].fields = [
                          ...regularFields,
                          ...otherPrefillFields,
                          ...selectedFieldsData,
                        ];

                        setSteps(newSteps);
                        handleFieldChange("prefillFieldIds", selected);
                      }
                    }
                  }
                }}
                renderValue={(selected) => {
                  return (selected as string[])
                    .map((id) => {
                      const field = prefillFields.find((f) => f.id === id);
                      return field ? field.label : "";
                    })
                    .filter((label) => label)
                    .join(", ");
                }}
              >
                {loadingPrefillFields ? (
                  <MenuItem disabled>
                    <em>Loading fields...</em>
                  </MenuItem>
                ) : groupedPrefillFields.length === 0 ? (
                  <MenuItem disabled>
                    <em>No fields available for prefill</em>
                  </MenuItem>
                ) : (
                  groupedPrefillFields
                    .map((step) => [
                      <ListSubheader
                        key={`step-${step.stepId}`}
                        sx={{
                          backgroundColor: "#f3f4f6",
                          fontWeight: "bold",
                          color: "#1f2937",
                        }}
                      >
                        {step.stepName}
                      </ListSubheader>,
                      ...step.sections
                        .map((section) => [
                          <ListSubheader
                            key={`section-${section.sectionId}`}
                            sx={{
                              backgroundColor: "#f9fafb",
                              fontWeight: "600",
                              color: "#374151",
                              paddingLeft: "24px",
                            }}
                          >
                            {section.sectionName}
                          </ListSubheader>,
                          ...section.fields.map((field) => (
                            <MenuItem
                              key={field.id}
                              value={field.id}
                              sx={{ paddingLeft: "48px" }}
                            >
                              <Checkbox
                                checked={selectedPrefillFields.includes(
                                  field.id
                                )}
                              />
                              <ListItemText
                                primary={
                                  <div
                                    style={{
                                      display: "flex",
                                      alignItems: "center",
                                      gap: "8px",
                                    }}
                                  >
                                    <span>{field.label}</span>
                                  </div>
                                }
                              />
                            </MenuItem>
                          )),
                        ])
                        .flat(),
                    ])
                    .flat()
                )}
              </Select>
            </Box>
          )}
        </Box>
      )}

      <FormControlLabel
        control={
          <Switch
            checked={cloneEnabled}
            onChange={(e) => {
              const checked = e.target.checked;
              setCloneEnabled(checked);
              // Store clone enabled state in field (using any to extend the type)
              (editedField as FieldTypeWithClone).cloneEnabled = checked;
            }}
          />
        }
        label={
          <span style={{ display: "flex", alignItems: "center", gap: 4 }}>
            Field Clone
            <InfoIcon title="Clone this field to another step and section with bi-directional sync." />
          </span>
        }
      />

      {/* Conditionally render the cloning UI */}
      {cloneEnabled && (
        <>
          <Typography fontWeight={600} fontSize={15} mb={1}>
            Field Clone
          </Typography>
          <Box
            display="flex"
            flexDirection="row"
            alignItems="center"
            gap={2}
            mt={1}
          >
            <Box sx={{ minWidth: 180 }}>
              <InputLabel>Select Step</InputLabel>
              <TextField
                select
                value={selectedCloneStep}
                onChange={(e) => {
                  setSelectedCloneStep(e.target.value);
                  const step = steps.find((s) => s.id === e.target.value);
                  if (step?.sections?.[0]) {
                    setSelectedCloneSection(step.sections[0].id);
                  } else {
                    setSelectedCloneSection("");
                  }
                }}
                fullWidth
                sx={{ mb: 2 }}
                SelectProps={{
                  displayEmpty: true,
                }}
              >
                <MenuItem disabled value="">
                  Select Step
                </MenuItem>
                {steps.map((step) => (
                  <MenuItem key={step.id} value={step.id}>
                    {step.name}
                  </MenuItem>
                ))}
              </TextField>
            </Box>
            <Box sx={{ minWidth: 180 }}>
              <InputLabel>Select Section</InputLabel>
              <TextField
                select
                value={selectedCloneSection}
                onChange={(e) => setSelectedCloneSection(e.target.value)}
                fullWidth
                sx={{ mb: 2 }}
                disabled={!selectedCloneStep}
                SelectProps={{
                  displayEmpty: true,
                }}
              >
                <MenuItem disabled value="">
                  Select Section
                </MenuItem>
                {steps
                  .find((step) => step.id === selectedCloneStep)
                  ?.sections.map((section) => (
                    <MenuItem key={section.id} value={section.id}>
                      {section.name}
                    </MenuItem>
                  ))}
              </TextField>
            </Box>
            <Button
              text="Clone Field"
              className="buttonstyle bg-teal-500 hover:bg-teal-600"
              onClick={() => {
                if (!selectedCloneStep || !selectedCloneSection) return;
                const clonedField = {
                  ...editedField,
                  id: `field-${Date.now()}`,
                  label: `${editedField.label} (Clone)`,
                  cloneOf: editedField.id,
                };

                const updatedSteps = steps.map((step) => {
                  if (step.id !== selectedCloneStep) return step;
                  return {
                    ...step,
                    sections: step.sections.map((section) => {
                      if (section.id !== selectedCloneSection) return section;
                      return {
                        ...section,
                        fields: [...section.fields, clonedField],
                      };
                    }),
                  };
                });

                setSteps(updatedSteps);
              }}
            >
              Clone Field
            </Button>
          </Box>
        </>
      )}
    </Box>
  );
};
