import React, { useState } from "react";
import {
  IconButton,
  Menu,
  MenuItem,
  Checkbox,
  ListItemIcon,
  ListItemText,
} from "@mui/material";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import VisibilityIcon from "@mui/icons-material/Visibility";
import TuneIcon from "@mui/icons-material/Tune";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";
import { getSubModulePermissionCommon } from "@/utils/generic";

const FieldActionsMenu = ({
  onEdit,
  onDelete,
  onShowCustomFields,
  hiddenFieldsChecked,
  onToggleHiddenFields,
}: {
  onEdit?: () => void;
  onDelete?: () => void;
  onShowCustomFields?: () => void;
  hiddenFieldsChecked?: boolean;
  onToggleHiddenFields?: () => void;
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <>
     {getSubModulePermissionCommon('Masters','Des Form','Edit Template')?.isEnabled && <IconButton size="small" onClick={handleMenuClick}>
        <MoreVertIcon fontSize="small" />
      </IconButton>}
      <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
        {onEdit && (
          <MenuItem
            onClick={() => {
              handleClose();
              onEdit();
            }}
          >
            <ListItemIcon>
              <EditIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Edit</ListItemText>
          </MenuItem>
        )}
        {onDelete && (
          <MenuItem
            onClick={() => {
              handleClose();
              onDelete();
            }}
          >
            <ListItemIcon>
              <DeleteIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Delete</ListItemText>
          </MenuItem>
        )}
        {typeof hiddenFieldsChecked === "boolean" && onToggleHiddenFields && (
          <MenuItem
            onClick={() => {
              handleClose();
              onToggleHiddenFields();
            }}
          >
            <ListItemIcon>
              <Checkbox
                edge="start"
                checked={hiddenFieldsChecked}
                tabIndex={-1}
                disableRipple
                icon={<VisibilityOffIcon fontSize="small" />}
                checkedIcon={<VisibilityIcon fontSize="small" />}
                inputProps={{ "aria-label": "Hidden Fields" }}
                onClick={(e) => {
                  e.stopPropagation();
                  onToggleHiddenFields();
                }}
              />
            </ListItemIcon>
            <ListItemText>Show Hidden Fields</ListItemText>
          </MenuItem>
        )}
        {onShowCustomFields && (
          <MenuItem
            onClick={() => {
              handleClose();
              onShowCustomFields();
            }}
          >
            <ListItemIcon>
              <TuneIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>View Custom Fields</ListItemText>
          </MenuItem>
        )}
      </Menu>
    </>
  );
};

export default FieldActionsMenu;
