import { Box, Drawer, IconButton, Typography } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { CustomFieldRenderer } from "./customfieldRender";

interface CustomFormField {
  id: string | number;
  label: string;
  field_type:
    | "number"
    | "grid"
    | "html"
    | "select"
    | "textarea"
    | "time"
    | "image"
    | "text"
    | "phone"
    | "email"
    | "multiselect"
    | "toggle"
    | "date"
    | "datetime"
    | "file_upload"
    | "checkboxes"
    | "global_select";
}

interface CustomFieldDrawerProps {
  open: boolean;
  onClose: () => void;
  customFormFields: CustomFormField[];
}

export const CustomFieldDrawer: React.FC<CustomFieldDrawerProps> = ({
  open,
  onClose,
  customFormFields = [],
}) => {
  return (
    <Drawer anchor="right" open={open} onClose={onClose}>
      <Box sx={{ width: 800, p: 2 }}>
        <div className="flex justify-between items-center mb-4">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2 bg-blue-50 px-2 py-1 pt-[10px]">
            <Typography variant="h6" className="font-semibold text-gray-900">
              Custom Fields
            </Typography>
            <IconButton onClick={onClose} aria-label="close">
              <CloseIcon />
            </IconButton>
          </div>
        </div>
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: "repeat(3, 1fr)",
            gap: 2,
            my: 2,
          }}
        >
          {customFormFields.map((field) => (
            <CustomFieldRenderer key={field.id} field={field} displayOnly />
          ))}
        </Box>
      </Box>
    </Drawer>
  );
};
