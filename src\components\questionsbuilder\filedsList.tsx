import { useState, useEffect } from "react";
import {
  Popover,
  List,
  ListItemButton,
  ListItemText,
  ListItemIcon,
  Divider,
  Tabs,
  Tab,
  Box,
  CircularProgress,
} from "@mui/material";
import AddCircleIcon from "@mui/icons-material/AddCircle";
import TextFieldsIcon from "@mui/icons-material/TextFields";
import LooksOneIcon from "@mui/icons-material/LooksOne";
import EmailIcon from "@mui/icons-material/Email";
import PhoneIcon from "@mui/icons-material/Phone";
import SubjectIcon from "@mui/icons-material/Subject";
import ArrowDropDownCircleIcon from "@mui/icons-material/ArrowDropDownCircle";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import ToggleOnIcon from "@mui/icons-material/ToggleOn";
import EventIcon from "@mui/icons-material/Event";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import TableChartIcon from "@mui/icons-material/TableChart";
import { getGlobals } from "@/api/Globals/globals";
import { getSubModulePermissionCommon } from "@/utils/generic";

const fetchGlobalFields = async () => {
  const data = await getGlobals();
  console.log("Fetched global fields:", data.findAllGlobals.globals);

  return (
    data?.findAllGlobals?.globals.map(
      (global: { id: string; name: string }) => ({
        label: global.name,
        id: global.id,
        type: "global_select",
        icon: <ArrowDropDownCircleIcon />,
      })
    ) || []
  );
};

interface CustomizeFieldMenuProps {
  sectionId: string;
  onAddField: (
    sectionId: string,
    fieldType: string,
    fieldLabel: string
  ) => void;
}

const fieldOptions = [
  { label: "Text", type: "text", icon: <TextFieldsIcon /> },
  { label: "Number", type: "number", icon: <LooksOneIcon /> },
  { label: "Email", type: "email", icon: <EmailIcon /> },
  { label: "Phone Number", type: "phone", icon: <PhoneIcon /> },
  { label: "Password", type: "password", icon: <LooksOneIcon /> },
  { label: "Text Area", type: "textarea", icon: <SubjectIcon /> },
  { label: "Select", type: "select", icon: <ArrowDropDownCircleIcon /> },
  { label: "Multi-Select", type: "multiselect", icon: <CheckBoxIcon /> },
  { label: "Toggle", type: "toggle", icon: <ToggleOnIcon /> },
  { label: "Date", type: "date", icon: <EventIcon /> },
  { label: "Time", type: "time", icon: <AccessTimeIcon /> },
  { label: "Checkboxes", type: "checkboxes", icon: <CheckBoxIcon /> },
  { label: "File Upload", type: "file_upload", icon: <CloudUploadIcon /> },
  // { label: "Image", type: "image", icon: <ImageIcon /> },
  { label: "Table Grid", type: "grid", icon: <TableChartIcon /> },
];

export const CustomizeFieldMenu: React.FC<CustomizeFieldMenuProps> = ({
  sectionId,
  onAddField,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [tabIndex, setTabIndex] = useState(0);
  const [globalFields, setGlobalFields] = useState<
    { label: string; type: string }[]
  >([]);
  const [loadingGlobals, setLoadingGlobals] = useState(false);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
    setTabIndex(0);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };
  const open = Boolean(anchorEl);

  useEffect(() => {
    if (open && tabIndex === 1 && globalFields.length === 0) {
      setLoadingGlobals(true);
      fetchGlobalFields()
        .then((data) => {
          const normalizedFields = (
            data as { label: string; type: string }[]
          ).map((field) => ({
            ...field,
          }));
          setGlobalFields(normalizedFields);
        })
        .catch((err) => {
          console.error("Failed to load global fields:", err);
        })
        .finally(() => setLoadingGlobals(false));
    }
  }, [open, tabIndex]);

  const renderFieldList = (
    fields: { label: string; type: string; icon?: React.ReactNode }[]
  ) => (
    <List dense className="overflow-y-auto py-1 max-h-[523px]">
      {fields.map((option, index) => (
        <div key={`${option.type}-${option.label}`}>
          <ListItemButton
            onClick={() => {
              onAddField(sectionId, option.type, option.label);
              handleClose();
            }}
            sx={{
              "&:hover": {
                backgroundColor: "#4AC7BD",
                color: "#fff",
                "& .MuiSvgIcon-root": {
                  color: "#fff",
                },
              },
            }}
          >
            {option.icon && (
              <ListItemIcon style={{ color: "#4ac7bd" }}>
                {option.icon}
              </ListItemIcon>
            )}
            <ListItemText
              primary={
                option.label.charAt(0).toUpperCase() + option.label.slice(1)
              }
            />
          </ListItemButton>
          {(index === 3 || index === 8) && <Divider />}
        </div>
      ))}
    </List>
  );

  return (
    <>
     {getSubModulePermissionCommon('Masters','Des Form','Edit Template')?.isEnabled && <button
        type="button"
        onClick={handleClick}
        className="flex items-center gap-2 p-2 rounded-md text-[#3c4858] hover:bg-gray-100 transition text-sm font-medium"
      >
        <AddCircleIcon className="text-[#3c4858]" fontSize="small" />
        <span>New Fields</span>
      </button>}
      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "left" }}
        transformOrigin={{ vertical: "top", horizontal: "left" }}
        PaperProps={{
          className: "rounded-md shadow-lg border border-gray-200 w-60",
        }}
      >
        <Tabs
          value={tabIndex}
          onChange={(e, v) => setTabIndex(v)}
          variant="fullWidth"
        >
          <Tab
            sx={{ textTransform: "none", padding: "8px 0px" }}
            label="Custom Fields"
          />
          <Tab
            sx={{ textTransform: "none", padding: "8px 0px" }}
            label="Global Fields"
          />
        </Tabs>
        <Box px={1} pb={1}>
          {tabIndex === 0 && renderFieldList(fieldOptions)}
          {tabIndex === 1 &&
            (loadingGlobals ? (
              <Box display="flex" justifyContent="center" py={2}>
                <CircularProgress size={24} />
              </Box>
            ) : (
              renderFieldList(globalFields)
            ))}
        </Box>
      </Popover>
    </>
  );
};
