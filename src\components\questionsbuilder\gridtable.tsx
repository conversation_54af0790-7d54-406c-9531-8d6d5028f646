import {
  Box,
  Dialog,
  <PERSON>alog<PERSON>ctions,
  DialogContent,
  DialogTitle,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TextField,
  Typography,
  IconButton,
  InputLabel,
  Select,
  CircularProgress,
  FormControlLabel,
  Switch,
} from "@mui/material";
import { useState, useEffect } from "react";
import { TableFields } from "./tablefields";
import AddCircleIcon from "@mui/icons-material/AddCircle";
import DeleteIcon from "@mui/icons-material/Delete";
import Button from "@/app/[components]/Button1";
import FieldActionsMenu from "./actionbox";
import { getGlobals } from "@/api/Globals/globals";
import { getSubModulePermissionCommon } from "@/utils/generic";

export interface GridColumn {
  id: string;
  name: string;
  fieldType: string;
  options?: { id: string; value: string }[];
  position?: number;
  globalFieldId?: string; // Added to support global_select
  required?: boolean; // Required toggle
  label?: string; // Label field
  fieldKey?: string; // Field key
}

interface FieldOption {
  label: string;
  field_type:
    | "text"
    | "phone"
    | "email"
    | "number"
    | "textarea"
    | "select"
    | "multiselect"
    | "toggle"
    | "date"
    | "time"
    | "datetime"
    | "file_upload"
    | "image"
    | "html"
    | "checkboxes"
    | "global_select"
    | "password"
    | "grid";
  required?: boolean;
  options?: { id: string; value: string }[];
  defaultValue?: string;
  name?: string;
  onEdit?: () => void;
  placeholder?: string;
  onDelete?: () => void;
  onChange?: (
    data:
      | string
      | number
      | boolean
      | string[]
      | null
      | { columns: GridColumn[]; rows: Record<string, unknown>[] }
  ) => void;
  value?:
    | string
    | number
    | boolean
    | string[]
    | null
    | { columns: GridColumn[]; rows: Record<string, unknown>[] };
  columns?: GridColumn[];
  rows?: Record<string, unknown>[];
}

interface GridTableProps {
  field: FieldOption;
  onEdit?: () => void;
  onChange?: (data: {
    columns: GridColumn[];
    rows: Record<string, unknown>[];
  }) => void;
}

export const GridTable: React.FC<GridTableProps> = ({ field, onChange }) => {
  const [columns, setColumns] = useState<GridColumn[]>(field.columns || []);
  const [rows, setRows] = useState<Record<string, unknown>[]>(field.rows || []);
  const [openAddColumnDialog, setOpenAddColumnDialog] = useState(false);
  const [newColumn, setNewColumn] = useState<
    Omit<GridColumn, "id"> & { position?: number }
  >({
    name: "",
    fieldType: "text",
    required: false,
    label: "",
    fieldKey: "",
  });
  const [editColumnIndex, setEditColumnIndex] = useState<number | null>(null);
  const [editColumn, setEditColumn] = useState<GridColumn | null>(null);
  const [globalFields, setGlobalFields] = useState<
    { id: string; name: string }[]
  >([]);
  const [loadingGlobals, setLoadingGlobals] = useState(false);

  // Fetch global fields when dialog opens and fieldType is global_select
  useEffect(() => {
    const isGlobalSelect =
      (editColumn && editColumn.fieldType === "global_select") ||
      (!editColumn && newColumn.fieldType === "global_select");
    if (openAddColumnDialog && isGlobalSelect && globalFields.length === 0) {
      setLoadingGlobals(true);
      getGlobals()
        .then((data) => {
          setGlobalFields(data?.findAllGlobals?.globals || []);
        })
        .catch(() => setGlobalFields([]))
        .finally(() => setLoadingGlobals(false));
    }
  }, [openAddColumnDialog, editColumn, newColumn.fieldType]);

  const handleAddColumn = () => {
    const columnId = `col_${Date.now()}`;
    const position = newColumn.position ?? columns.length;
    const newCol = { ...newColumn, id: columnId };
    const updatedColumns = [
      ...columns.slice(0, position),
      newCol,
      ...columns.slice(position),
    ];
    const updatedRows = rows.map((row) => ({
      ...row,
      [columnId]: "",
    }));
    setColumns(updatedColumns);
    setRows(updatedRows);
    setNewColumn({
      name: "",
      fieldType: "text",
      required: false,
      label: "",
      fieldKey: "",
    });
    setOpenAddColumnDialog(false);
    if (onChange) onChange({ columns: updatedColumns, rows: updatedRows });
  };

  const handleEditColumn = () => {
    if (editColumn && editColumnIndex !== null) {
      const position = editColumn.position ?? editColumnIndex;
      const updatedColumns = [...columns];
      const [col] = updatedColumns.splice(editColumnIndex, 1);
      updatedColumns.splice(position, 0, { ...editColumn, id: col.id });
      setColumns(updatedColumns);
      setEditColumn(null);
      setEditColumnIndex(null);
      setOpenAddColumnDialog(false);
      if (onChange) onChange({ columns: updatedColumns, rows });
    }
  };

  const handleDeleteColumn = (columnId: string) => {
    const updatedColumns = columns.filter((col) => col.id !== columnId);

    const updatedRows = rows.map((row) => {
      const newRow = { ...row };
      delete newRow[columnId];
      return newRow;
    });

    setColumns(updatedColumns);
    setRows(updatedRows);

    if (onChange) onChange({ columns: updatedColumns, rows: updatedRows });
  };

  // const handleAddRow = () => {
  //   const newRow: Record<string, unknown> = { id: `row_${Date.now()}` };
  //   for (const col of columns) {
  //     newRow[col.id] = "";
  //   }
  //   const updatedRows = [...rows, newRow];
  //   setRows(updatedRows);

  //   if (onChange) onChange({ columns, rows: updatedRows });
  // };

  const handleDeleteRow = (rowId: string) => {
    const updatedRows = rows.filter((row) => row.id !== rowId);
    setRows(updatedRows);

    if (onChange) onChange({ columns, rows: updatedRows });
  };

  const handleCellChange = (
    rowId: string,
    columnId: string,
    value: string | number | boolean | null
  ) => {
    const updatedRows = rows.map((row) =>
      row.id === rowId ? { ...row, [columnId]: value } : row
    );
    setRows(updatedRows);

    if (onChange) onChange({ columns, rows: updatedRows });
  };

  const renderCell = (row: Record<string, unknown>, column: GridColumn) => {
    let value = row[column.id];
    if (value === undefined || value === null) {
      value = "";
    }
    // Ensure value is not an object (except for arrays)
    if (typeof value === "object" && !Array.isArray(value)) {
      value = "";
    }
    const cellField: FieldOption = {
      label: column.name ?? "",
      field_type: column.fieldType as FieldOption["field_type"],
      options: field.options,
    };
    return (
      <TableFields
        field={cellField}
        value={
          value as
            | string
            | number
            | boolean
            | string[]
            | { columns: GridColumn[]; rows: Record<string, unknown>[] }
            | null
            | undefined
        }
        name={`${row.id}-${column.id}`}
        onChange={(value) => {
          if (
            typeof value === "string" ||
            typeof value === "number" ||
            typeof value === "boolean" ||
            value === null
          ) {
            handleCellChange(row.id as string, column.id, value);
          }
        }}
      />
    );
  };

  return (
    <Box
      sx={{
        p: 2,
        border: "1px solid #e0e0e0",
        borderRadius: 2,
        backgroundColor: "#fafafa",
      }}
    >
      <Box sx={{ display: "flex", justifyContent: "flex-end", mb: 2 }}>
       {getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled && <button
          type="button"
          onClick={() => setOpenAddColumnDialog(true)}
          className="flex items-center gap-2 p-2 rounded-md text-[#3c4858] hover:bg-gray-100 transition text-sm font-medium"
        >
          <AddCircleIcon className="text-[#3c4858]" fontSize="small" />
          <span>Add Column</span>
        </button>}

        {/* <button
          type="button"
          onClick={handleAddRow}
          className="flex items-center gap-2 p-2 rounded-md text-[#3c4858] hover:bg-gray-100 transition text-sm font-medium"
        >
          <AddCircleIcon className="text-[#3c4858]" fontSize="small" />
          <span>Add Row</span>
        </button> */}
      </Box>

      {columns.length > 0 ? (
        <Box sx={{ overflowX: "auto" }}>
          <Table sx={{ minWidth: 650, borderSpacing: "0 8px" }} size="small">
            <TableHead>
              <TableRow sx={{ backgroundColor: "#f0f4ff" }}>
                {columns.map((column, index) => (
                  <TableCell
                    key={column.id}
                    sx={{ fontWeight: 600, color: "#1e40af" }}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                      }}
                    >
                      <div>
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            gap: "8px",
                          }}
                        >
                          {column.name}
                          {column.required && (
                            <span
                              style={{ color: "#ef4444", fontSize: "12px" }}
                            >
                              *
                            </span>
                          )}
                        </div>
                        {/* <Typography
                          variant="caption"
                          sx={{ display: "block", color: "#6b7280" }}
                        >
                          {column.fieldType}
                          {column.label && ` • ${column.label}`}
                          {column.fieldKey && ` • Key: ${column.fieldKey}`}
                        </Typography> */}
                      </div>
                      <div>
                        {getSubModulePermissionCommon('Masters', 'Des Form', 'Edit Template')?.isEnabled && <FieldActionsMenu
                          onEdit={() => {
                            setEditColumnIndex(index);
                            setEditColumn({ ...column, position: index }); // <-- set position here
                            setOpenAddColumnDialog(true);
                          }}
                          onDelete={() => handleDeleteColumn(column.id)}
                        />}
                      </div>
                    </Box>
                  </TableCell>
                ))}
                <TableCell sx={{ width: "50px" }}></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {rows.map((row) => (
                <TableRow key={String(row.id)} hover>
                  {columns.map((column) => (
                    <TableCell
                      key={`${row.id}-${column.id}`}
                      sx={{ verticalAlign: "top" }}
                    >
                      {renderCell(row, column)}
                    </TableCell>
                  ))}
                  <TableCell>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => handleDeleteRow(row.id as string)}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Box>
      ) : (
        <Typography
          variant="body2"
          color="textSecondary"
          sx={{ p: 2, textAlign: "center" }}
        >
          No columns added yet. Click Add Column to get started.
        </Typography>
      )}

      <Dialog
        open={openAddColumnDialog}
        onClose={() => {
          setOpenAddColumnDialog(false);
          setEditColumn(null);
          setEditColumnIndex(null);
          setNewColumn({
            name: "",
            fieldType: "text",
            required: false,
            label: "",
            fieldKey: "",
          });
        }}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>
          {editColumn ? "Edit Column" : "Add New Column"}
        </DialogTitle>
        <DialogContent sx={{ pb: 0 }}>
          <Box>
            <InputLabel className="customdropdown">Column Name</InputLabel>
            {/* If global_select, show dropdown, else show textfield */}
            {(editColumn ? editColumn.fieldType : newColumn.fieldType) ===
            "global_select" ? (
              loadingGlobals ? (
                <Box sx={{ display: "flex", alignItems: "center", py: 1 }}>
                  <CircularProgress size={20} />
                  <span style={{ marginLeft: 8 }}>Loading...</span>
                </Box>
              ) : (
                <Select
                  fullWidth
                  value={
                    editColumn
                      ? editColumn.globalFieldId || ""
                      : newColumn.globalFieldId || ""
                  }
                  onChange={(e) => {
                    const selectedId = e.target.value;
                    const selectedField = globalFields.find(
                      (gf) => gf.id === selectedId
                    );
                    if (editColumn) {
                      setEditColumn({
                        ...editColumn,
                        name: selectedField ? selectedField.name : "",
                        globalFieldId: selectedId,
                      });
                    } else {
                      setNewColumn({
                        ...newColumn,
                        name: selectedField ? selectedField.name : "",
                        globalFieldId: selectedId,
                      });
                    }
                  }}
                  displayEmpty
                  sx={{ mt: 1 }}
                >
                  <MenuItem value="">
                    <em>Select Global Field</em>
                  </MenuItem>
                  {globalFields.map((gf) => (
                    <MenuItem key={gf.id} value={gf.id}>
                      {gf.name}
                    </MenuItem>
                  ))}
                </Select>
              )
            ) : (
              <TextField
                autoFocus
                margin="dense"
                fullWidth
                placeholder="Enter column name"
                value={editColumn ? editColumn.name : newColumn.name}
                onChange={(e) => {
                  if (editColumn) {
                    setEditColumn({ ...editColumn, name: e.target.value });
                  } else {
                    setNewColumn({ ...newColumn, name: e.target.value });
                  }
                }}
              />
            )}
          </Box>
          <Box>
            <InputLabel className="customdropdown">Field Type</InputLabel>
            <TextField
              select
              margin="dense"
              fullWidth
              value={editColumn ? editColumn.fieldType : newColumn.fieldType}
              onChange={(e) => {
                if (editColumn) {
                  setEditColumn({ ...editColumn, fieldType: e.target.value });
                } else {
                  setNewColumn({ ...newColumn, fieldType: e.target.value });
                }
              }}
            >
              {[
                "text",
                "number",
                "email",
                "select",
                "date",
                "phone",
                "textarea",
                "toggle",
                "time",
                "datetime",
                "file_upload",
                "global_select",
              ].map((type) => (
                <MenuItem key={type} value={type}>
                  {type.charAt(0).toUpperCase() + type.slice(1).toLowerCase()}
                </MenuItem>
              ))}
            </TextField>
          </Box>
          {/* Position Dropdown */}
          <Box>
            <InputLabel className="customdropdown">Position</InputLabel>
            <TextField
              select
              margin="dense"
              fullWidth
              value={
                editColumn
                  ? (editColumn.position ?? editColumnIndex ?? 0)
                  : (newColumn.position ?? columns.length)
              }
              onChange={(e) => {
                const pos = Number(e.target.value);
                if (editColumn) {
                  setEditColumn({ ...editColumn, position: pos });
                } else {
                  setNewColumn({ ...newColumn, position: pos });
                }
              }}
            >
              {Array.from({
                length: columns.length + (editColumn ? 0 : 1),
              }).map((_, idx) => (
                <MenuItem key={idx} value={idx}>
                  {idx + 1}
                </MenuItem>
              ))}
            </TextField>
          </Box>

          {/* Required Toggle */}
          <Box sx={{ mt: 2 }}>
            <FormControlLabel
              control={
                <Switch
                  checked={
                    editColumn
                      ? editColumn.required || false
                      : newColumn.required || false
                  }
                  onChange={(e) => {
                    if (editColumn) {
                      setEditColumn({
                        ...editColumn,
                        required: e.target.checked,
                      });
                    } else {
                      setNewColumn({
                        ...newColumn,
                        required: e.target.checked,
                      });
                    }
                  }}
                />
              }
              label="Required Field"
            />
          </Box>

          {/* Label Field */}
          <Box sx={{ mt: 2 }}>
            <InputLabel className="customdropdown">Label</InputLabel>
            <TextField
              margin="dense"
              fullWidth
              placeholder="Enter field label"
              value={
                editColumn ? editColumn.label || "" : newColumn.label || ""
              }
              onChange={(e) => {
                if (editColumn) {
                  setEditColumn({ ...editColumn, label: e.target.value });
                } else {
                  setNewColumn({ ...newColumn, label: e.target.value });
                }
              }}
            />
          </Box>

          {/* Field Key */}
          <Box sx={{ mt: 2 }}>
            <InputLabel className="customdropdown">Field Key</InputLabel>
            <TextField
              margin="dense"
              fullWidth
              placeholder="Enter field key (e.g., provider_npi)"
              value={
                editColumn
                  ? editColumn.fieldKey || ""
                  : newColumn.fieldKey || ""
              }
              onChange={(e) => {
                if (editColumn) {
                  setEditColumn({ ...editColumn, fieldKey: e.target.value });
                } else {
                  setNewColumn({ ...newColumn, fieldKey: e.target.value });
                }
              }}
            />
          </Box>

          {(editColumn ? editColumn.fieldType : newColumn.fieldType) ===
            "select" && (
            <Box sx={{ mt: 2 }}>
              <InputLabel className="lable-color ">Options</InputLabel>
              {(
                (editColumn ? editColumn.options : newColumn.options) || []
              ).map((opt, idx) => (
                <Box
                  key={opt.id}
                  sx={{ display: "flex", alignItems: "center", mb: 1 }}
                >
                  <TextField
                    size="small"
                    value={opt.value}
                    onChange={(e) => {
                      if (editColumn) {
                        const options = [...(editColumn.options || [])];
                        options[idx].value = e.target.value;
                        setEditColumn({ ...editColumn, options });
                      } else {
                        const options = [...(newColumn.options || [])];
                        options[idx].value = e.target.value;
                        setNewColumn({ ...newColumn, options });
                      }
                    }}
                    sx={{ flex: 1, mr: 1 }}
                  />
                  <IconButton
                    size="small"
                    color="error"
                    onClick={() => {
                      if (editColumn) {
                        const options = (editColumn.options || []).filter(
                          (_, i) => i !== idx
                        );
                        setEditColumn({ ...editColumn, options });
                      } else {
                        const options = (newColumn.options || []).filter(
                          (_, i) => i !== idx
                        );
                        setNewColumn({ ...newColumn, options });
                      }
                    }}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </Box>
              ))}
              <Button
                text="Add Option"
                className="buttonstyle bg-cyan-700 hover:bg-cyan-800 float-right"
                onClick={() => {
                  const options = editColumn
                    ? editColumn.options || []
                    : newColumn.options || [];
                  const newOpt = {
                    id: `opt_${Date.now()}`,
                    value: `Option ${options.length + 1}`,
                  };
                  if (editColumn) {
                    setEditColumn({
                      ...editColumn,
                      options: [...options, newOpt],
                    });
                  } else {
                    setNewColumn({
                      ...newColumn,
                      options: [...options, newOpt],
                    });
                  }
                }}
              >
                Add Option
              </Button>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button
            text="Cancel"
            onClick={() => {
              setOpenAddColumnDialog(false);
              setEditColumn(null);
              setEditColumnIndex(null);
              setNewColumn({
                name: "",
                fieldType: "text",
                required: false,
                label: "",
                fieldKey: "",
              });
            }}
            className="buttonstyle bg-cyan-950 hover:bg-cyan-950"
          >
            Cancel
          </Button>
          {editColumn ? (
            <Button
              text="Update"
              onClick={handleEditColumn}
              disabled={!editColumn.name}
              className="buttonstyle bg-teal-500 hover:bg-teal-600"
            >
              Update
            </Button>
          ) : (
            <Button
              text="Add"
              onClick={handleAddColumn}
              disabled={!newColumn.name}
              className="buttonstyle bg-teal-500 hover:bg-teal-600"
            >
              Add
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};
