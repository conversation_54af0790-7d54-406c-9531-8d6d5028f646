import TextFieldsIcon from "@mui/icons-material/TextFields";
import LooksOneIcon from "@mui/icons-material/LooksOne";
import EmailIcon from "@mui/icons-material/Email";
import PhoneIcon from "@mui/icons-material/Phone";
import SubjectIcon from "@mui/icons-material/Subject";
import ArrowDropDownCircleIcon from "@mui/icons-material/ArrowDropDownCircle";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import ToggleOnIcon from "@mui/icons-material/ToggleOn";
import EventIcon from "@mui/icons-material/Event";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import ImageIcon from "@mui/icons-material/Image";
import TableChartIcon from '@mui/icons-material/TableChart';
const fieldOptions = [
	{ label: "Text Field", type: "text", icon: <TextFieldsIcon /> },
	{ label: "Number", type: "number", icon: <LooksOneIcon /> },
	{ label: "Email", type: "email", icon: <EmailIcon /> },
	{ label: "Phone Number", type: "phone", icon: <PhoneIcon /> },
	{ label: "Text Area", type: "textarea", icon: <SubjectIcon /> },
	{ label: "Select", type: "select", icon: <ArrowDropDownCircleIcon /> },
	{ label: "Multi-Select", type: "multiselect", icon: <CheckBoxIcon /> },
	{ label: "Toggle", type: "toggle", icon: <ToggleOnIcon /> },
	{ label: "Date", type: "date", icon: <EventIcon /> },
	{ label: "Time", type: "time", icon: <AccessTimeIcon /> },
	{ label: "Checkboxes", type: "checkboxes", icon: <CheckBoxIcon /> },
	{ label: "File Upload", type: "file_upload", icon: <CloudUploadIcon /> },
	{ label: "Image", type: "image", icon: <ImageIcon /> },
	{ label: "Table Grid", type: "grid", icon: <TableChartIcon /> },
];

export default fieldOptions;