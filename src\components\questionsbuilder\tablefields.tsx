import {
  <PERSON><PERSON><PERSON>,
  <PERSON>u<PERSON><PERSON>,
  <PERSON>witch,
  FormControlLabel,
  Typography,
  Box,
  FormControl,
  Stack,
  IconButton,
  Menu,
} from "@mui/material";
import { DatePicker, TimePicker, DateTimePicker } from "@mui/x-date-pickers";
import { useState } from "react";
import dayjs from "dayjs";
import type { Dayjs } from "dayjs";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import Checkbox from "@mui/material/Checkbox";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { GridColumn } from "./gridtable";

export interface FieldOption {
  label: string;
  field_type:
    | "text"
    | "phone"
    | "email"
    | "number"
    | "textarea"
    | "select"
    | "multiselect"
    | "toggle"
    | "date"
    | "time"
    | "datetime"
    | "file_upload"
    | "image"
    | "html"
    | "checkboxes"
    | "global_select"
    | "password"
    | "grid";

  required?: boolean;
  options?: { id: string; value: string }[];
  defaultValue?: string;
  globals_name?: string;
  name?: string;
  onEdit?: () => void;
  placeholder?: string;
  onDelete?: () => void;
  onChange?: (
    data:
      | string
      | number
      | boolean
      | string[]
      | null
      | { columns: GridColumn[]; rows: Record<string, unknown>[] }
  ) => void;
  value?:
    | string
    | number
    | boolean
    | string[]
    | null
    | { columns: GridColumn[]; rows: Record<string, unknown>[] };
  columns?: GridColumn[];
  rows?: Record<string, unknown>[];
}

export const TableFields = ({
  field,
  onEdit,
  value: propValue,
  onChange,
  // name
}: {
  field: FieldOption;
  onEdit?: () => void;
  value?:
    | string
    | number
    | boolean
    | string[]
    | null
    | { columns: GridColumn[]; rows: Record<string, unknown>[] };
  onChange?: (
    value:
      | string
      | number
      | boolean
      | string[]
      | null
      | { columns: GridColumn[]; rows: Record<string, unknown>[] }
  ) => void;
  name?: string;
}) => {
  const [value, setValue] = useState<string>(
    propValue !== undefined && propValue !== null
      ? String(propValue)
      : field.defaultValue || ""
  );
  const [dateValue, setDateValue] = useState<Dayjs | null>(
    typeof propValue === "string" ||
      typeof propValue === "number" ||
      propValue instanceof Date
      ? dayjs(propValue)
      : dayjs()
  );
  const [fileName, setFileName] = useState("");
  const [imagePreview, setImagePreview] = useState("");
  const [toggleValue, setToggleValue] = useState(
    propValue !== undefined ? propValue : false
  );

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setValue(e.target.value);
    if (onChange) onChange(e.target.value);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) setFileName(file.name);
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImagePreview(URL.createObjectURL(file));
      setFileName(file.name);
    }
  };

  const handleToggleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setToggleValue(e.target.checked);
    if (onChange) onChange(e.target.checked);
  };

  const FieldActionsMenu = ({
    onEdit,
    onDelete,
  }: {
    onEdit?: () => void;
    onDelete?: () => void;
  }) => {
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    return (
      <>
        <IconButton size="small" onClick={handleMenuClick}>
          <MoreVertIcon fontSize="small" />
        </IconButton>
        <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
          <MenuItem
            onClick={() => {
              handleClose();
              if (onEdit) onEdit();
            }}
          >
            Edit
          </MenuItem>
          <MenuItem
            onClick={() => {
              handleClose();
              if (onDelete) onDelete();
            }}
          >
            Delete
          </MenuItem>
        </Menu>
      </>
    );
  };

  switch (field.field_type) {
    case "text":
    case "phone":
    case "email":
    case "number":
      return (
        <Box sx={{ position: "relative" }}>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            <TextField
              fullWidth
              name={field.name}
              required={field.required}
              variant="outlined"
              value={value}
              placeholder={`Enter the ${field.label}`}
              onChange={handleChange}
              type={field.field_type === "phone" ? "tel" : field.field_type}
              inputProps={
                field.field_type === "number"
                  ? { inputMode: "numeric", pattern: "[0-9]*" }
                  : {}
              }
              InputProps={{
                endAdornment: (
                  <FieldActionsMenu onEdit={onEdit} onDelete={field.onDelete} />
                ),
              }}
            />
          </div>
        </Box>
      );

    case "textarea":
      return (
        <Box sx={{ position: "relative" }}>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            {" "}
            <TextField
              fullWidth
              name={field.name}
              required={field.required}
              variant="outlined"
              multiline
              minRows={3}
              value={value}
              onChange={handleChange}
              InputProps={{
                endAdornment: (
                  <FieldActionsMenu onEdit={onEdit} onDelete={field.onDelete} />
                ),
              }}
            />
          </div>
        </Box>
      );

    case "select":
      return (
        <Box sx={{ position: "relative" }}>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            <TextField
              select
              fullWidth
              name={field.name}
              required={field.required}
              variant="outlined"
              value={value}
              onChange={handleChange}
              InputProps={{
                endAdornment: (
                  <FieldActionsMenu onEdit={onEdit} onDelete={field.onDelete} />
                ),
              }}
            >
              {field.options?.map((option) =>
                typeof option === "string" ? (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ) : (
                  <MenuItem key={option.id} value={option.value}>
                    {option.value}
                  </MenuItem>
                )
              )}
            </TextField>
          </div>
        </Box>
      );

    case "multiselect":
      return (
        <Box sx={{ position: "relative" }}>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            <TextField
              select
              fullWidth
              name={field.name}
              required={field.required}
              variant="outlined"
              value={Array.isArray(value) ? value : []}
              onChange={(e) => setValue(e.target.value)}
              SelectProps={{
                multiple: true,
              }}
            >
              {field.options?.map((option) =>
                typeof option === "string" ? (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ) : (
                  <MenuItem key={option.id} value={option.value}>
                    {option.value}
                  </MenuItem>
                )
              )}
            </TextField>
          </div>
        </Box>
      );

    case "toggle":
      return (
        <Box sx={{ position: "relative" }}>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            <Stack direction="row" alignItems="center" spacing={1}>
              <FormControlLabel
                control={
                  <Switch
                    checked={!!toggleValue}
                    onChange={handleToggleChange}
                    name={field.name}
                  />
                }
                label={
                  <>
                    {field.label}
                    {field.required && <span style={{ color: "red" }}>*</span>}
                  </>
                }
              />
              <FieldActionsMenu onEdit={onEdit} onDelete={field.onDelete} />
            </Stack>
          </div>
        </Box>
      );

    case "checkboxes":
      return (
        <Box sx={{ position: "relative" }}>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            <FormControl component="fieldset">
              <Box>
                {field.options?.map((option) => {
                  const selectedValues = value
                    ? value.split(",").filter(Boolean)
                    : [];
                  const optValue =
                    typeof option === "string" ? option : option.value;
                  const optLabel =
                    typeof option === "string" ? option : option.value;
                  const optKey =
                    typeof option === "string" ? option : option.id;
                  return (
                    <FormControlLabel
                      key={optKey}
                      control={
                        <Checkbox
                          checked={selectedValues.includes(optValue)}
                          onChange={(e) => {
                            let newSelected: string[];
                            if (e.target.checked) {
                              newSelected = [...selectedValues, optValue];
                            } else {
                              newSelected = selectedValues.filter(
                                (v) => v !== optValue
                              );
                            }
                            setValue(newSelected.join(","));
                          }}
                        />
                      }
                      label={optLabel}
                    />
                  );
                })}
              </Box>
            </FormControl>
            <FieldActionsMenu onEdit={onEdit} onDelete={field.onDelete} />
          </div>
        </Box>
      );

    case "date":
      return (
        <Box sx={{ position: "relative" }}>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DatePicker
                value={dateValue}
                onChange={(newValue) => setDateValue(newValue)}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    required: field.required,
                    name: field.name,
                  },
                }}
              />
            </LocalizationProvider>
          </div>
          <Box
            sx={{
              position: "absolute",
              top: 35,
              right: 0,
              zIndex: 1,
            }}
          >
            <FieldActionsMenu onEdit={onEdit} onDelete={field.onDelete} />
          </Box>
        </Box>
      );

    case "time":
      return (
        <Box sx={{ position: "relative" }}>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <TimePicker
                value={dateValue}
                onChange={(newValue) => setDateValue(newValue)}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    required: field.required,
                    name: field.name,
                  },
                }}
              />
            </LocalizationProvider>
          </div>
        </Box>
      );

    case "datetime":
      return (
        <Box sx={{ position: "relative" }}>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DateTimePicker
                value={dateValue}
                onChange={(newValue) => setDateValue(newValue)}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    required: field.required,
                    name: field.name,
                  },
                }}
              />
            </LocalizationProvider>
          </div>
          <Box
            sx={{
              position: "absolute",
              top: 35,
              right: 0,
              zIndex: 1,
            }}
          >
            <FieldActionsMenu onEdit={onEdit} onDelete={field.onDelete} />
          </Box>
        </Box>
      );

    case "file_upload":
      return (
        <Box sx={{ position: "relative" }}>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            <FormControl fullWidth>
              <input
                type="file"
                name={field.name}
                accept=".pdf,.doc,.docx,.xls,.xlsx,.txt"
                onChange={handleFileChange}
                required={field.required}
                style={{ marginTop: "8px" }}
              />
              {fileName && (
                <Typography variant="caption">{fileName}</Typography>
              )}
            </FormControl>
          </div>
        </Box>
      );

    case "image":
      return (
        <Box sx={{ position: "relative" }}>
          <div style={{ display: "flex", alignItems: "center", gap: "3px" }}>
            <FormControl fullWidth>
              <input
                type="file"
                name={field.name}
                accept="image/*"
                onChange={handleImageChange}
                required={field.required}
                style={{ marginTop: "8px" }}
              />
              {imagePreview && (
                <Box mt={1}>
                  <img
                    src={imagePreview}
                    alt="preview"
                    style={{ maxWidth: "100%", height: "auto" }}
                  />
                  <Typography variant="caption" display="block">
                    {fileName}
                  </Typography>
                </Box>
              )}
            </FormControl>
          </div>
        </Box>
      );
    // case "grid":
    //     return (
    //         <Box sx={{ position: "relative", mb: 2 }}>
    //             <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
    //                 <Box sx={{ flexGrow: 1 }}>
    //                     <GridTable
    //                         field={field}
    //                         onEdit={onEdit}
    //                         onChange={(data) => {
    //                             if (field.onChange) field.onChange(data);
    //                             if (onChange) onChange(data);
    //                         }}
    //                     />
    //                 </Box>
    //                 <FieldActionsMenu onEdit={onEdit} onDelete={field.onDelete} />
    //             </Box>
    //         </Box>
    //     );
    default:
      return (
        <Typography variant="body2">
          Unsupported field type: {field.field_type}
        </Typography>
      );
  }
};
