import { Dispatch, SetStateAction } from 'react';
export interface FieldOption {
    id: string;
    value: string;
}

export interface LogicCondition {
  fieldId: string;
  operator: '===' | '!==' | '>' | '<' | '>=' | '<=';
  value: string;
  multiSelectLogic?: 'AND' | 'OR';
}

export interface FieldType {
    id: string;
    label: string;
    field_type:"number"
    | "html"
    | "select"
    | "textarea"
    | "time"
    | "image"
    | "text"
    | "date"
    | "email"
    | "phone"
    | "grid"
    | "checkboxes"
    | "multiselect"
    | "toggle"
    | "file_upload"
    | "datetime"
    | "global_select";
    required?: boolean;
    placeholder?: string;
    filter?: boolean;
    options?: FieldOption[];
    logic_rules?: string[];
    logicConditions?: LogicCondition[];
    logicJoinType?: 'AND' | 'OR';
    visibleIf?: string;
    prefillEnabled?: boolean;
    prefillIdentifier?: string | null;
    prefillFields?: string[];
    category?: string;
    prefilledFields?: FieldType[];
    global?: boolean;
    only_in_grid?: boolean;
    is_default?: boolean;
    globals_name?: string;
    show_in_grid?: boolean;
    only_in_custom_form?: boolean;
    is_import?: boolean;
    prefillFieldIds?: string[];
    prefilled?: boolean;
    sectionId?: string;
  stepId?: string;
  sourceFieldId?: string;
  columns?: GridColumn[];
}

  interface GridColumn {
    id: string;
    name: string;
    fieldType?: string;
    options?: { id: string; value: string }[];
    globals_name?: string;
    placeholder?: string;
    required?: boolean;
  }

export interface FieldEditorProps {
    open: boolean;
    field: FieldType | null;
    onClose: () => void;
    onSave: (updatedField: FieldType) => void;
    allFields?: FieldType[];
    steps?: StepType[];
    setSteps?: Dispatch<SetStateAction<StepType[]>>;
}
export interface SectionType {
  id: string;
  name: string;
  fields: FieldType[];
}

export interface StepType {
  id: string;
  name: string;
  sections: SectionType[];
}