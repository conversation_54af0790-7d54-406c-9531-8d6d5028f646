"use client";

import React, { useState, useEffect, useRef, useCallback } from "react";
import { Clock, Play, Pause, RotateCcw, Save } from "lucide-react";

interface PersistentTimerProps {
  ticketId: string;
  userId: string;
  initialTime?: string;
  onTimeUpdate?: (time: string, totalSeconds: number) => void;
  onTimeSave?: (time: string, totalSeconds: number) => Promise<void>;
  autoSave?: boolean;
  autoSaveInterval?: number; // in seconds
  className?: string;
  showControls?: boolean;
}

export default function PersistentTimer({
  ticketId,
  userId,
  initialTime = "00:00:00",
  onTimeUpdate,
  onTimeSave,
  autoSave = true,
  autoSaveInterval = 30, // Save every 30 seconds
  className = "",
  showControls = true,
}: PersistentTimerProps) {
  const storageKey = `timer_${ticketId}_${userId}`;

  // Parse time string to seconds
  const parseTime = (timeStr: string): number => {
    const parts = timeStr.split(":").map(Number);
    if (parts.length === 2) {
      return parts[0] * 60 + parts[1];
    } else if (parts.length === 3) {
      return parts[0] * 3600 + parts[1] * 60 + parts[2];
    }
    return 0;
  };

  // Format seconds to HH:MM:SS
  const formatTime = (totalSeconds: number): string => {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  };

  // Initialize state from localStorage or initial time
  const [totalSeconds, setTotalSeconds] = useState<number>(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem(storageKey);
      if (saved) {
        const { seconds, lastSaved } = JSON.parse(saved);
        // If last saved was less than 1 hour ago, use saved time
        if (Date.now() - lastSaved < 3600000) {
          return seconds;
        }
      }
    }
    return parseTime(initialTime);
  });

  const [isRunning, setIsRunning] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [, setLastSaved] = useState<number>(Date.now());

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const autoSaveRef = useRef<NodeJS.Timeout | null>(null);

  // Save to localStorage
  const saveToLocalStorage = useCallback(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem(
        storageKey,
        JSON.stringify({
          seconds: totalSeconds,
          lastSaved: Date.now(),
          isRunning,
        })
      );
    }
  }, [storageKey, totalSeconds, isRunning]);

  // Save to backend
  const saveToBackend = useCallback(async () => {
    if (onTimeSave && !isSaving) {
      setIsSaving(true);
      try {
        await onTimeSave(formatTime(totalSeconds), totalSeconds);
        setLastSaved(Date.now());
      } catch (error) {
        console.error("Failed to save time:", error);
      } finally {
        setIsSaving(false);
      }
    }
  }, [onTimeSave, totalSeconds, isSaving]);

  // Timer effect
  useEffect(() => {
    if (isRunning) {
      intervalRef.current = setInterval(() => {
        setTotalSeconds((prev) => {
          const newTime = prev + 1;
          if (onTimeUpdate) {
            onTimeUpdate(formatTime(newTime), newTime);
          }
          return newTime;
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isRunning, onTimeUpdate]);

  // Auto-save effect
  useEffect(() => {
    if (autoSave && isRunning) {
      autoSaveRef.current = setInterval(() => {
        saveToBackend();
      }, autoSaveInterval * 1000);
    } else {
      if (autoSaveRef.current) {
        clearInterval(autoSaveRef.current);
      }
    }

    return () => {
      if (autoSaveRef.current) {
        clearInterval(autoSaveRef.current);
      }
    };
  }, [autoSave, isRunning, autoSaveInterval, saveToBackend]);

  // Save to localStorage whenever time changes
  useEffect(() => {
    saveToLocalStorage();
  }, [totalSeconds, isRunning, saveToLocalStorage]);

  // Save when component unmounts or page unloads
  useEffect(() => {
    const handleBeforeUnload = () => {
      saveToLocalStorage();
      if (onTimeSave && totalSeconds > 0) {
        // Use sendBeacon for reliable saving on page unload
        navigator.sendBeacon(
          "/api/save-time",
          JSON.stringify({
            ticketId,
            userId,
            time: formatTime(totalSeconds),
            totalSeconds,
          })
        );
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
      handleBeforeUnload();
    };
  }, [ticketId, userId, totalSeconds, onTimeSave, saveToLocalStorage]);

  const toggleTimer = () => {
    setIsRunning(!isRunning);
  };

  const resetTimer = () => {
    setIsRunning(false);
    setTotalSeconds(0);
    if (typeof window !== "undefined") {
      localStorage.removeItem(storageKey);
    }
    if (onTimeUpdate) {
      onTimeUpdate("00:00:00", 0);
    }
  };

  const manualSave = async () => {
    await saveToBackend();
  };

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      <Clock size={14} />
      <span className="text-black text-[13px] font-semibold">
        Total Time Worked:
      </span>
      <span className="text-[white] bg-[#EA6B6B] p-1 rounded-md font-mono">
        {formatTime(totalSeconds)}
      </span>

      {showControls && (
        <div className="flex items-center gap-1 ml-2">
          <button
            onClick={toggleTimer}
            className="p-1 rounded-full hover:bg-gray-200 transition"
            title={isRunning ? "Pause Timer" : "Start Timer"}
          >
            {isRunning ? (
              <Pause size={12} className="text-red-600" />
            ) : (
              <Play size={12} className="text-green-600" />
            )}
          </button>

          <button
            onClick={resetTimer}
            className="p-1 rounded-full hover:bg-gray-200 transition"
            title="Reset Timer"
          >
            <RotateCcw size={12} className="text-gray-600" />
          </button>

          {onTimeSave && (
            <button
              onClick={manualSave}
              disabled={isSaving}
              className="p-1 rounded-full hover:bg-gray-200 transition disabled:opacity-50"
              title="Save Time"
            >
              <Save
                size={12}
                className={`${isSaving ? "text-blue-600 animate-pulse" : "text-gray-600"}`}
              />
            </button>
          )}
        </div>
      )}

      {autoSave && isRunning && (
        <span className="text-xs text-gray-500 ml-2">Auto-saving...</span>
      )}
    </div>
  );
}
