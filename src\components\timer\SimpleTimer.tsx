"use client";

import React, { useState, useEffect, useRef } from "react";
import OutlineTimer from "../../assests/outlinetimer.svg";
import Image from "next/image";

interface SimpleTimerProps {
  initialTime?: string; // Format: "HH:MM:SS" or "MM:SS"
  onTimeUpdate?: (time: string) => void;
  autoStart?: boolean;
  showControls?: boolean;
  className?: string;
}

export default function SimpleTimer({
  initialTime = "00:00:00",
  onTimeUpdate,
  autoStart = false,
  showControls = true,
  className = "",
}: SimpleTimerProps) {
  // Parse time string to seconds
  const parseTime = (timeStr: string): number => {
    const parts = timeStr.split(":").map(Number);
    if (parts.length === 2) {
      // MM:SS format
      return parts[0] * 60 + parts[1];
    } else if (parts.length === 3) {
      // HH:MM:SS format
      return parts[0] * 3600 + parts[1] * 60 + parts[2];
    }
    return 0;
  };

  // Format seconds to HH:MM:SS
  const formatTime = (totalSeconds: number): string => {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) {
      return `${hours.toString().padStart(2, "0")}:${minutes
        .toString()
        .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
    } else {
      return `${minutes.toString().padStart(2, "0")}:${seconds
        .toString()
        .padStart(2, "0")}`;
    }
  };

  const [totalSeconds, setTotalSeconds] = useState<number>(
    parseTime(initialTime)
  );
  const [isRunning, setIsRunning] = useState<boolean>(autoStart);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Timer effect
  useEffect(() => {
    if (isRunning) {
      intervalRef.current = setInterval(() => {
        setTotalSeconds((prev) => {
          const newTime = prev + 1;
          const formattedTime = formatTime(newTime);
          if (onTimeUpdate) {
            onTimeUpdate(formattedTime);
          }
          return newTime;
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isRunning, onTimeUpdate]);

  const toggleTimer = () => {
    setIsRunning(!isRunning);
  };

  // const resetTimer = () => {
  //   setIsRunning(false);
  //   setTotalSeconds(0);
  //   if (onTimeUpdate) {
  //     onTimeUpdate("00:00:00");
  //   }
  // };

  // const pauseTimer = () => {
  //   setIsRunning(false);
  // };

  // const startTimer = () => {
  //   setIsRunning(true);
  // };

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      <Image src={OutlineTimer} alt="Timer Icon" width={20} height={20} />
      <span className="text-[#222222] text-[14px] font-normal">
        Total Time Worked:
      </span>
      <span className="text-[white] bg-[#EA6B6B] px-1 py-0.5 rounded-md">
        {formatTime(totalSeconds)}
      </span>

      {showControls && (
        <div className="flex items-center gap-1 ml-2">
          <button
            onClick={toggleTimer}
            className="p-1 rounded-full hover:bg-gray-200 transition"
            title={isRunning ? "Pause Timer" : "Start Timer"}
          >
            {/* {isRunning ? (
              <Pause size={12} className="text-red-600" />
            ) : (
              <Play size={12} className="text-green-600" />
            )} */}
          </button>
          {/* 
          <button
            onClick={resetTimer}
            className="p-1 rounded-full hover:bg-gray-200 transition"
            title="Reset Timer"
          >
            <RotateCcw size={12} className="text-gray-600" />
          </button> */}
        </div>
      )}
    </div>
  );
}

// Export individual functions for external control
export const useTimerControls = (initialTime: string = "00:00:00") => {
  // Parse time string to seconds
  const parseTime = (timeStr: string): number => {
    const parts = timeStr.split(":").map(Number);
    if (parts.length === 2) {
      // MM:SS format
      return parts[0] * 60 + parts[1];
    } else if (parts.length === 3) {
      // HH:MM:SS format
      return parts[0] * 3600 + parts[1] * 60 + parts[2];
    }
    return 0;
  };

  const [totalSeconds, setTotalSeconds] = useState<number>(
    parseTime(initialTime)
  );
  const [isRunning, setIsRunning] = useState<boolean>(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const formatTime = (totalSeconds: number): string => {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) {
      return `${hours.toString().padStart(2, "0")}:${minutes
        .toString()
        .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
    } else {
      return `${minutes.toString().padStart(2, "0")}:${seconds
        .toString()
        .padStart(2, "0")}`;
    }
  };

  useEffect(() => {
    if (isRunning) {
      intervalRef.current = setInterval(() => {
        setTotalSeconds((prev) => prev + 1);
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isRunning]);

  const start = () => setIsRunning(true);
  const pause = () => setIsRunning(false);
  const reset = () => {
    setIsRunning(false);
    setTotalSeconds(0);
  };
  const toggle = () => setIsRunning(!isRunning);

  return {
    time: formatTime(totalSeconds),
    totalSeconds,
    isRunning,
    start,
    pause,
    reset,
    toggle,
  };
};
