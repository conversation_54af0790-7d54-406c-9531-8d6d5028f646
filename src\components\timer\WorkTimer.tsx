"use client";

import React, { useState, useEffect, useRef } from "react";
import { Clock, Play, Pause, RotateCcw } from "lucide-react";

interface WorkTimerProps {
  initialTime?: string; // Format: "HH:MM:SS"
  onTimeUpdate?: (time: string) => void;
  className?: string;
}

export default function WorkTimer({
  initialTime = "00:00:00",
  onTimeUpdate,
  className = "",
}: WorkTimerProps) {
  // Parse initial time
  const parseTime = (timeStr: string): number => {
    const [hours = "0", minutes = "0", seconds = "0"] = timeStr.split(":");
    return (
      parseInt(hours) * 3600 + parseInt(minutes) * 60 + parseInt(seconds)
    );
  };

  // Format seconds to HH:MM:SS
  const formatTime = (totalSeconds: number): string => {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    return [hours, minutes, seconds]
      .map((val) => (val < 10 ? `0${val}` : val))
      .join(":");
  };

  const [seconds, setSeconds] = useState<number>(parseTime(initialTime));
  const [isRunning, setIsRunning] = useState<boolean>(false);
  const [displayTime, setDisplayTime] = useState<string>(
    formatTime(parseTime(initialTime))
  );
  
  // Use a ref to store the start time of the current session
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number>(0);

  // Start/stop timer
  useEffect(() => {
    if (isRunning) {
      startTimeRef.current = Date.now() - seconds * 1000;
      
      timerRef.current = setInterval(() => {
        const elapsedSeconds = Math.floor((Date.now() - startTimeRef.current) / 1000);
        setSeconds(elapsedSeconds);
        const formattedTime = formatTime(elapsedSeconds);
        setDisplayTime(formattedTime);
        
        if (onTimeUpdate) {
          onTimeUpdate(formattedTime);
        }
      }, 1000);
    } else if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isRunning, onTimeUpdate]);

  const toggleTimer = () => {
    setIsRunning(!isRunning);
  };

  const resetTimer = () => {
    setIsRunning(false);
    setSeconds(0);
    setDisplayTime("00:00:00");
    if (onTimeUpdate) {
      onTimeUpdate("00:00:00");
    }
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <div className="flex items-center gap-1">
        <Clock size={16} className="text-gray-600" />
        <span className="text-sm font-medium">Time Worked:</span>
      </div>
      
      <div className="flex items-center gap-2">
        <span className="text-white bg-[#EA6B6B] px-2 py-1 rounded-md font-mono text-sm">
          {displayTime}
        </span>
        
        <button
          onClick={toggleTimer}
          className="p-1 rounded-full hover:bg-gray-200 transition"
          title={isRunning ? "Pause Timer" : "Start Timer"}
        >
          {isRunning ? (
            <Pause size={16} className="text-red-600" />
          ) : (
            <Play size={16} className="text-green-600" />
          )}
        </button>
        
        <button
          onClick={resetTimer}
          className="p-1 rounded-full hover:bg-gray-200 transition"
          title="Reset Timer"
        >
          <RotateCcw size={16} className="text-gray-600" />
        </button>
      </div>
    </div>
  );
}

// Persistent Timer Hook
export function useWorkTimer(key: string, initialTime: string = "00:00:00") {
  const [time, setTime] = useState<string>(
    typeof window !== "undefined"
      ? localStorage.getItem(key) || initialTime
      : initialTime
  );

  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem(key, time);
    }
  }, [time, key]);

  return [time, setTime] as const;
}
