import { Toaster, toast } from "react-hot-toast";
import { X, <PERSON><PERSON>ir<PERSON>, AlertTriangle, Info } from "lucide-react";
import { responseMessages, ResponseCode } from "./toastrResponse";

export const ToastProvider = () => {
  return (
    <Toaster
      position="top-right"
      toastOptions={{
        style: {
          padding: 0,
          boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
          borderRadius: "8px",
          background: "#fff",
        },
      }}
    />
  );
};

const resolveMessage = (messageOrCode: string) => {
  return responseMessages[messageOrCode as ResponseCode] || messageOrCode ;
};

const getColorClasses = (type: "success" | "error" | "info") => {
  switch (type) {
    case "success":
      return {
        border: "border-green-500",
        icon: "text-green-500",
      };
    case "error":
      return {
        border: "border-red-500",
        icon: "text-red-500",
      };
    case "info":
      return {
        border: "border-blue-500",
        icon: "text-blue-500",
      };
  }
};

const renderStyledToast = (
  type: "success" | "error" | "info",
  title: string,
  message: string,
   // eslint-disable-next-line @typescript-eslint/no-explicit-any
  t: any
) => {
  const { border, icon } = getColorClasses(type);
  const Icon =
    type === "success" ? CheckCircle : type === "error" ? AlertTriangle : Info;

  return (
    <div
      className={`flex items-start border-l-4 ${border} p-4 pr-3 bg-white rounded-md shadow-md`}
      style={{ width: "350px" }} // keep toast compact
    >
      <Icon className={`${icon} w-5 h-5 mt-0.5 mr-3`} />
      <div className="flex-1">
        <p className="text-sm font-semibold text-gray-800">{title}</p>
        <p className="text-sm text-gray-600">{message}</p>
      </div>
      <button
        className="ml-4"
        onClick={() => toast.dismiss(t.id)}
        aria-label="Dismiss"
      >
        <X className="w-4 h-4 text-gray-500 hover:text-gray-700" />
      </button>
    </div>
  );
};

export const showToast = {
  success: (code: string) =>
    toast.custom((t) =>
      renderStyledToast("success", "Success", resolveMessage(code), t)
    ),
  error: (code: string) =>
    toast.custom((t) =>
      renderStyledToast("error", "Error", resolveMessage(code), t)
    ),
  info: (code: string) =>
    toast.custom((t) =>
      renderStyledToast("info", "Info", resolveMessage(code), t)
    ),
};
