export enum ResponseCode {
  SUCCESS = 'SUCCESS',
  CREATED = 'CREATED',
  OTP_SENT = 'OTP_SENT',
  OTP_VERIFIED = 'OTP_VERIFIED',
  OTP_INVALID = 'OTP_INVALID',
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  TOTP_INVALID = 'TOTP_INVALID',
  TWO_FA_ENABLED = 'TWO_FA_ENABLED',
  TWO_FA_PASS_ENABLED = 'TWO_FA_PASS_ENABLED',
  EMAIL_REQUIRED = 'EMAIL_REQUIRED',
  OTP_EXPIRED = 'OTP_EXPIRED',
  INVALID_EMAIL = 'INVALID_EMAIL',
  TOKEN_MISSING = 'TOKEN_MISSING',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  TOKEN_INVALID = 'TOKEN_INVALID',
  UNAUTHORIZED = 'UNAUTHORIZED',
  SERVER_ERROR = 'SERVER_ERROR',
  ALREADY_DISABLED = 'ALREADY_DISABLED',
  ALREADY_EXISTS = 'ALREADY_EXISTS',
  LOGOUT_SUCCESS='LOGOUT_SUCCESS',
  MFA_SENT_SUCCESS='MFA_SENT_SUCCESS',
  MFA_VERIFIED_SUCCESS='MFA_VERIFIED_SUCCESS',
  OTP_SENT_SUCCESS='OTP_SENT_SUCCESS',
  OTP_VERIFIED_SUCCESS='OTP_VERIFIED_SUCCESS',
  RESEND_OTP_SUCCESS='RESEND_OTP_SUCCESS',
  ENABLE_EMAIL='ENABLE_EMAIL',
  ENABLE_MFA='ENABLE_MFA',
  OTP_LIMIT_REACHED='OTP_LIMIT_REACHED',
  UPDATED='UPDATED',
  DELETED='DELETED',
}

export const responseMessages: Record<ResponseCode, string> = {
  [ResponseCode.SUCCESS]: "Login successfully.",
  [ResponseCode.CREATED]: "Created successfully.",
  [ResponseCode.OTP_SENT]: "OTP sent successfully.",
  [ResponseCode.OTP_VERIFIED]: "OTP verified successfully.",
  [ResponseCode.OTP_INVALID]: "Invalid OTP. Please try again.",
  [ResponseCode.USER_NOT_FOUND]: "User not found.",
  [ResponseCode.TOTP_INVALID]: "Invalid TOTP. Please try again.",
  [ResponseCode.TWO_FA_ENABLED]: "2FA has been enabled.",
  [ResponseCode.TWO_FA_PASS_ENABLED]: "2FA with password enabled.",
  [ResponseCode.EMAIL_REQUIRED]: "Email is required.",
  [ResponseCode.OTP_EXPIRED]: "OTP has expired.",
  [ResponseCode.INVALID_EMAIL]: "Invalid email address.",
  [ResponseCode.TOKEN_MISSING]: "Authentication token is missing.",
  [ResponseCode.INTERNAL_ERROR]: "An internal error occurred.",
  [ResponseCode.TOKEN_INVALID]: "Invalid token.",
  [ResponseCode.UNAUTHORIZED]: "You are not authorized to perform this action.",
  [ResponseCode.SERVER_ERROR]: "Server error. Please try again later.",
  [ResponseCode.ALREADY_DISABLED]: "This feature is already disabled.",
  [ResponseCode.ALREADY_EXISTS]: "This resource already exists.",
  [ResponseCode.LOGOUT_SUCCESS]: "Logout successfully.",
  [ResponseCode.MFA_SENT_SUCCESS]: "Check your Authenticator App.",
  [ResponseCode.MFA_VERIFIED_SUCCESS]: "Code verified successfully.",
  [ResponseCode.OTP_SENT_SUCCESS]: "OTP sent successfully.",
  [ResponseCode.OTP_VERIFIED_SUCCESS]: "OTP verified successfully.",
  [ResponseCode.RESEND_OTP_SUCCESS]: "OTP resent successfully.",
  [ResponseCode.ENABLE_EMAIL]: "Email OTP is Enabled.",
  [ResponseCode.ENABLE_MFA]: "Authenticator App OTP is Enabled.",
  [ResponseCode.UPDATED]: "Updated successfully.",
  [ResponseCode.DELETED]: "Deleted successfully.",
  [ResponseCode.OTP_LIMIT_REACHED]: "You’ve reached the limit of 5 code requests for today. Please try again after 24 hours.",
  };
