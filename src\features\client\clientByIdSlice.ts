import { TableData } from '@/types/user';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

const initialState = {};

console.log('initialState',initialState);

export const clientByIdSlice = createSlice({
  name: 'clientById',
  initialState,
  reducers: {
    setSingleClient: (state, action: PayloadAction<Partial<TableData>>) => {
      Object.assign(state, action.payload);
    },

  },
});

export const { setSingleClient } = clientByIdSlice.actions;
export const clientByIdReducer =  clientByIdSlice.reducer;