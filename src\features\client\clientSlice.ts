import { TableData } from '@/types/user';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import tableConfig from "../../app/(protected)/organizations/clientData.json";

const initialState = tableConfig;

console.log('initialState',initialState);

export const clientSlice = createSlice({
  name: 'client',
  initialState,
  reducers: {
    setClientTable: (state, action: PayloadAction<TableData>) => {
      Object.assign(state, action.payload);
    },

  },
});

export const sideNavSlice = createSlice({
  name: 'sideNav',
  initialState:{
    sideNav: true,
  },
  reducers: {
    setSideNav(state, action: PayloadAction<boolean>) {
      return {
        ...state,
        sideNav: action.payload,
      };
      
    }
  },
});


export const orgListSlice = createSlice({
  name: 'orgList',
  initialState:{
    orgList: [],
  },
  reducers: {
    setOrgList(state, action: PayloadAction<[]>) {
      return {
        ...state,
        orgList: action.payload,
      };
      
    }
  },
});


export const { setClientTable } = clientSlice.actions;
export const { setSideNav } = sideNavSlice.actions;
export const { setOrgList } = orgListSlice.actions;
export const clientReducer =  clientSlice.reducer;
export const orgListReducer =  orgListSlice.reducer;
export const sideNavReducer = sideNavSlice.reducer;