import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface TitleState {
  titles: string[];
}

const initialState: TitleState = {
  titles: [],
};

export const titleSlice = createSlice({
  name: 'title',
  initialState,
  reducers: {
    setTitles: (state, action: PayloadAction<string[]>) => {
      state.titles = action.payload;
    },
    addTitle: (state, action: PayloadAction<string>) => {
      state.titles.push(action.payload);
    },
    clearTitles: () => initialState,
  },
});

export const { setTitles, addTitle, clearTitles } = titleSlice.actions;
export const titleReducer = titleSlice.reducer;
