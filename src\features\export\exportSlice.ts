/* eslint-disable @typescript-eslint/no-explicit-any */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface TitleState {
  exportId: string[];
}

const initialState: TitleState = {
  exportId: [],
};

export const exportIdSlice = createSlice({
  name: 'exportId',
  initialState,
  reducers: {
    setExportId: (state, action: PayloadAction<string[]>) => {
        console.log('action.payload',action.payload);
        
      state.exportId = action.payload;
    },

    clearExportId: () => initialState,
  },
});

export const printSlice = createSlice({
  name: 'print',
  initialState:[] as any[],
  reducers: {
  setPrintData(state, action: PayloadAction<any[]>) {
  state = action.payload;
}
  },
});

export const { setExportId, clearExportId } = exportIdSlice.actions;
export const exportIdReducer = exportIdSlice.reducer;
export const { setPrintData } = printSlice.actions;
export const printReducer = printSlice.reducer;
