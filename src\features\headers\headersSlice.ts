import { createSlice, PayloadAction } from '@reduxjs/toolkit';

const initialState: string[] = [];

export const headerSlice = createSlice({
  name: 'clientHeaders',
  initialState,
  reducers: {
    setHeaders: (state, action: PayloadAction<string[]>) => {
      return action.payload; // Replace entire state (safer and simpler)
    },
  },
});
export const headerDefaultSlice = createSlice({
  name: 'clientHeadersDefault',
  initialState:[],
  reducers: {
    setHeadersDefault(state, action: PayloadAction<[]>) {
       return action.payload; 
      
    }
  },
});
export type RoleResponse = {
  id: string;
  value: string;
  role: {
    _id: string;
    name: string;
    permissions: {
      moduleName: string;
      displayName: string;
      isEnabled: boolean;
      subModules: {
        displayName: string;
        moduleName: string;
        isEnabled: boolean;
        permissions: {
          displayName: string;
          isEnabled: boolean;
        }[];
      }[];
    }[];
    key: string;
  };
};
// export const headerProcessSlice = createSlice({
//   name: 'headerProcess',
//   initialState:{},
//   reducers: {
//     setRole(state, action: PayloadAction<Partial<RoleResponse>>) {
//        return action.payload; 
      
//     }
//   },
// });
const initialStates: Partial<RoleResponse> = {};

export const headerProcessSlice = createSlice({
  name: 'headerProcess',
  initialState:initialStates,
  reducers: {
    setRole(state, action: PayloadAction<Partial<RoleResponse>>) {
      return action.payload; // Replace entire state
    }
  }
});
type Process = {
    organizationID: string;
    subOrganizationId: string;
    processId: string;
}
 const initalProcess:Process={
  organizationID:"",
  subOrganizationId:"",
  processId:""
 }
export const processSlice = createSlice({
  name: 'process',
  initialState:initalProcess,
  reducers: {
    setProcess(state, action: PayloadAction<Partial<Process>>) {
        return {
        ...state,
        ...action.payload
      };
      
    },
    setClearProcess() {
       return initalProcess; 
      
    }
  },
});


// Export actions and reducer
export const { setHeaders } = headerSlice.actions;
export const headersReducer = headerSlice.reducer;
export const { setHeadersDefault } = headerDefaultSlice.actions;
export const headersDefaultReducer = headerDefaultSlice.reducer;
export const { setRole } = headerProcessSlice.actions;
export const headerProcessReducer = headerProcessSlice.reducer;
export const { setProcess, setClearProcess } = processSlice.actions;
export const processReducer = processSlice.reducer;