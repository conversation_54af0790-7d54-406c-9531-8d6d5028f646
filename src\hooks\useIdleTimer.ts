// hooks/useIdleTimer.ts
import { useEffect, useRef } from "react";

const IDLE_TIMEOUT = 4 * 60 * 60 * 1000; // 4 hours in milliseconds

export const useIdleTimer = (onIdle: () => void) => {
  const timer = useRef<ReturnType<typeof setTimeout> | null>(null);

  const resetTimer = () => {
    if (timer.current) clearTimeout(timer.current);
    timer.current = setTimeout(onIdle, IDLE_TIMEOUT);
  };

  useEffect(() => {
    const activityEvents = ["mousemove", "keydown", "scroll", "click"];

    const handleActivity = () => {
      resetTimer();
    };

    activityEvents.forEach((event) =>
      window.addEventListener(event, handleActivity)
    );

    // Initialize the timer
    resetTimer();

    return () => {
      if (timer.current) clearTimeout(timer.current);
      activityEvents.forEach((event) =>
        window.removeEventListener(event, handleActivity)
      );
    };
  }, []);
};
