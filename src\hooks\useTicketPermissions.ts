import { useMemo } from 'react';
import { usePathname } from 'next/navigation';
import { getSubModulePermissionCommon } from '@/utils/generic';

export interface TicketPermissions {
  canAllocate: boolean;
  canReallocate: boolean;
  canExport: boolean;
  canPrint: boolean;
  canView: boolean;
  canUpdate: boolean;
  canDelete: boolean;
  canCreate: boolean;
  canSelf: boolean;
  canExportToExcel: boolean;
}

export const useTicketPermissions = (): TicketPermissions => {
  const pathname = usePathname();

  const subModuleName = useMemo(() => {
    if (pathname.includes('/Alltickets')) return 'All Ticket';
    if (pathname.includes('/Exceptions')) return 'Exception';
    if (pathname.includes('/Completed')) return 'Completed';
    if (pathname.includes('/Source')) return 'Source';
    if (pathname.includes('/import')) return 'Import';
    return 'All Ticket'; // Default fallback
  }, [pathname]);

  const permissions = useMemo(() => {
    const moduleName = 'Tickets';
    
    return {
      canAllocate: getSubModulePermissionCommon(moduleName, subModuleName, 'Allocate')?.isEnabled ?? false,
      canReallocate: getSubModulePermissionCommon(moduleName, subModuleName, 'Reallocate')?.isEnabled ?? false,
      canExport: getSubModulePermissionCommon(moduleName, subModuleName, 'Export')?.isEnabled ?? false,
      canPrint: getSubModulePermissionCommon(moduleName, subModuleName, 'Print')?.isEnabled ?? false,
      canView: getSubModulePermissionCommon(moduleName, subModuleName, 'View')?.isEnabled ?? false,
      canUpdate: getSubModulePermissionCommon(moduleName, subModuleName, 'Update')?.isEnabled ?? false,
      canDelete: getSubModulePermissionCommon(moduleName, subModuleName, 'Delete')?.isEnabled ?? false,
      canCreate: getSubModulePermissionCommon(moduleName, subModuleName, 'Create')?.isEnabled ?? false,
      canSelf: getSubModulePermissionCommon(moduleName, subModuleName, 'Self')?.isEnabled ?? false,
      canExportToExcel: getSubModulePermissionCommon(moduleName, subModuleName, 'Export as Excel')?.isEnabled ?? false,
    };
  }, [subModuleName]);

  return permissions;
};

// Generic permission hook for any module
export const useModulePermissions = (moduleName: string, subModuleName: string) => {
  return useMemo(() => {
    return {
      canAllocate: getSubModulePermissionCommon(moduleName, subModuleName, 'Allocate')?.isEnabled ?? false,
      canReallocate: getSubModulePermissionCommon(moduleName, subModuleName, 'Reallocate')?.isEnabled ?? false,
      canExport: getSubModulePermissionCommon(moduleName, subModuleName, 'Export')?.isEnabled ?? false,
      canPrint: getSubModulePermissionCommon(moduleName, subModuleName, 'Print')?.isEnabled ?? false,
      canView: getSubModulePermissionCommon(moduleName, subModuleName, 'View')?.isEnabled ?? false,
      canUpdate: getSubModulePermissionCommon(moduleName, subModuleName, 'Update')?.isEnabled ?? false,
      canDelete: getSubModulePermissionCommon(moduleName, subModuleName, 'Delete')?.isEnabled ?? false,
      canCreate: getSubModulePermissionCommon(moduleName, subModuleName, 'Create')?.isEnabled ?? false,
      canSelf: getSubModulePermissionCommon(moduleName, subModuleName, 'Self')?.isEnabled ?? false,
      canExportToExcel: getSubModulePermissionCommon(moduleName, subModuleName, 'Export as Excel')?.isEnabled ?? false,

    };
  }, [moduleName, subModuleName]);
};
