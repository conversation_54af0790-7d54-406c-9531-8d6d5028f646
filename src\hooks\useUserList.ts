import { useState, useEffect } from 'react';
export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  avatar?: string;
  department?: string;
  isActive?: boolean;
}
interface UseUserListReturn {
  users: User[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

import { getUserList } from '../api/ProviderCredentials/provider';

export const useUserList = (): UseUserListReturn => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchUsers = async (): Promise<void> => {
    setLoading(true);
    setError(null);

    try {
      const response = await getUserList();
      let allUsers: User[] = [];
      interface UserData {
        _id: string;
        name: string;
        email: string;
        roleName?: string;
        employeeId?: string;
      }
      interface RoleGroup {
        role: string;
        users: UserData[];
      }
      interface SingleRoleGroup {
        role?: string;
        users?: UserData[];
      }

      if (response?.usersUnderManager) {
        if (Array.isArray(response.usersUnderManager)) {
          response.usersUnderManager.forEach((roleGroup: RoleGroup) => {
            if (roleGroup.users && Array.isArray(roleGroup.users)) {
              const roleUsers = roleGroup.users.map((user) => ({
                id: user._id,
                name: user.name,
                email: user.email,
                role: user.roleName || roleGroup.role || 'User',
                department: roleGroup.role,
                employeeId: user.employeeId,
                avatar: undefined,
                isActive: true
              }));
              allUsers = [...allUsers, ...roleUsers];
            }
          });
        } else {
          const singleGroup = response.usersUnderManager as SingleRoleGroup;
          if (singleGroup.users && Array.isArray(singleGroup.users)) {
            const roleUsers = singleGroup.users.map((user: UserData) => ({
              id: user._id,
              name: user.name,
              email: user.email,
              role: user.roleName || singleGroup.role || 'User',
              department: singleGroup.role || 'Unknown',
              employeeId: user.employeeId,
              avatar: undefined,
              isActive: true
            }));
            allUsers = roleUsers;
          }
        }
      }

      setUsers(allUsers);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch users';
      setError(errorMessage);
      console.error('Error fetching users:', err);
    } finally {
      setLoading(false);
    }
  };

  const refetch = async (): Promise<void> => {
    await fetchUsers();
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  return {
    users,
    loading,
    error,
    refetch,
  };
};
