/* eslint-disable @typescript-eslint/no-explicit-any */
import { showToast } from "@/components/toaster/ToastProvider";
import {
  ApolloClient,
  DocumentNode,
  InMemoryCache,
  HttpLink,from
} from "@apollo/client";
import { setContext } from "@apollo/client/link/context";
import { onError } from "@apollo/client/link/error";
import fetch from "cross-fetch";
import Cookies from "js-cookie";
// import { useRouter } from "next/navigation";

// Create the HTTP link
const httpLink = new HttpLink({
  uri: process.env.NEXT_PUBLIC_API_URL_GRAPHQL,
  fetch,
});


// Middleware to attach headers dynamically


const authLink = setContext((_, { headers }) => {
  // Retrieve the token from cookies instead of localStorage
  // const token = sessionStorage.getItem("ms_access_token");
   const token =Cookies.get("token")
  const orgId = Cookies.get("orgId");
  const subOrgId = Cookies.get("subOrgId");
  const processId = Cookies.get("processId");

  const customHeaders: Record<string, string> = {
    ...headers,
    authorization: token ? `Bearer ${token}` : "",
  };

  // Only add org-related headers if they're all defined and non-empty
  if (orgId?.trim() && subOrgId?.trim() && processId?.trim()) {
    console.log('headers',orgId,subOrgId,processId)
    customHeaders.orgId = orgId;
    customHeaders.subOrgId = subOrgId;
    customHeaders.processId = processId;
  }

  return {
    headers: customHeaders,
  };
});
const errorLink = onError(({ graphQLErrors, networkError }) => {
  // const router =useRouter()
  if (graphQLErrors) {
    for (const err of graphQLErrors) {
      console.log('err--',err);
      
      // const code = err.extensions?.code;
      // console.log('code',err.code);
      // if(code=='401'){
      //   alert(`GraphQL error: ${err.message}`);
      //    Cookies.remove("token");
      //     window.location.href = "/";
      // }
      if ((err as any).code === 'FORBIDDEN') {
        showToast.error(err.message)
        window.location.href = "/process";
        // alert(`FORBIDDEN: ${err.message}`);
          // router.push('/process')
      }
      // switch (code) {
      //   case "NOT_FOUND":
      //     alert("Error 404: Resource not found.");
      //     break;
      //   case "401":
      //     alert("Session expired. Please login again.");
      //     // Optional: redirect to login or clear cookies
      //     Cookies.remove("token");
      //     window.location.href = "/login";
      //     break;
      //   case "FORBIDDEN":
      //     alert("You do not have permission to access this resource.");
      //     break;
      //   default:
      //     alert(`GraphQL error: ${err.message}`);
      //     Cookies.remove("token");
      //     window.location.href = "/";
      // }
    }
  }


  if (networkError) {
    console.error("Network error:", networkError);
    // alert("A network error occurred. Please try again.");
  }
});



const client = new ApolloClient({
  link: from([errorLink, authLink, httpLink]), // Apply error -> auth -> http
  cache: new InMemoryCache(),
  connectToDevTools: process.env.NEXT_PUBLIC_ENV !== "development",
});


export default client;

export const GraphqlInstance = {
  async query(gql: DocumentNode, payload: any) {
    try {
      const response = await client.query({
        query: gql,
        variables: payload,
        fetchPolicy: "network-only",
      });
      // alert('asdfads')
      console.log('response',response);
      
      return response;
    } catch (error) {
      console.error(error);
      console.log('response',error);
      //  alert(
      //   'asdfds'
      // );
      throw error;
    }
  },
  async mutation(gql: DocumentNode, payload: any) {
    try {
      const response = await client.mutate({
        mutation: gql,
        variables: payload,
        fetchPolicy: "network-only",
      });
      //  alert('asdfads')
      return response;
    } catch (error) {
      console.error(error);
      // alert(
      //   'asdfds'
      // );
      
      throw error;
    }
  },
};
