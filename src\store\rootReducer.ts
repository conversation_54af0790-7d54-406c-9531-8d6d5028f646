// src/store/rootReducer.ts
import { combineReducers } from '@reduxjs/toolkit';
import userReducer from '../features/users/userSlice';
import {clientReducer, orgListReducer} from '../features/client/clientSlice';
import {clientByIdReducer} from '../features/client/clientByIdSlice';
import {sideNavReducer} from '../features/client/clientSlice';
import { headerProcessReducer, headersDefaultReducer, headersReducer, processReducer } from '@/features/headers/headersSlice';
import { titleReducer } from '@/features/client/titleSlice';
import { exportIdReducer, printReducer } from '@/features/export/exportSlice';
import { loaderReducer } from '@/features/headers/loaderSlice';


const rootReducer = combineReducers({
  user: userReducer,
  client:clientReducer,
  sideNav:sideNavReducer,
  orgList:orgListReducer,
  clientById:clientByIdReducer,
  headers:headersReducer,
  headersDefault:headersDefaultReducer,
  title:titleReducer,
  roleProcess:headerProcessReducer,
  process: processReducer,
  exportId: exportIdReducer,
  headerProcess:headerProcessReducer,
  loader:loaderReducer,
  print: printReducer
});

export type RootState = ReturnType<typeof rootReducer>;
export default rootReducer;
