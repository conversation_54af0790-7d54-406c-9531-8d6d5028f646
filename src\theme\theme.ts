import { createTheme } from '@mui/material/styles';

const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2', // Your generic primary color
      contrastText: '#fff',
    },
    secondary: {
      main: '#9c27b0', // Generic secondary color
      contrastText: '#fff',
    },
    error: {
      main: '#f44336',
    },
    warning: {
      main: '#ff9800',
    },
    info: {
      main: '#2196f3',
    },
    success: {
      main: '#4caf50',
    },
    background: {
      // default: '#f5f5f5',
      paper: '#ffffff',
    },
    text: {
      primary: '#000000',
      secondary: '#555555',
    },
  },
  typography: {
    fontFamily: 'Roboto, sans-serif',
  },
  components: {
    MuiFormHelperText: {
        styleOverrides: {
          root: {
            margin: 0,
          },
        },
      },
      MuiButton: {
        styleOverrides: {
          root: {
            marginTop: '24px',
            padding: '12px 0',
            fontSize: "18px",
            fontWeight: "bold",
            height: "50px",
            background: "rgb(3 105 161)",
            color: "white",
            borderRadius: "6px",
            textTransform: "none",
            boxShadow: "0px 4px 10px rgba(255, 81, 47, 0.3)",
            "&:hover": {
              background: "rgb(2 132 199)",
            },
          },
        },
      },
  }
});
export default theme;
