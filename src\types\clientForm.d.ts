/* eslint-disable @typescript-eslint/no-explicit-any */
export interface Form {
  id: string;
  name: string;
  sections: Section[];
}

export interface Section {
  id: string;
  name: string;
  fields: Field[];
}

export type FieldType = 
  | 'text'
  | 'email'
  | 'phone'
  | 'date'
  | 'select'
  | 'number'
  | 'grid';

export interface Field {
  value?: string;
  name: string | undefined;
  id: string;
  label: string;
  field_type: FieldType;
  logic_rules: any[];
  placeholder?: string;
  required?: boolean;
  filter?: boolean;
  options?: Option[];
  columns?: GridColumn[];
  rows?: any[];
}

export interface Option {
  id: string;
  value: string;
}

export interface GridColumn {
  name: string;
  fieldType: FieldType;
  id: string;
}