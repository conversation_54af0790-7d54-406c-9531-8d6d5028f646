// TypeScript type definitions

export interface InputFieldProps {
  label: string;
  name: string;
  type: string;
  placeholder: string;
  disabled: boolean;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur: (e: React.ChangeEvent<HTMLInputElement>) => void;
  error: boolean;
  helperText: string;
}

export interface NavItemProps {
  id?: string;
  icon?;
  label?: string;
  isActive: boolean;
  onClick: () => void;
}
export interface MFA {
  secret: string;
  period: string;
  digits: string;
}

export interface Input {
  email: string;
  otp?: string;
  skip2FACheck: boolean;
}

export interface Column {
  id: string;
  _id?: string;
  title: string;
  width?: string;
  hasFilter?: boolean;
  type: string;
  required?: boolean;
  label?: string;
  visible?: boolean;
  sortable?: boolean;
  filterType?: string;
  placeholder?: string;
  options?: string[];
}

interface TableColumnHeaderProps {
  column: Column;
  className?: string;
  selectedFilters: string[];
  onFilterChange?: (column: string, values: string[]) => void;
  onSort?: (column: string) => void;
  sortDirection?: "asc" | "desc" | null;
}
export interface TableConfig {
  columns: Column[];
  settings: {
    defaultSortColumn: string;
    defaultSortDirection: "asc" | "desc";
    rowsPerPage: number;
    selectable: boolean;
    filterable: boolean;
    responsive: boolean;
  };
}


export interface MasterData {
    id:string;
    icd:string;
    diagnosisCode:string;
    diagnosisDescription:string;
    cptCode:string;
    description:string;
    speciality:string;
    mueLimit:string;
    billingFrequency:string;
    unitCalculationRule:string;
}
export interface TableData {
  tableConfig: TableConfig;
  data: Record<string, string>[];
}

export interface TableDataMaster {
  tableConfig: TableConfig;
  data: MasterData[];
}
export type ColumnConfig = {
  
 id: string;
  title: string;
  type: string;
   width?: string;
  required: boolean;
  hasFilter?: boolean;
  placeholder: string;
  filterType: "text" | "select";
  visible: boolean;
  options?: string[];
};

// export type TableData = {
//   tableConfig: {
//     columns: ColumnConfig[];
//     settings: {
//       defaultSortColumn: string;
//       defaultSortDirection: "asc" | "desc";
//       rowsPerPage: number;
//       selectable: boolean;
//       filterable: boolean;
//       responsive: boolean;
//     };
//     data: Record<string, string>[];
//   };
// };