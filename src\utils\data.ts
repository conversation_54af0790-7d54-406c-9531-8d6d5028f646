// Provider Credentialing Data
import dashboard from '../assests/dashboardIcon.png'
import tickets from '../assests/tickets.png'
import userManagement from '../assests/userManagementIcon.png'
import reports from '../assests/reports.png'
import client from '../assests/clientIcon.png'
import masters from '../assests/master.png'
import settingsIcon from '../assests/settingsIcon.png'
export interface TicketData {
    priority: {
      level: 'Low' | 'Medium' | 'High';
      icon: string;
    };
    ticketId: string;
    groupType: 'Group' | 'Individual';
    providerName: string;
    payer: string;
    location: string;
    npiId: string;
    taxId: string;
    statusCode: string;
    binStatus: string;
    receivedDate: string;
    requestBy: {
      name: string;
      date: string;
    };
    workedDate: {
      name: string;
      date: string;
    };
    action: {
      type: string;
      color: string;
    };
  }

 export const moduleNameToPathMap: Record<string, string> = {
  "Dashboard": "/dashboard",
  "Tickets": "/tickets",
  "Reports": "/reports",
  "User Management": "/user-management",
   "Organizations": "/organizations",
  "Masters": "/masters/payer",
  "Settings": "/settings",
};

    export const navItems = [
{
        id: "dashboard",
        label: "Dashboard",
        icon:dashboard
      },
      {
        id: "tickets",
        label: "Tickets",
        icon: tickets
      },
      {
        id: "user-management",
        label: "User Management",
        icon: userManagement
      },
      {
        id: "reports",
        label: "Reports",
        icon: reports
      },
       {
        id: "organizations",
        label: "Organizations",
        icon: client
      },
      {
        id: "masters",
        label: "Masters",
        icon: masters
      },
      {
        id: "settings",
        label: "Settings",
        icon: settingsIcon
      }
    ];
  // data/mockData.ts
export const rows = [
  {
    id: 1,
    priority: 'Low',
    ticketId: 147895,
    group: 'Group',
    providerName: 'Amaya',
    payer: 'LIC',
    location: 'Andalusia',
    npiId: '**********',
    taxId: '87-3866139',
    statusCode: '257436',
    binStatus: 'New',
    action: 'Submitted',
    receivedDate: '15-12-2022',
    requestBy: 'Johnwick',
    workedDate: '16-12-2022',
  },
  // Add other rows similarly...
];

export const columns = [
  { field: 'priority', headerName: 'Priority', width: 100 },
  { field: 'ticketId', headerName: 'Ticket ID', width: 100 },
  { field: 'group', headerName: 'Group / Individual', width: 150 },
  { field: 'providerName', headerName: 'Provider Name', width: 150 },
  { field: 'payer', headerName: 'Payer (P/S)', width: 120 },
  { field: 'location', headerName: 'Location', width: 130 },
  { field: 'npiId', headerName: 'NPI ID', width: 130 },
  { field: 'taxId', headerName: 'TAX ID', width: 130 },
  { field: 'statusCode', headerName: 'Status Code', width: 120 },
  { field: 'binStatus', headerName: 'Bin Status', width: 130 },
  { field: 'action', headerName: 'Action', width: 120 },
  { field: 'receivedDate', headerName: 'Received Date', width: 130 },
  { field: 'requestBy', headerName: 'Request by', width: 120 },
  { field: 'workedDate', headerName: 'Worked Date', width: 130 },
];

  
  export const ticketsData: TicketData[] = [
    {
      priority: { level: 'Low', icon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/80a042a8b5463e3e1225663504d9dff7bd130398?placeholderIfAbsent=true&apiKey=302dce02927d48f0b85efbe3f3581c2b' },
      ticketId: '147895',
      groupType: 'Group',
      providerName: 'Amaya',
      payer: 'LIC',
      location: 'Andalusia',
      npiId: '18011774123',
      taxId: '87-3865139',
      statusCode: 'New',
      binStatus: 'Submitted',
      receivedDate: '15-12-2022',
      requestBy: { name: 'Johnwick', date: '16-12-2022' },
      workedDate: { name: 'Flemings', date: '12-12-2022' },
      action: { type: 'Allocate', color: 'purple-600' }
    },
    {
      priority: { level: 'Medium', icon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/7d17938161eca82dea74d64dd2b7155710447d81?placeholderIfAbsent=true&apiKey=302dce02927d48f0b85efbe3f3581c2b' },
      ticketId: '235476',
      groupType: 'Individual',
      providerName: 'Allocated',
      payer: 'TATA',
      location: 'Columbus',
      npiId: '18011774123',
      taxId: '87-3867897',
      statusCode: 'Allocated',
      binStatus: 'Follow Up',
      receivedDate: '14-12-2022',
      requestBy: { name: 'Amaya', date: '13-12-2022' },
      workedDate: { name: 'Atlas', date: '18-12-2022' },
      action: { type: 'Re Allocate', color: 'red-400' }
    },
    {
      priority: { level: 'High', icon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/7d17938161eca82dea74d64dd2b7155710447d81?placeholderIfAbsent=true&apiKey=302dce02927d48f0b85efbe3f3581c2b' },
      ticketId: '457896',
      groupType: 'Group',
      providerName: 'Inprogress',
      payer: 'ICICI',
      location: 'Washington',
      npiId: '**********',
      taxId: '87-3861478',
      statusCode: 'Inprogress',
      binStatus: 'Pending',
      receivedDate: '13-12-2022',
      requestBy: { name: 'Johnwick', date: '12-12-2022' },
      workedDate: { name: 'Johnwick', date: '16-12-2022' },
      action: { type: 'Re Allocate', color: 'red-400' }
    },
    {
      priority: { level: 'High', icon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/7d17938161eca82dea74d64dd2b7155710447d81?placeholderIfAbsent=true&apiKey=302dce02927d48f0b85efbe3f3581c2b' },
      ticketId: '125489',
      groupType: 'Individual',
      providerName: 'Inprogress',
      payer: 'New India',
      location: 'Clanton',
      npiId: '12458117204',
      taxId: '87-3862365',
      statusCode: 'Exceptions',
      binStatus: 'Follow Up',
      receivedDate: '14-12-2022',
      requestBy: { name: 'Flemings', date: '15-12-2022' },
      workedDate: { name: 'Amaya', date: '13-12-2022' },
      action: { type: 'Handle Exceptions', color: 'violet-400' }
    },
    {
      priority: { level: 'Medium', icon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/7d17938161eca82dea74d64dd2b7155710447d81?placeholderIfAbsent=true&apiKey=302dce02927d48f0b85efbe3f3581c2b' },
      ticketId: '32658',
      groupType: 'Group',
      providerName: 'Completed',
      payer: 'Medicare',
      location: 'San Francisco',
      npiId: '**********',
      taxId: '96-968574',
      statusCode: 'QC Queue',
      binStatus: 'Pending',
      receivedDate: '18-12-2022',
      requestBy: { name: 'Atlas', date: '19-12-2022' },
      workedDate: { name: 'Johnwick', date: '12-12-2022' },
      action: { type: 'Allocate to QC', color: 'blue-400' }
    },
    {
      priority: { level: 'Low', icon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/80a042a8b5463e3e1225663504d9dff7bd130398?placeholderIfAbsent=true&apiKey=302dce02927d48f0b85efbe3f3581c2b' },
      ticketId: '215463',
      groupType: 'Individual',
      providerName: 'Rework',
      payer: 'ICICI',
      location: 'Fort Payne',
      npiId: '18011774123',
      taxId: '96-963582',
      statusCode: 'In Completed',
      binStatus: 'Pending',
      receivedDate: '20-12-2022',
      requestBy: { name: 'Rambo', date: '22-12-2022' },
      workedDate: { name: 'Flemings', date: '15-12-2022' },
      action: { type: 'Allocate', color: 'purple-600' }
    },
    {
      priority: { level: 'Medium', icon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/7d17938161eca82dea74d64dd2b7155710447d81?placeholderIfAbsent=true&apiKey=302dce02927d48f0b85efbe3f3581c2b' },
      ticketId: '258741',
      groupType: 'Group',
      providerName: 'Exceptions',
      payer: 'New India',
      location: 'Colorado',
      npiId: '18011774123',
      taxId: '96-968574',
      statusCode: 'Rework',
      binStatus: 'Follow Up',
      receivedDate: '20-12-2022',
      requestBy: { name: 'Amaya', date: '20-12-2022' },
      workedDate: { name: 'Atlas', date: '19-12-2022' },
      action: { type: 'Re Allocate', color: 'red-400' }
    },
    {
      priority: { level: 'Low', icon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/7d17938161eca82dea74d64dd2b7155710447d81?placeholderIfAbsent=true&apiKey=302dce02927d48f0b85efbe3f3581c2b' },
      ticketId: '147895',
      groupType: 'Individual',
      providerName: 'QC Completed',
      payer: 'ICICI',
      location: 'Chicago',
      npiId: '18011774147',
      taxId: '96-258741',
      statusCode: 'QC Allocated',
      binStatus: 'Submitted',
      receivedDate: '12-12-2022',
      requestBy: { name: 'Johnwick', date: '24-12-2022' },
      workedDate: { name: 'Process', date: '' },
      action: { type: 'Re Allocate', color: 'red-400' }
    },
    {
      priority: { level: 'Medium', icon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/7d17938161eca82dea74d64dd2b7155710447d81?placeholderIfAbsent=true&apiKey=302dce02927d48f0b85efbe3f3581c2b' },
      ticketId: '235476',
      groupType: 'Group',
      providerName: 'Completed',
      payer: 'ICICI',
      location: 'Phoenix',
      npiId: '**********',
      taxId: '87-3862365',
      statusCode: 'QC Inprogress',
      binStatus: 'Submitted',
      receivedDate: '11-12-2022',
      requestBy: { name: 'Flemings', date: '12-12-2022' },
      workedDate: { name: 'Johnwick', date: '' },
      action: { type: 'Allocate', color: 'purple-600' }
    },
    {
      priority: { level: 'High', icon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/7d17938161eca82dea74d64dd2b7155710447d81?placeholderIfAbsent=true&apiKey=302dce02927d48f0b85efbe3f3581c2b' },
      ticketId: '457896',
      groupType: 'Individual',
      providerName: 'Exceptions',
      payer: 'TATA',
      location: 'San Jose',
      npiId: '**********',
      taxId: '87-3865139',
      statusCode: 'QC Incomplete',
      binStatus: 'Follow Up',
      receivedDate: '14-12-2022',
      requestBy: { name: 'Atlas', date: '18-12-2022' },
      workedDate: { name: 'Flemings', date: '15-12-2022' },
      action: { type: 'Re Allocate', color: 'red-400' }
    },
    {
      priority: { level: 'Medium', icon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/80a042a8b5463e3e1225663504d9dff7bd130398?placeholderIfAbsent=true&apiKey=302dce02927d48f0b85efbe3f3581c2b' },
      ticketId: '147895',
      groupType: 'Group',
      providerName: 'Amaya',
      payer: 'LIC',
      location: 'Andalusia',
      npiId: '**********',
      taxId: '87-384568',
      statusCode: 'QC Rework',
      binStatus: 'Pending',
      receivedDate: '15-12-2022',
      requestBy: { name: 'Johnwick', date: '16-12-2022' },
      workedDate: { name: 'Johnwick', date: '' },
      action: { type: 'Allocate', color: 'purple-600' }
    },
    {
      priority: { level: 'High', icon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/7d17938161eca82dea74d64dd2b7155710447d81?placeholderIfAbsent=true&apiKey=302dce02927d48f0b85efbe3f3581c2b' },
      ticketId: '235476',
      groupType: 'Group',
      providerName: 'Allocated',
      payer: 'TATA',
      location: 'Andalusia',
      npiId: '**********',
      taxId: '96-968574',
      statusCode: 'Re QC Pending',
      binStatus: 'Pending',
      receivedDate: '14-12-2022',
      requestBy: { name: 'Amaya', date: '13-12-2022' },
      workedDate: { name: 'Amaya', date: '13-12-2022' },
      action: { type: 'Re Allocate', color: 'red-400' }
    },
    {
      priority: { level: 'High', icon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/7d17938161eca82dea74d64dd2b7155710447d81?placeholderIfAbsent=true&apiKey=302dce02927d48f0b85efbe3f3581c2b' },
      ticketId: '457896',
      groupType: 'Individual',
      providerName: 'Inprogress',
      payer: 'ICICI',
      location: 'Clanton',
      npiId: '**********',
      taxId: '87-3861254',
      statusCode: 'QC Pass',
      binStatus: 'Submitted',
      receivedDate: '13-12-2022',
      requestBy: { name: 'Johnwick', date: '12-12-2022' },
      workedDate: { name: 'Johnwick', date: '12-12-2022' },
      action: { type: 'Allocate', color: 'purple-600' }
    },
    {
      priority: { level: 'Low', icon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/7d17938161eca82dea74d64dd2b7155710447d81?placeholderIfAbsent=true&apiKey=302dce02927d48f0b85efbe3f3581c2b' },
      ticketId: '125489',
      groupType: 'Group',
      providerName: 'Inprogress',
      payer: 'New India',
      location: 'San Francisco',
      npiId: '**********',
      taxId: '87-3864789',
      statusCode: 'New',
      binStatus: 'Pending',
      receivedDate: '14-12-2022',
      requestBy: { name: 'Flemings', date: '15-12-2022' },
      workedDate: { name: 'Johnwick', date: '' },
      action: { type: 'Re Allocate', color: 'red-400' }
    },
    {
      priority: { level: 'Medium', icon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/7d17938161eca82dea74d64dd2b7155710447d81?placeholderIfAbsent=true&apiKey=302dce02927d48f0b85efbe3f3581c2b' },
      ticketId: '32658',
      groupType: 'Group',
      providerName: 'Completed',
      payer: 'Medicare',
      location: 'Fort Payne',
      npiId: '-',
      taxId: '-',
      statusCode: 'Follow Up',
      binStatus: 'Submitted',
      receivedDate: '18-12-2022',
      requestBy: { name: 'Atlas', date: '19-12-2022' },
      workedDate: { name: '-', date: '' },
      action: { type: 'Allocate', color: 'purple-600' }
    },
    {
      priority: { level: 'Low', icon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/80a042a8b5463e3e1225663504d9dff7bd130398?placeholderIfAbsent=true&apiKey=302dce02927d48f0b85efbe3f3581c2b' },
      ticketId: '147895',
      groupType: 'Individual',
      providerName: 'Amaya',
      payer: 'LIC',
      location: 'Colorado',
      npiId: '-',
      taxId: '-',
      statusCode: 'New',
      binStatus: '-',
      receivedDate: '15-12-2022',
      requestBy: { name: 'Johnwick', date: '-' },
      workedDate: { name: '-', date: '' },
      action: { type: 'Process', color: 'orange-300' }
    }
  ];
  
  export const filterOptions = {
    actions: ['Actions'],
    perPage: [16, 32, 64, 128],
    dateRange: 'Now - 2'
  };
  
  export const navigationItems = [
    { name: 'Dashboard', active: false, icon: '', width: '154px' },
    { name: 'Tickets', active: true, icon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/46435ebd61ef64a1f7420a11604dc58c12b4d92f?placeholderIfAbsent=true&apiKey=302dce02927d48f0b85efbe3f3581c2b', width: '154px' },
    { name: 'User Management', active: false, icon: '', width: '204px' },
    { name: 'Reports', active: false, icon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/1e47131164b66ce3ad2527ea73a57ea421831394?placeholderIfAbsent=true&apiKey=302dce02927d48f0b85efbe3f3581c2b', width: '154px' },
    { name: 'Client', active: false, icon: '', width: '154px' },
    { name: 'Masters', active: false, icon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/fa8b25fca599b77617169546d32b1a9e759265b7?placeholderIfAbsent=true&apiKey=302dce02927d48f0b85efbe3f3581c2b', width: '154px' },
    { name: 'Settings', active: false, icon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/5555132c8085901fed68c02d2780d2692d869651?placeholderIfAbsent=true&apiKey=302dce02927d48f0b85efbe3f3581c2b', width: '154px' }
  ];
  
  export const userInfo = {
    name: 'Johnwick',
    role: 'Admin'
  };
   export const options = ["CR", "Option 2", "Option 3"];
  export const options1 = ["TATA", "Option 2", "Option 3"];
 export  const options2 = ["Provider Credentialing", "Option 2", "Option 3"];
  export const tableHeaders = [
    'Priority',
    'Ticket ID',
    'Group / Individual',
    'Provider Name',
    'Payer (P/S)',
    'Location',
    'NPI ID',
    'TAX ID',
    'Status Code',
    'Bin Status',
    'Action',
    'Received Date',
    'Request by',
    'Worked Date',
    'Action'
  ];
  