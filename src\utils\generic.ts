import { getImagedUrl } from "@/api/file/file";
import { RootState, store } from "@/store";
import { differenceInHours, differenceInMinutes, format, isToday, isYesterday, parseISO } from "date-fns";
import { ResponseCookies } from "next/dist/compiled/@edge-runtime/cookies";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
/* eslint-disable @typescript-eslint/no-explicit-any */

export function cn(...classes: (string | boolean | null | undefined)[]) {
  return classes.filter(Boolean).join(' ')
}
// Helper functions

export const formatTime = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
    .toString()
    .padStart(2, '0');
  const secs = (seconds % 60).toString().padStart(2, '0');
  return `${mins}:${secs}`;
};

export const toCamelCase = (str: string) => {
  return str
    .replace(/[()]/g, '')                        // Remove parentheses only
    .replace(/[^a-zA-Z0-9]+(.)/g, (_, chr) => chr.toUpperCase()) 
    .replace(/^([A-Z])/, match => match.toLowerCase());
};
export const toNormalCase = (str: string) => {
  const acronyms = ["CPT", "ICD", "DOB", "NPI", "ID", "URL", "SSN"];
    str = str.replace(/_/g, " "); // Remove underscores
  str = str.replace(/^[a-z]/, (match) => match.toUpperCase());

  acronyms.forEach(acronym => {
    const pattern = new RegExp(`\\b${acronym}\\b`, 'gi');
    str = str.replace(pattern, acronym);
  });
  str = str.replace(/([a-z0-9])([A-Z])/g, "$1 $2");
  acronyms.forEach(acronym => {
    const pattern = new RegExp(`(${acronym})([A-Z][a-z])`, 'g');
    str = str.replace(pattern, '$1 $2');
  });

  return str.trim();
};




// export function convertFormJsonFromClients(clients: { values: string; _id: string }[]): TableData {
//   const columnsMap = new Map<string, any>();
//   const dataRows: Record<string, any>[] = [];


//   // console.log('1111111111111111111111111111111',clients)
//   clients.forEach((client) => {
//     try {
//       const parsed = JSON.parse(client.values);

//       // Extracting the basicInformation object
//       const basicInformation = parsed?.basicInformation;

//       if (!basicInformation) return; // No valid basicInformation, skip this client.

//       const row: Record<string, any> = { _id: client._id }; // 🔥 Store the _id

//       // Iterate over all fields inside basicInformation
//       Object.keys(basicInformation).forEach((key) => {
//         const fieldValue = basicInformation[key];

//         // Use camelCase for column ID
//         const id = camelCase(key);

//         // Create column if not already created
//         if (!columnsMap.has(id)) {
//           const col = {
//             id,
//             _id: client._id,
//             title: key,  // Use original key as column title
//             type: "text",  // Assuming all fields are text for simplicity
//             required: false,  // Default value for required
//             placeholder: "",  // No placeholder by default
//             filterType: "text",
//             visible: true,
//           };

//           columnsMap.set(id, col);
//         }

//         // Store the field value in the row
//         row[id] = fieldValue ?? "";
//       });

//       dataRows.push(row);
//     } catch (e) {
//       console.log('1111111111111111111111111111111',clients)

//       console.error("Failed to parse client.values:", client.values, e);
//     }
//   });

//   const columns = Array.from(columnsMap.values());

//   return {
//     tableConfig: {
//       columns,
//       settings: {
//         defaultSortColumn: columns.find((col) => col.type === "text")?.id || "",
//         defaultSortDirection: "asc",
//         rowsPerPage: 10,
//         selectable: true,
//         filterable: true,
//         responsive: true,
//       },
//     },
//     data: dataRows,
//   };
// }
export const transformedClients = (rawClients: any[]) => rawClients.map((client) => {
  const { _id, ...rest } = client;
  return {
    _id,
    values: JSON.stringify(rest)
  };
});

export function formatDateToDDMMYYYY(dateInput: string | number | Date, key?: string): string {
  console.log('key', key, dateInput);

  // Handle undefined, null, or empty string directly
  if (!dateInput || (typeof dateInput === 'string' && dateInput.trim() === '')) {
    return '-';
  }

  const date = new Date(dateInput);

  if (!isNaN(date.getTime())) {
    return date
      .toLocaleDateString('en-GB')
      .split('/')
      .join('-'); // dd-mm-yyyy
  }

  // If it's a non-empty string but not a valid date, return it as-is
  if (typeof dateInput === 'string') {
    return dateInput;
  }

  return '-';
}
export const isDynamicSectionValid = (data: Record<string, any>): boolean => {
  if (!data || typeof data !== "object" || Object.keys(data).length === 0) {
    return false; 
  }

  return Object.values(data).every((section) => {
    if (!section || typeof section !== "object") return false;

    return Object.values(section).every(
      (value) =>
        value !== null &&
        value !== undefined &&
        value.toString().trim() !== ""
    );
  });
};
export function convertFormJsonFromClients(clients: { values: string; _id: string }[]): MastersTableData {
  const state: RootState = store.getState();
  const titles = state.headers;
  console.log('titles',titles);

  const columnsMap = new Map<string, any>();
  const dataRows: Record<string, any>[] = [];

  const createColumn = (id: string, title: string) => {
    if (!columnsMap.has(id)) {
      columnsMap.set(id, {
        id,
        _id: id,
        title,
        type: 'text',
        required: false,
        placeholder: '',
        filterType: 'text',
        visible: true,
      });
    }
  };

  // 1. Add titles from Redux state (for column config)
  if (titles?.length > 0) {
    titles.forEach((title) => {
      const id = camelCase(title);
      createColumn(id, title);
    });
  }

  // 2. Always parse clients to generate rows
  clients.forEach((client) => {
    try {
      const parsed = JSON.parse(client?.values);
      const row: Record<string, any> = { _id: client?._id };

      const processEntry = (prefix: string, value: any) => {
        if (Array.isArray(value)) return;


        if (typeof value === 'object' && value !== null) {
          if ('value' in value && typeof value.value === 'string') {
            const colId = camelCase(prefix);
            if (titles?.length === 0) createColumn(colId, prefix);
            row[colId] = value.value;
          } else if (Object.keys(value).length === 0) {
            const colId = camelCase(prefix);
            if (titles?.length === 0) createColumn(colId, prefix);
            row[colId] = 'N/A';
          }
          else {
            Object.entries(value).forEach(([k, v]) => {
              processEntry(k, v);
            });
          }
        } else {
          const colId = camelCase(prefix);
          if (titles?.length === 0) createColumn(colId, prefix);

          if (colId === 'updatedAt') {
            row[colId] = formatDateToDDMMYYYY(value)
          }
          else if (colId=='createdAt') {
            row[colId] = formatDateToDDMMYYYY(value)
          }
           else if (colId=='isActive') {
            row[colId] = value?"Active":"In-Active"
          }


          else { row[colId] = value; }

        }
      };

      Object.entries(parsed).forEach(([key, value]) => {
        processEntry(key, value);
      });

      dataRows.push(row);
    } catch (e) {
      console.error('Failed to parse client.values:', client.values, e);
    }
  });

  const columns = Array.from(columnsMap.values());

  return {
    tableConfig: {
      columns,
      settings: {
        defaultSortColumn: columns.find((col) => col.type === 'text')?.id || '',
        defaultSortDirection: 'asc',
        rowsPerPage: 10,
        selectable: true,
        filterable: true,
        responsive: true,
      },
    },
    data: dataRows,
  };
}
type MastersData = {
  _id: string;
  code: string;
  processId: {
    _id: string;
    name: string;
    __v: number;
  };
  __v: number;
};

type MastersTableData = {
  tableConfig: {
    columns: {
      id: string;
      _id: string;
      title: string;
      type: string;
      required: boolean;
      placeholder: string;
      filterType: string;
      visible: boolean;
    }[];
    settings: {
      defaultSortColumn: string;
      defaultSortDirection: "asc" | "desc";
      rowsPerPage: number;
      selectable: boolean;
      filterable: boolean;
      responsive: boolean;
    };
  };
  data: Record<string, any>[];
};

export function convertFormJsonFromMasters(clients: MastersData[]): MastersTableData {
  const columnsMap = new Map<string, any>();
  const dataRows: Record<string, any>[] = [];

  clients.forEach((client) => {
    const row: Record<string, any> = { _id: client._id };

    Object.entries(client).forEach(([key, value]) => {
      if (key === "_id") return;

      const id = camelCase(key);

      if (!columnsMap.has(id)) {
        columnsMap.set(id, {
          id,
          _id: client._id,
          title: key,
          type: "text",
          required: false,
          placeholder: "",
          filterType: "text",
          visible: true,
        });
      }

      // If value is an object (like processId), extract its name or stringify it
      if (typeof value === "object" && value !== null) {
        row[id] = value.name || JSON.stringify(value);
      } else {
        row[id] = value ?? "";
      }
    });

    dataRows.push(row);
  });

  const columns = Array.from(columnsMap.values());

  return {
    tableConfig: {
      columns,
      settings: {
        defaultSortColumn: columns.find((col) => col.type === "text")?.id || "",
        defaultSortDirection: "asc",
        rowsPerPage: 10,
        selectable: true,
        filterable: true,
        responsive: true,
      },
    },
    data: dataRows,
  };
}
// camelCase helper
function camelCase(str: string) {
  return str
    .replace(/\s(.)/g, (_: any, group1: string) => group1.toUpperCase())
    .replace(/\s/g, "")
    .replace(/^(.)/, (_: any, group1: string) => group1.toLowerCase())
    .replace(/[^a-zA-Z0-9]/g, '');
}


export function extractMatchingKeys(
  obj: any[] | Record<string, any> | null,
  keysToExtract: string | string[],
  result: Record<string, any> = {}
): Record<string, any> {
  const keys = Array.isArray(keysToExtract) ? keysToExtract : [keysToExtract];

  const normalize = (str: string) => str.replace(/[^a-zA-Z0-9]/g, "").toLowerCase();
  const normalizedKeys = keys.map(normalize);

  if (Array.isArray(obj)) {
    obj.forEach((item) => extractMatchingKeys(item, keysToExtract, result));
  } else if (typeof obj === "object" && obj !== null) {
    for (const key in obj) {
      const normalizedKey = normalize(key);
      const index = normalizedKeys.indexOf(normalizedKey);

      if (index !== -1) {
        result[keys[index]] = obj[key]; // Use clean key from input array
      }

      extractMatchingKeys(obj[key], keysToExtract, result);
    }
  }

  return result;
}


 export const fetchImageUrl = async (filename: string) => {
    try {
      const res = await getImagedUrl({ filename });
      console.log(res.generateViewUrl.data.viewUrl);

      // Optional fetch check if you want:
      const responses = await fetch(res.generateViewUrl.data.viewUrl, {
        method: "GET",
        // headers: {
        //   "Content-Type": "image/png",
        // },
      });
      console.log(ResponseCookies,'response');
      // window.open(res.generateViewUrl.data.viewUrl, "_blank");
       try {
      // Your signed URL
      const signedUrl =responses.url

      // Extract filename from the path
      const urlObj = new URL(signedUrl);
      const pathname = urlObj.pathname;
      const filename = pathname.substring(pathname.lastIndexOf("/") + 1);

      // Fetch the file
      const response = await fetch(signedUrl);
      if (!response.ok) {
        throw new Error("Failed to fetch file from signed URL");
      }

      const blob = await response.blob();

      // Create download link
      const downloadUrl = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = downloadUrl;
      a.download = filename; // Dynamically from URL
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error("Error downloading file:", error);
      alert("Failed to download the file.");
    }
  }
  catch (error) {
      console.error("Error downloading file:", error);
      // alert("Failed to download the file.");
    }
  }

export const fetchPrint = async (filename: string) => {
    try {
      const res = await getImagedUrl({ filename });
      console.log(res.generateViewUrl.data.viewUrl);

      // Optional fetch check if you want:
      const responses = await fetch(res.generateViewUrl.data.viewUrl, {
        method: "GET",
        // headers: {
        //   "Content-Type": "image/png",
        // },
      });
      console.log(responses,'response');
      // window.open(res.generateViewUrl.data.viewUrl, "_blank");
       try {
      // Your signed URL
      const signedUrl =responses.url

      const response = await fetch(signedUrl);
      console.log('response',response.url);
       const data = await fetch(res.generateViewUrl.data.viewUrl, {
        method: "GET",
        // headers: {
        //   "Content-Type": "image/png",
        // },
      });
      return data
//  if (response.url) {
//     localStorage.setItem('printUrl', response.url);
//     window.open('/print', '_blank');
//   }
     
      } catch (error) {
      console.error("Error downloading file:", error);
      alert("Failed to download the file.");
    }
    }
  catch (error) {
      console.error("Error downloading file:", error);
      // alert("Failed to download the file.");
    }
}
  
 export function formatDate(timestamp:string) {
  const date = new Date(timestamp);
  const options:any = {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  };
  // get something like: 07/10/2025, 09:22 AM
  const formatted = date.toLocaleString('en-US', options);

  // Replace / with -
  return formatted.replace(/\//g, '-').replace(',', '');
}

export const handlePrint = () => {
    const printContents = document.getElementById('print-section')?.innerHTML;
    const printWindow = window.open('', '_blank');

    if (printWindow && printContents) {
      printWindow.document.write(`
        <html>
        
          <head>
            <title>Client List</title>
            <style>
              body { font-family: sans-serif; padding: 20px; }
              table { width: 100%; border-collapse: collapse; }
              th, td { border: 1px solid #ddd; padding: 8px; }
              th { background-color: #f2f2f2; }
            </style>
          </head>
          <body>
            ${printContents}
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
  };

 export function getOrganizationSubModules(modules: any) {
   const process: RootState = store.getState();
  const headerProcess = process.headerProcess;

  const permissions = headerProcess?.role?.permissions || [];

  const orgModule = permissions.find(
    (mod: any) => mod.moduleName ===modules
  );

  if (!orgModule || !orgModule.subModules) return [];

  return orgModule.subModules;
}

export function getSubModulePermission( subModuleName: string, permissionName: string) {
  const process: RootState = store.getState();
  const headerProcess = process.headerProcess;
  const orgPermissions = headerProcess?.role?.permissions?.find(
    (mod: any) => mod.moduleName === "Organizations"
  )?.subModules || [];

  const subModule = orgPermissions.find(
    (sub: any) => sub.moduleName === subModuleName
  );

  if (!subModule || !subModule.permissions) return null;

  return subModule.permissions.find(
    (perm: any) => perm.displayName === permissionName
  ) || null;
}

export function getSubModulePermissionCommon(moduleName:string, subModuleName: string, permissionName: string) {
  const process: RootState = store.getState();
  const headerProcess = process.headerProcess;
  const orgPermissions = headerProcess?.role?.permissions?.find(
    (mod: any) => mod.moduleName === moduleName
  )?.subModules || [];

  const subModule = orgPermissions.find(
    (sub: any) => sub.moduleName === subModuleName
  );

  if (!subModule || !subModule.permissions) return null;

  return subModule.permissions.find(
    (perm: any) => perm.displayName === permissionName
  ) || null;
}

export function getprovidermodulePermissionCommon(moduleName: string, subModuleName: string) {
  const process: RootState = store.getState();
  const headerProcess = process.headerProcess;
  const orgPermissions =
    headerProcess?.role?.permissions?.find(
      (mod: any) => mod.moduleName === moduleName
    )?.subModules || [];

  // Find submodule by moduleName or displayName (case-insensitive)
  const subModule = orgPermissions.find(
    (sub: any) =>
      (sub.moduleName?.toLowerCase() === subModuleName.toLowerCase() ||
        sub.displayName?.toLowerCase() === subModuleName.toLowerCase()) &&
      sub.isEnabled // Only if submodule is enabled
  );

  if (!subModule || !subModule.permissions) return null;

  return subModule; // Return the whole submodule object if enabled
}

export function generateAuditTitle( userEmail:string, action:string, entityType:string ): string {
  const entity = entityType.toLowerCase();
  const user = userEmail || "Unknown User";

  switch (action) {
    case "CREATE":
      return `${user} created a ${entity}`;
    case "UPDATE":
      return `${user} updated a ${entity}`;
    case "DELETE":
      return `${user} deleted a ${entity}`;
    case "LOGIN":
      return `${user} logged into ${entity}`;
    case "LOGOUT":
      return `${user} logged out from ${entity}`;
    case "ROLE_ASSIGNMENT":
      return `${user} was assigned a role in ${entity}`;
    default:
      return `${user} performed ${action} on ${entity}`;
  }
}

export const formatRelativeTime = (timestamp: string) => {
  const date = parseISO(timestamp);
  const now = new Date();

  const mins = differenceInMinutes(now, date);
  const hrs = differenceInHours(now, date);

  if (mins < 1) return "Just now";
  if (mins === 1) return "1 min ago";
  if (mins < 60) return `${mins} mins ago`;
  if (hrs === 1) return "1 hr ago";
  if (hrs < 24 && isToday(date)) return `Today, ${format(date, "hh:mm a")}`;
  if (isYesterday(date)) return `Yesterday, ${format(date, "hh:mm a")}`;
  return format(date, "hh:mm a");
};

export function capitalizeAfterHyphen(text: string): string {
  if (!text.includes('-')) return capitalizeFirstLetter(text);

  const [before, after] = text.split('-').map(part => part.trim());
  return `${before} - ${capitalizeFirstLetter(after.toLowerCase())}`;
}


export function capitalizeFirstLetter(text: any): string {
  if (!text || typeof text !== 'string') return '';
  return text.charAt(0).toUpperCase() + text.slice(1);
}


export const downloadExcelWithHeaders = (headers: string[]) => {
  const worksheetData = 
    [headers] // sampleData: Array of arrays
 

  const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);
  const workbook = XLSX.utils.book_new();

  XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

  const excelBuffer = XLSX.write(workbook, { bookType: "xlsx", type: "array" });
  const data = new Blob([excelBuffer], { type: "application/octet-stream" });

  saveAs(data, "sample.xlsx");
};