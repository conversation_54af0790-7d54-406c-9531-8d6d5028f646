'use client';
import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Typography } from '@mui/material';

interface Permission {
  displayName: string;
  isEnabled: boolean;
}

interface SubModule {
  displayName: string;
  moduleName: string;
  isEnabled: boolean;
  permissions: Permission[];
}

interface IModule {
  moduleName: string;
  displayName: string;
  isEnabled: boolean;
  subModules: SubModule[];
}

interface PermissionGuardProps {
  children: React.ReactNode;
  moduleName: string;
  subModuleName?: string;
  permissionName?: string;
  permissions: IModule[];
  redirectTo?: string;
  fallback?: React.ReactNode;
}

interface PermissionWrapperProps {
  children: React.ReactNode;
  moduleName: string;
  subModuleName?: string;
   permissions: IModule[];
  permissionName?: string;
  fallback?: React.ReactNode;
}



// Optimized permission checking functions - permissions passed as parameter
export const hasModuleAccess = (permissions: IModule[], moduleName: string): boolean => {
  if (!permissions || !Array.isArray(permissions)) return false;

  const modules = permissions.find((item: IModule) =>
    item.moduleName.includes(moduleName)
  );
  return modules?.isEnabled || false;
};

export const hasSubModuleAccess = (permissions: IModule[], moduleName: string, subModuleName: string): boolean => {
  if (!permissions || !Array.isArray(permissions)) return false;

  const modules = permissions.find((item: IModule) =>
    item.moduleName.includes(moduleName)
  );

  if (!modules || !modules?.isEnabled) {
    return false;
  }

  const subModule = modules.subModules?.find((sub: SubModule) =>
    sub.moduleName === subModuleName
  );

  return subModule?.isEnabled || false;
};

export const hasPermission = (permissions: IModule[], moduleName: string, subModuleName: string, permissionName: string): boolean => {
  if (!permissions || !Array.isArray(permissions)) return false;

  if (!hasSubModuleAccess(permissions, moduleName, subModuleName)) {
    return false;
  }

  const modules = permissions.find((item: IModule) =>
    item.moduleName.includes(moduleName)
  );

  const subModule = modules?.subModules?.find((sub: SubModule) =>
    sub.moduleName === subModuleName
  );

  const permission = subModule?.permissions?.find((perm: Permission) =>
    perm.displayName === permissionName
  );

  return permission?.isEnabled || false;
};

// PermissionGuard Component - Protects entire pages/components
export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  moduleName,
  subModuleName,
  permissionName,
  permissions,
  redirectTo = '/access-denied',
  fallback
}) => {
  const router = useRouter();

  useEffect(() => {
    if (!permissions) return;

    // let hasAccess = false;

    // if (permissionName && subModuleName) {
    //   hasAccess = hasPermission(permissions, moduleName, subModuleName, permissionName);
    // } else if (subModuleName) {
    //   hasAccess = hasSubModuleAccess(permissions, moduleName, subModuleName);
    // } else {
    //   hasAccess = hasModuleAccess(permissions, moduleName);
    // }

    // if (!hasAccess) {
    //   router.push(redirectTo);
    // }
  }, [permissions, moduleName, subModuleName, permissionName, router, redirectTo]);

  // Check permissions for rendering
  if (!permissions) {
    return (
      <div className="py-4 space-y-8 bg-[white] !rounded-[16px] h-[100%] flex items-center justify-center">
        <Typography variant="h6" sx={{ color: "#2B3674", fontWeight: 600, fontSize: '16px' }}>
          Loading...
        </Typography>
      </div>
    );
  }

  let hasAccess = false;
  if (permissionName && subModuleName) {
    hasAccess = hasPermission(permissions, moduleName, subModuleName, permissionName);
  } else if (subModuleName) {
    hasAccess = hasSubModuleAccess(permissions, moduleName, subModuleName);
  } else {
    hasAccess = hasModuleAccess(permissions, moduleName);
  }

  // Show fallback or access denied message
  if (!hasAccess) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="py-4 space-y-8 bg-[white] !rounded-[16px] h-[100%] flex items-center justify-center">
        <Typography variant="h6" sx={{ color: "#2B3674", fontWeight: 600, fontSize: '16px' }}>
          {`Access Denied: You don't have permission to access this page.`}
        </Typography>      </div>
    );
  }

  return <>{children}</>;
};

// PermissionWrapper Component - Conditionally renders UI elements
export const PermissionWrapper: React.FC<PermissionWrapperProps> = ({
  children,
  moduleName,
  subModuleName,
  permissionName,
  permissions,
  fallback = null
}) => {
  // Check permissions
  if (!permissions) return null;

  let hasAccess = false;
  if (permissionName && subModuleName) {
    hasAccess = hasPermission(permissions, moduleName, subModuleName, permissionName);
  } else if (subModuleName) {
    hasAccess = hasSubModuleAccess(permissions, moduleName, subModuleName);
  } else {
    hasAccess = hasModuleAccess(permissions, moduleName);
  }

  // Return children if has access, otherwise return fallback
  return hasAccess ? <>{children}</> : <>{fallback}</>;
};
